"""
优化的配置管理器
统一管理所有算法和系统配置，支持动态加载和缓存
"""

import os
import yaml
import json
from typing import Dict, Any, Optional
from pathlib import Path
from functools import lru_cache
import logging

logger = logging.getLogger(__name__)

class ConfigManager:
    """统一配置管理器"""

    def __init__(self):
        self._configs: Dict[str, Any] = {}
        self._config_files: Dict[str, str] = {
            'cgcl': './pretrain_model_dict/cgcl_config.yaml',
            'egcl': './custom_config.yaml',
            'clip': './pretrain_model_dict/clip_config.yaml',
            'svm': './pretrain_model_dict/svm_config.yaml',
            'system': './config/system.yaml'
        }
        self._load_all_configs()

    def _load_all_configs(self):
        """加载所有配置文件"""
        for config_name, config_path in self._config_files.items():
            try:
                self._configs[config_name] = self._load_config_file(config_path)
                logger.info(f"配置加载成功: {config_name}")
            except Exception as e:
                logger.warning(f"配置加载失败 {config_name}: {e}")
                self._configs[config_name] = self._get_default_config(config_name)

    @lru_cache(maxsize=32)
    def _load_config_file(self, config_path: str) -> Dict[str, Any]:
        """缓存配置文件加载"""
        if not os.path.exists(config_path):
            return {}

        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                return yaml.safe_load(f)
            elif config_path.endswith('.json'):
                return json.load(f)
            else:
                return {}

    def _get_default_config(self, config_name: str) -> Dict[str, Any]:
        """获取默认配置"""
        defaults = {
            'cgcl': {
                'atom_fea_len': 64,
                'n_conv': 3,
                'h_fea_len': 128,
                'n_h': 1,
                'batch_size': 32
            },
            'egcl': {
                'neighbors': 12,
                'rcut': 3.0,
                'search_delta': 1.0,
                'n_classification': 7,
                'batch_size': 24
            },
            'clip': {
                'embedding_size': 384,
                'n_layers': 2,
                'n_heads': 4,
                'batch_size': 16
            },
            'svm': {
                'kernel': 'rbf',
                'C': 1.0,
                'gamma': 0.1,
                'probability': True
            },
            'system': {
                'max_concurrent_tasks': 5,
                'task_timeout': 3600,
                'cache_size': 1000,
                'cleanup_interval': 3600
            }
        }
        return defaults.get(config_name, {})

    def get_config(self, config_name: str, key: Optional[str] = None) -> Any:
        """获取配置值"""
        config = self._configs.get(config_name, {})
        if key:
            return config.get(key)
        return config

    def update_config(self, config_name: str, updates: Dict[str, Any]):
        """更新配置"""
        if config_name in self._configs:
            self._configs[config_name].update(updates)
            logger.info(f"配置更新: {config_name}")

    def reload_config(self, config_name: str):
        """重新加载指定配置"""
        if config_name in self._config_files:
            self._load_config_file.cache_clear()
            self._configs[config_name] = self._load_config_file(
                self._config_files[config_name]
            )
            logger.info(f"配置重新加载: {config_name}")

# 全局配置管理器实例
config_manager = ConfigManager()