from ase.io import read
from dscribe.descriptors import SOAP
from pymatgen.core.structure import Structure
import numpy as np
import os
from pymatgen.io.ase import AseAtomsAdaptor
from ase import Atoms
import pandas as pd

def generate_soap_descriptor(uploaded_file,
                             centers=None,
                             rcut=5.0,
                             nmax=8,
                             lmax=6,
                             sigma=0.3,
                             sparse=False,
                             average="inner",  # 默认使用平均化
                             **kwargs):
    try:
        # 读取结构文件
        structure = Structure.from_file(uploaded_file)
    except Exception as e:
        print(f"Failed to load CIF file: {e}")
        return None, None

    # 转换为 ASE Atoms 对象
    adaptor = AseAtomsAdaptor()
    ase_atoms = adaptor.get_atoms(structure)

    # 获取化学式
    formula = structure.formula
    species = list(set([str(site.specie.symbol) for site in structure]))
    try:
        # 创建 SOAP 描述符对象
        soap = SOAP(species=species,periodic=True, r_cut=rcut, n_max=nmax, l_max=lmax, sigma=sigma, sparse=sparse, average=average, **kwargs)
        # 计算描述符
        if centers:
            descriptors = soap.create(ase_atoms, centers=centers)
        else:
            descriptors = soap.create(ase_atoms)
    except Exception as e:
        print(f"Failed to create SOAP descriptors: {e}")
        return None, None

    return formula, descriptors