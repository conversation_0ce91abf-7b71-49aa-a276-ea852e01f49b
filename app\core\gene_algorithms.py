"""
材料基因算法核心模块 - API包装器
这个模块将原始的材料基因算法包装成适合API调用的形式
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Union, Tuple, Callable
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time
import re
from app.config import settings, algorithm_config
from app.services.file_handler import file_handler
from app.utils.exceptions import AlgorithmError
from app.core.cache_manager import cache_manager
from app.core.config_manager import config_manager

# 导入原始算法模块（需要确保这些模块在Python路径中）
try:
    from app.core.units_models import main_cgcl, main_egcl, main_clip, main_gene_edit,optimize_design_space, show_tsne_result, show_edit_3d,show_edit_2d, generate_optimization_gif, visualize_optimization
    print(1)
    from app.core.gene_wajue_soap import generate_soap_descriptor as soap
    print(2)
    from app.core.gene_wajue_afs import calculate_features_for_all_formulas as afs
    print(3)
except ImportError as e:
    logging.error(f"导入算法模块失败: {e}")
    # 在开发阶段可以创建模拟函数
    pass

logger = logging.getLogger(__name__)

class MaterialGeneAlgorithms:
    """材料基因算法包装器类"""
    
    def __init__(self):
        self.config = algorithm_config
        self.models_loaded = {}
        
    @cache_manager.cache_result("gene_mining", ttl=7200)  # 缓存2小时
    async def gene_mining(
        self,
        cif_file_ids: List[str],
        progress_callback: Optional[Callable] = None,
        metadata= None
    ) -> Dict[str, Any]:
        """
        材料基因挖掘
        
        Args:
            cif_file_ids: CIF文件ID列表
            progress_callback: 进度回调函数
            
        Returns:
            包含AFS和SOAP特征的字典
        """
        try:
            logger.info(f"开始基因挖掘，处理 {len(cif_file_ids)} 个文件")
            print("read_file")
            # 获取文件路径
            cif_files = []
            for file_id in cif_file_ids:
                print("read_first_file")
                file_path = await file_handler.get_file_path(file_id)
                if file_path and os.path.exists(file_path):
                    cif_files.append(file_path)
                else:
                    raise FileNotFoundError(f"文件不存在: {file_id}")
            
            if progress_callback:
                progress_callback(10.0)
            print(cif_files)
            # 提取SOAP特征和化学式
            formulas = []
            soap_features = []
            
            for i, cif_file in enumerate(cif_files):
                try:
                    formula, descriptors = soap(cif_file)
                    formulas.append(formula)
                    soap_features.append(descriptors)
                    
                    if progress_callback:
                        progress = 10.0 + (i + 1) / len(cif_files) * 60.0
                        progress_callback(progress)
                        
                except Exception as e:
                    logger.warning(f"处理文件 {cif_file} 时出错: {e}")
                    continue
            
            if progress_callback:
                progress_callback(70.0)
            
            # 计算AFS特征
            afs_features = None
            if formulas:
                formula_data = pd.DataFrame(data=formulas, columns=["Formula"])
                feature_csv = self.config.afs_feature_csv
                
                if os.path.exists(feature_csv):
                    afs_features =  afs(formula_data, feature_csv, method="reduce")
                else:
                    logger.warning(f"AFS特征文件不存在: {feature_csv}")
            
            if progress_callback:
                progress_callback(90.0)
     
            # 保存结果
            result = {
                "formulas": formulas,
                "soap_features": soap_features,
                "afs_features": afs_features.to_dict() if afs_features is not None else None,
                "processed_samples": len(formulas),
                "metadata": metadata or {},
                "feature_dimensions": {
                    "soap_dim": soap_features[0].shape if soap_features else None,
                    "afs_dim": afs_features.shape if afs_features is not None else None
                }
            }
         
            if progress_callback:
                progress_callback(100.0)
            print("gene_mining_result has successfully")
            logger.info(f"基因挖掘完成，处理了 {len(formulas)} 个样本")
            return result
            
        except Exception as e:
            logger.error(f"基因挖掘失败: {e}")
            raise AlgorithmError(f"基因挖掘失败: {e}")
    
    @cache_manager.cache_result("gene_identification", ttl=7200)  # 缓存2小时
    async def gene_identification(
        self,
        cif_file_ids: List[str],
        progress_callback: Optional[Callable] = None,
        metadata= None
    ) -> Dict[str, Any]:
        """
        基因识别
        
        Args:
            cif_file_ids: CIF文件ID列表
            progress_callback: 进度回调函数
            
        Returns:
            包含CGCL和EGCL特征的字典
        """
        try:
            logger.info(f"开始基因识别  in gene_algori，处理 {len(cif_file_ids)} 个文件")
            
            # 获取文件路径
            cif_files = []
            for file_id in cif_file_ids:
                file_path = await file_handler.get_file_path(file_id)
                if file_path and os.path.exists(file_path):
                    cif_files.append(file_path)
            
            if progress_callback:
                progress_callback(10.0)
            
            # 运行CGCL模型
            feature_from_cgcl = None
            try:
                feature_from_cgcl = main_cgcl(
                    cif_files, 
                    settings.atom_init_json,
                    settings.cgcl_model_path,
                    afs_features=None
                )
                if progress_callback:
                    progress_callback(50.0)
            except Exception as e:
                logger.error(f"CGCL模型执行失败: {e}")
           
            # 运行EGCL模型
            feature_from_egcl = None
            try:
                feature_from_egcl = main_egcl(
                    cif_files,
                    settings.custom_yaml,
                    settings.egcl_model_path,
                    soap_features=None
                )
                if progress_callback:
                    progress_callback(90.0)
            except Exception as e:
                logger.error(f"EGCL模型执行失败: {e}")
            
            result = {
                "cgcl_features": feature_from_cgcl,
                "egcl_features": feature_from_egcl,
                "feature_dimensions": {
                    "cgcl_dim": feature_from_cgcl.shape if feature_from_cgcl is not None else None,
                    "egcl_dim": feature_from_egcl.shape if feature_from_egcl is not None else None
                },
                "model_performance": {
                    "cgcl_status": "success" if feature_from_cgcl is not None else "failed",
                    "egcl_status": "success" if feature_from_egcl is not None else "failed"
                }
            }
            
            if progress_callback:
                progress_callback(100.0)
            
            logger.info("基因识别完成")
            return result
            
        except Exception as e:
            logger.error(f"基因识别失败: {e}")
            raise AlgorithmError(f"基因识别失败: {e}")
    
    async def gene_fusion(
        self,
        cif_file_ids: List[str],
        csv_file_id: str,
        progress_callback: Optional[Callable] = None,
        metadata= None
    ) -> Dict[str, Any]:
        """
        基因融合
        
        Args:
            cif_file_ids: CIF文件ID列表
            csv_file_id: CSV数据文件ID
            progress_callback: 进度回调函数
            
        Returns:
            融合特征结果
        """
        try:
            logger.info("开始基因融合")
            
            # 获取文件路径
            cif_files = []
            cif_files_server_local={}
            for file_id in cif_file_ids:
                file_path = await file_handler.get_file_path(file_id)
                file_info = await file_handler.get_file_info(file_id)
                cif_files_server_local[file_info['original_name']] = file_path
                if file_path:
                    cif_files.append(file_path)
            
            csv_file_path = await file_handler.get_file_path(csv_file_id)
            if not csv_file_path:
                raise FileNotFoundError(f"CSV文件不存在: {csv_file_id}")
            
            data_frame = pd.read_csv(csv_file_path)
            original_names = data_frame.iloc[:,0].str.strip().str.split('/').str[-1].tolist()
            matched_files = []
            matched_data = []
            matched_original_names = []
            matched_new_names = []
            for idx, original_name in enumerate(original_names):
                if original_name in cif_files_server_local:
                    matched_files.append(cif_files_server_local[original_name])
                    matched_original_names.append(original_name)
                    matched_data.append(data_frame.iloc[idx])
                    matched_new_names.append(cif_files_server_local[original_name])
            if not matched_files:
                raise ValueError("未找到任何 CIF 文件与 CSV 中的条目匹配。")
            data_frame_matched = pd.DataFrame(matched_data).reset_index(drop=True)
            data_frame_matched.iloc[:,0] = data_frame_matched.iloc[:,0].apply(
                lambda x: cif_files_server_local[os.path.basename(x)]
            )
            if progress_callback:
                progress_callback(20.0)
            final_feature,best_pred,ground_truth = main_clip(
                cif_files,
                data_frame_matched,
                settings.custom_yaml,
                settings.clip_model_path
            )
            logger.info(f"融合特征维度: {final_feature.shape if hasattr(final_feature, 'shape') else len(final_feature)}")
            if progress_callback:
                progress_callback(80.0)
            # print(best_pred)
            # print(ground_truth)
            # ✅ 清洗字段为 1维列表
            matched_ground_truth = [float(x.item()) if isinstance(x, np.generic) else float(x) for x in np.array(ground_truth).flatten()]
            matched_best_pred = [float(x.item()) if isinstance(x, np.generic) else float(x) for x in np.array(best_pred).flatten()]
            print(matched_ground_truth)
            print(matched_best_pred)
            print(matched_original_names)
            print(matched_new_names)
            result_df = pd.DataFrame({
            'original_name': matched_original_names,
            'new_name': matched_new_names,
            'ground_truth': matched_ground_truth,
            'best_pred': matched_best_pred
                })
            def calculate_metric(best_pred, ground_truth):
                """
                支持单样本和多样本，自动处理指标计算，避免异常。
                """
                best_pred = np.array(best_pred).flatten()
                ground_truth = np.array(ground_truth).flatten()

                metric_results = {}

                if len(ground_truth) < 2:
                    # 单样本处理
                    abs_error = abs(best_pred[0] - ground_truth[0])
                    rel_error = abs(best_pred[0] - ground_truth[0]) / (abs(ground_truth[0]) + 1e-8)
                    
                    metric_results['r2'] = None  # 单样本无法计算R2
                    metric_results['mae'] = abs_error
                    metric_results['mape'] = rel_error * 100  # 转成百分比
                    metric_results['n_samples'] = 1
                else:
                    # 多样本正常计算
                    from sklearn.metrics import r2_score, mean_absolute_error, mean_absolute_percentage_error
                    r2 = r2_score(ground_truth, best_pred)
                    mae = mean_absolute_error(ground_truth, best_pred)
                    mape = mean_absolute_percentage_error(ground_truth, best_pred) * 100  # 转成百分比
                    metric_results['r2'] = r2
                    metric_results['mae'] = mae
                    metric_results['mape'] = mape
                    metric_results['n_samples'] = len(ground_truth)

                return metric_results
            result = {
                "fused_features": final_feature,
                "fusion_dimension": final_feature.shape[1] if hasattr(final_feature, 'shape') else len(final_feature),
                "best_prediction": best_pred,
                "ground_truth": ground_truth,
                "metrics": calculate_metric(best_pred, ground_truth),
                "cif_files_server_local": cif_files_server_local,
                "final_result": result_df.to_dict(orient='records'),
            }
            
            if progress_callback:
                progress_callback(100.0)
            
            logger.info("基因融合完成")
            return result
            
        except Exception as e:
            logger.error(f"基因融合失败: {e}")
            raise AlgorithmError(f"基因融合失败: {e}")

    async def gene_editing(
        self,
        features_data_id: str,
        flag: Union[float, Tuple[float, float]],
        label_type: str,
        is_train: bool = True,
        model_path: Optional[str] = None,
        progress_callback: Optional[Callable] = None,
        metadata= None
    ) -> Dict[str, Any]:
        """
        基因编辑
        
        Args:
            features_data_id: 特征数据ID
            flag: 性能阈值或范围
            label_type: 标签类型
            is_train: 是否训练模式
            model_path: 模型路径
            progress_callback: 进度回调函数
            
        Returns:
            编辑结果
        """
        try:
            logger.info("开始基因编辑")
            
            #加载特征数据
            print(features_data_id)
            print(flag)
            print(label_type)
            print(is_train)
        
            final_features = await file_handler.load_result(features_data_id, "csv")
            if final_features is None:
                raise FileNotFoundError(f"特征数据不存在: {features_data_id}")
            
            if progress_callback:
                progress_callback(20.0)
            print("start gene_editing")
            # 运行基因编辑算法
            edit_model = main_gene_edit(
                final_features,
                flag,
                label_type,
                is_train,
                settings.svm_model_path
            )
            print(edit_model)
            if progress_callback:
                progress_callback(80.0)
            # if not is_train:
                    
            #     X = final_features.iloc[:, :384].values
            #     y_true=final_features.iloc[:, -1].values
            #     def clean_y_values(y):
            #         # 如果 y 是字符串加括号的格式（例如 '[6.54]'），去掉括号，提取数字
            #         return np.array([float(re.sub(r'[^\d.-]', '', str(val))) for val in y])
            #     def _calculate_labels(y, flag, label_type, ratio):
            #         """智能标签划分核心逻辑"""
            #         n_samples = len(y)
            #         n_select = int(n_samples * ratio)
            #         y=clean_y_values(y)
            #         if isinstance(flag, tuple):
            #             # 区间模式
            #             left, right = flag
            #             in_interval = (y >= left) & (y <= right)
            #             if not np.any(in_interval):  # 如果完全没有交集
            #                 # 计算所有点到区间中点的距离
            #                 mid = (left + right) / 2
            #                 distances = np.abs(y - mid)
            #                 closest_indices = np.argsort(distances)[:n_select]
            #                 labels = np.zeros_like(y, dtype=int)
            #                 labels[closest_indices] = 1
            #                 print(f"警告: y值不在flag区间内，已自动选择最近{ratio*100}%的样本作为正样本")
            #             else:
            #                 labels = in_interval.astype(int)
            #         else:
            #             # 单值模式
            #             if label_type == 'interval':
            #                 # 区间模式（flag±tol）
            #                 tol = abs(flag) * 0.05
            #                 in_interval = (y >= flag - tol) & (y <= flag + tol)
                            
            #                 if not np.any(in_interval):  # 无交集
            #                     distances = np.abs(y - flag)
            #                     closest_indices = np.argsort(distances)[:n_select]
            #                     labels = np.zeros_like(y, dtype=int)
            #                     labels[closest_indices] = 1
            #                     print(f"警告: y值不在flag附近，已自动选择最近{ratio*100}%的样本作为正样本")
            #                 else:
            #                     labels = in_interval.astype(int)
                                
            #             elif label_type == 'boundary_left':
            #                 # 左侧分界
            #                 if np.all(y > flag):  # 全部在右侧
            #                     # 选择最靠近flag的30%
            #                     distances = y - flag
            #                     closest_indices = np.argsort(distances)[:n_select]
            #                     labels = np.zeros_like(y, dtype=int)
            #                     labels[closest_indices] = 1
            #                     print(f"警告: 所有y值都在flag右侧，已自动选择最接近的{ratio*100}%作为正样本")
            #                 else:
            #                     labels = (y <= flag).astype(int)
                                
            #             elif label_type == 'boundary_right':
            #                 # 右侧分界
            #                 if np.all(y < flag):  # 全部在左侧
            #                     # 选择最靠近flag的30%
            #                     distances = flag - y
            #                     closest_indices = np.argsort(distances)[:n_select]
            #                     labels = np.zeros_like(y, dtype=int)
            #                     labels[closest_indices] = 1
            #                     print(f"警告: 所有y值都在flag左侧，已自动选择最接近的{ratio*100}%作为正样本")
            #                 else:
            #                     labels = (y >= flag).astype(int)
            #         return labels
            #     y_true_label= _calculate_labels(
            #         y_true, flag, label_type, 0.3
            #     )
            #     y_pred = edit_model.predict(X)
            #     # 计算准确率
            #     accuracy = np.mean(y_pred == y_true_label)
            #     logger.info(f"模型预测准确率: {accuracy * 100:.2f}%")

            
            result = {
                "model_id": f"svm_model_{features_data_id}",
                "features_data_id": features_data_id,
                #"classification_accuracy": accuracy if not is_train else None,
                "support_vectors": getattr(edit_model, 'n_support_', [0])[0] if hasattr(edit_model, 'n_support_') else 0,
                "model_parameters": {
                    "kernel": getattr(edit_model, 'kernel', 'rbf'),
                    "C": getattr(edit_model, 'C', 1.0),
                    "gamma": getattr(edit_model, 'gamma', 'scale')
                }
            }
            
            model_save_path = f"svm_model_{features_data_id}"
            await file_handler.save_result(model_save_path, edit_model, "pkl")
            
            if progress_callback:
                progress_callback(100.0)
            
            logger.info("基因编辑完成")
            return result
            
        except Exception as e:
            logger.error(f"基因编辑失败: {e}")
            raise AlgorithmError(f"基因编辑失败: {e}")
    
    async def gene_design(
        self,
        svc_model_id: str,
        search_features_id: str,
        n_calls: int = 200,
        target_value: float = 12.04,
        progress_callback: Optional[Callable] = None,
        metadata= None
    ) -> Dict[str, Any]:
        """
        基因设计
        
        Args:
            svc_model_id: SVM模型ID
            search_features_id: 搜索特征ID
            n_calls: 优化次数
            progress_callback: 进度回调函数
            
        Returns:
            设计优化结果
        """
        try:
            logger.info(f"开始基因设计，优化次数: {n_calls}")
            
            # 加载SVM模型和搜索特征
            svc_model = await file_handler.load_result(svc_model_id, "pkl")
            search_features = await file_handler.load_result(search_features_id, "csv")
            
            if svc_model is None:
                raise FileNotFoundError(f"SVM模型不存在: {svc_model_id}")
            if search_features is None:
                raise FileNotFoundError(f"搜索特征不存在: {search_features_id}")
            
            if progress_callback:
                progress_callback(10.0)
            
            # 运行贝叶斯优化
            design_result = optimize_design_space(
                svc_model_path=None,
                proxy_model_path=settings.clip_model_path,
                config_yaml=settings.custom_yaml,
                n_calls=n_calls,
                search_features=search_features,
                svc_model=svc_model,
                target_value=target_value,
            )
            print("design success")
            if progress_callback:
                progress_callback(90.0)
            
            # 处理优化结果
            result = {
                "optimal_value": design_result.get("optim_value", 0.0),
                "optimal_parameters": design_result.get("optim_params", []),
                "convergence_iterations": len(design_result.get("history", {}).get("values", [])),
                "optimization_history": design_result.get("history", {}),
                "class_1_points": design_result.get("class_1_points", []),
                "result": design_result.get("result", []),
                "target_value": target_value,
            }
            
            if progress_callback:
                progress_callback(100.0)
            
            logger.info("基因设计完成")
            return result
            
        except Exception as e:
            logger.error(f"基因设计失败: {e}")
            raise AlgorithmError(f"基因设计失败: {e}")
    
    async def run_full_pipeline(
        self,
        cif_file_ids: List[str],
        csv_file_id: str,
        parameters: Dict[str, Any],
        progress_callback: Optional[Callable] = None,
        metadata= None
    ) -> Dict[str, Any]:
        """
        运行完整算法流程
        
        Args:
            cif_file_ids: CIF文件ID列表
            csv_file_id: CSV文件ID
            parameters: 算法参数
            progress_callback: 进度回调函数
            
        Returns:
            完整流程结果
        """
        try:
            logger.info("开始运行完整算法流程")
            
            results = {}
            
            # 1. 基因挖掘 (0-20%)
            mining_result = await self.gene_mining(
                cif_file_ids, 
                lambda p: progress_callback(p * 0.2) if progress_callback else None
            )
            results["mining"] = mining_result
            
            # 2. 基因识别 (20-40%)
            identification_result = await self.gene_identification(
                cif_file_ids,
                lambda p: progress_callback(20 + p * 0.2) if progress_callback else None
            )
            results["identification"] = identification_result
            
            # 3. 基因融合 (40-60%)
            fusion_result = await self.gene_fusion(
                cif_file_ids,
                csv_file_id,
                lambda p: progress_callback(40 + p * 0.2) if progress_callback else None
            )
            results["fusion"] = fusion_result
            
            # 保存融合特征以便后续使用
            fusion_id = f"fusion_{int(time.time())}"
            await file_handler.save_result(fusion_id, fusion_result["fused_features"], "csv")
            
            # 4. 基因编辑 (60-80%)
            editing_result = await self.gene_editing(
                fusion_id,
                parameters.get("target_value", 12.04),
                parameters.get("label_type", "boundary_right"),
                parameters.get("train_mode", True) == "true",
                progress_callback=lambda p: progress_callback(60 + p * 0.2) if progress_callback else None
            )
            results["editing"] = editing_result
            
            # 5. 基因设计 (80-100%)
            design_result = await self.gene_design(
                editing_result["model_id"],
                fusion_id,
                parameters.get("n_calls", 200),
                lambda p: progress_callback(80 + p * 0.2) if progress_callback else None
            )
            results["design"] = design_result
            
            logger.info("完整算法流程执行完成")
            return results
            
        except Exception as e:
            logger.error(f"完整流程执行失败: {e}")
            raise AlgorithmError(f"完整流程执行失败: {e}")
    
    async def generate_visualization(
        self,
        data_id: str,
        visualization_type: str,
        save_filename: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成可视化
        
        Args:
            data_id: 数据ID
            visualization_type: 可视化类型
            save_filename: 保存文件名
            
        Returns:
            可视化结果
        """
        try:
            logger.info(f"生成可视化: {visualization_type}")
            
            # 根据类型生成不同的可视化
            if visualization_type == "tsne":
                data = await file_handler.load_result(data_id, "npy")
                if data is None:
                    data = await file_handler.load_result(data_id, "csv")
                    if data is not None:
                        data = data.values
                
                if data is not None:
                    show_tsne_result(data, save_filename or f"tsne_{int(time.time())}")
            
            elif visualization_type == "edit_2d":
                # 加载SVM模型和数据
                svc_model = await file_handler.load_result(data_id + "_model", "pkl")
                data_pair = await file_handler.load_result(data_id, "csv")
                if svc_model and data_pair:
                    show_edit_2d(svc_model, data_pair, save_filename or f"edit_2d_{int(time.time())}")
            
            elif visualization_type == "edit_3d":
                # 加载SVM模型和数据
                svc_model = await file_handler.load_result(data_id + "_model", "pkl")
                data_pair = await file_handler.load_result(data_id, "csv")
                if svc_model and data_pair:
                    show_edit_3d(svc_model, data_pair, save_filename or f"edit_3d_{int(time.time())}")
            
            elif visualization_type == "optimization":
                # 加载优化历史
                history = await file_handler.load_result(data_id, "json")
                if history:
                    # 生成优化过程可视化
                    pass  # 实现优化可视化逻辑
            
            return {
                "visualization_type": visualization_type,
                "image_urls": [f"/static/images/{save_filename}.png"],
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"生成可视化失败: {e}")
            raise AlgorithmError(f"生成可视化失败: {e}")

# 全局算法实例
material_gene_algorithms = MaterialGeneAlgorithms()