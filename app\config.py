from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
import os
from pathlib import Path

class Settings(BaseSettings):
    """应用配置"""
    
    # 基本配置
    app_name: str = "Material Genome API"
    app_version: str = "1.0.0"
    debug: bool = Field(default=False, env="DEBUG")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=1, env="WORKERS")
    
    # 安全配置
    secret_key: str = Field(default="HIT material_genome", env="SECRET_KEY")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Redis配置（用于任务队列）
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # 文件上传配置
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    allowed_file_extensions: list = [".cif", ".csv"]
    upload_dir: str = "uploads"
    results_dir: str = "results"
    
    # 算法模型路径配置
    model_base_path: str = "./pretrain_model_dict"
    
    # CGCL模型配置
    cgcl_model_path: str = "./pretrain_model_dict/model_best.pth.tar"
    
    # EGCL模型配置  
    egcl_model_path: str = "./pretrain_model_dict/best_contrast.pt"
    
    # CLIP模型配置
    clip_model_path: str = "./pretrain_model_dict/best_contrast_hit.pt"
    
    # SVM模型配置
    svm_model_path: str = "./pretrain_model_dict/svc_model.pkl"
    
    # 其他配置文件
    custom_yaml: str = "./custom_config.yaml"
    atom_init_json: str = "./atom_init.json"
    afs_feature_csv: str = "./data_pre/ori_features.csv"
    
    # 算法默认参数
    default_target_value: float = 12.04
    default_n_calls: int = 200
    default_label_type: str = "boundary_right"
    
    # 任务配置
    task_timeout: int = 3600  # 1小时超时
    max_concurrent_tasks: int = 5
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = "logs/app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 创建必要的目录
        Path(self.upload_dir).mkdir(exist_ok=True)
        Path(self.results_dir).mkdir(exist_ok=True)
        Path("logs").mkdir(exist_ok=True)


class AlgorithmConfig:
    """算法相关配置"""
    
    # AFS特征配置
    afs_feature_csv = "./data_pre/ori_features.csv"
    afs_csv_out_path = "./output"
    
    # CGCL配置
    cgcl_config = {
        "atom_fea_len": 64,
        "n_conv": 3,
        "h_fea_len": 128,
        "n_h": 1
    }
    
    # EGCL配置  
    egcl_config = {
        "neighbors": 12,
        "rcut": 3.0,
        "search_delta": 1.0,
        "n_classification": 7,
        "bond_fea_len": 80,
        "angle_fea_len": 80,
        "n_conv_edge": 3,
        "h_fea_edge": 128,
        "h_fea_angle": 128
    }
    
    # CLIP配置
    clip_config = {
        "embedding_size": 384,
        "n_layers": 2,
        "n_heads": 4,
        "num_points": 9,
        "block_size": 31
    }
    
    # SVM配置
    svm_config = {
        "kernel": "rbf",
        "C": 1.0,
        "gamma": 0.1,
        "probability": True,
        "random_state": 42,
        "class_weight": "balanced"
    }
    
    # 贝叶斯优化配置
    optimization_config = {
        "n_random_starts": 20,
        "random_state": 42,
        "confidence_threshold": 0.8,
        "max_iter": 5
    }


# 全局配置实例
settings = Settings()
algorithm_config = AlgorithmConfig()