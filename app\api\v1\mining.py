"""
基因挖掘API路由
提供SOAP描述符和AFS特征提取功能
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from typing import List
import logging

from app.models.request_models import GeneMiningRequest
from app.models.response_models import (
    GeneMiningResponse, TaskResponse, ResponseStatus, TaskStatus
)
from app.services.task_manager import task_manager
from app.core.gene_algorithms import material_gene_algorithms
from app.services.file_handler import file_handler

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/mining", tags=["基因挖掘"])

@router.post("/start", response_model=TaskResponse)
async def start_gene_mining(request: GeneMiningRequest):
    """
    启动基因挖掘任务
    
    从CIF文件中提取SOAP描述符和AFS特征
    这是材料基因算法的第一步，用于从晶体结构中挖掘基本特征
    """
    try:
        logger.info(f"启动基因挖掘任务，处理 {len(request.cif_files)} 个CIF文件")
        
        # 验证文件存在性
        for file_id in request.cif_files:
            file_info = await file_handler.get_file_info(file_id)
            if not file_info:
                raise HTTPException(
                    status_code=404, 
                    detail=f"CIF文件不存在: {file_id}"
                )
            if file_info.get("file_type") != "cif":
                raise HTTPException(
                    status_code=400,
                    detail=f"文件类型错误，期望CIF文件: {file_id}"
                )
        
        # 创建异步任务
        task_id = await task_manager.create_task(
            task_type="gene_mining",
            task_func=material_gene_algorithms.gene_mining,
            cif_file_ids=request.cif_files,
            metadata={
                "cif_count": len(request.cif_files),
                "use_soap": request.use_soap,
                "use_afs": request.use_afs
            }
        )
        logger.info(f"基因挖掘任务已在mining创建，任务ID: {task_id}")
        return TaskResponse(
            status=ResponseStatus.SUCCESS,
            message="基因挖掘任务已启动",
            task_id=task_id,
            task_status=TaskStatus.PENDING,
            progress=0.0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动基因挖掘任务失败: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"启动基因挖掘任务失败: {str(e)}"
        )

@router.get("/status/{task_id}")
async def get_mining_status(task_id: str):
    """获取基因挖掘任务状态"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "gene_mining":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "获取任务状态成功",
            "task_id": task_id,
            "status": task_info.status.value,
            "progress": task_info.progress,
            "start_time": task_info.start_time,
            "end_time": task_info.end_time,
            # "result": task_info.result if task_info.status == TaskStatus.COMPLETED else None,
            "error_message": task_info.error_message
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取挖掘状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/result/{task_id}", response_model=GeneMiningResponse)
async def get_mining_result(task_id: str):
    """获取基因挖掘结果"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "gene_mining":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        if task_info.status != TaskStatus.COMPLETED:
            raise HTTPException(
                status_code=400, 
                detail=f"任务未完成，当前状态: {task_info.status.value}"
            )
        
        result = task_info.result
        if not result:
            raise HTTPException(status_code=404, detail="任务结果不存在")
        
        # 保存特征数据到文件系统
        afs_features_id = None
        soap_features_id = None
        
        if result.get("afs_features"):
            afs_features_id = f"afs_{task_id}"
            print("afs:",type(result["afs_features"]))
            asf_url= await file_handler.save_result(afs_features_id, result["afs_features"], "json")
        
        if result.get("soap_features"):
            soap_features_id = f"soap_{task_id}"
            soap_url= await file_handler.save_result(soap_features_id, result["soap_features"], "npy")
        logger.info(f"基因挖掘结果已保存，AFS特征ID: {afs_features_id}, SOAP特征ID: {soap_features_id}")
        print(result["feature_dimensions"])
        return GeneMiningResponse(
            status=ResponseStatus.SUCCESS,
            message="基因挖掘完成",
            task_id=task_id,
            afs_features_id=afs_features_id,
            soap_features_id=soap_features_id,
            afs_download_url=asf_url,      # 新增字段赋值
            soap_download_url=soap_url,    # 新增字段赋值
            processed_samples=result.get("processed_samples", 0),
            feature_dimensions=result.get("feature_dimensions", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取挖掘结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取结果失败: {str(e)}")

@router.post("/batch", response_model=List[TaskResponse])
async def start_batch_mining(requests: List[GeneMiningRequest]):
    """批量启动基因挖掘任务"""
    try:
        logger.info(f"批量启动 {len(requests)} 个基因挖掘任务")
        
        if len(requests) > 10:  # 限制批量任务数量
            raise HTTPException(
                status_code=400,
                detail="批量任务数量不能超过10个"
            )
        
        results = []
        
        for i, request in enumerate(requests):
            try:
                # 验证文件
                for file_id in request.cif_files:
                    file_info = await file_handler.get_file_info(file_id)
                    if not file_info or file_info.get("file_type") != "cif":
                        raise ValueError(f"无效的CIF文件: {file_id}")
                
                # 创建任务
                task_id = await task_manager.create_task(
                    task_type="gene_mining",
                    task_func=material_gene_algorithms.gene_mining,
                    cif_file_ids=request.cif_files,
                    metadata={
                        "batch_index": i,
                        "cif_count": len(request.cif_files),
                        "use_soap": request.use_soap,
                        "use_afs": request.use_afs
                    }
                )
                
                results.append(TaskResponse(
                    status=ResponseStatus.SUCCESS,
                    message=f"批量任务 {i+1} 已启动",
                    task_id=task_id,
                    task_status=TaskStatus.PENDING,
                    progress=0.0
                ))
                
            except Exception as e:
                logger.warning(f"批量任务 {i+1} 启动失败: {e}")
                results.append(TaskResponse(
                    status=ResponseStatus.ERROR,
                    message=f"批量任务 {i+1} 启动失败: {str(e)}",
                    task_id="",
                    task_status=TaskStatus.FAILED,
                    progress=0.0
                ))
        
        return results
        
    except Exception as e:
        logger.error(f"批量启动失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量启动失败: {str(e)}")

@router.delete("/cancel/{task_id}")
async def cancel_mining_task(task_id: str):
    """取消基因挖掘任务"""
    try:
        success = await task_manager.cancel_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=400, 
                detail="任务取消失败，可能任务不存在或已完成"
            )
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "任务已取消",
            "task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

@router.get("/history")
async def get_mining_history(
    limit: int = 20,
    status_filter: str = None
):
    """获取基因挖掘历史记录"""
    try:
        # 获取基因挖掘类型的任务
        mining_tasks = task_manager.get_tasks_by_type("gene_mining")
        
        # 状态过滤
        if status_filter:
            mining_tasks = [
                task for task in mining_tasks 
                if task.status.value == status_filter
            ]
        
        # 按时间排序并限制数量
        mining_tasks.sort(key=lambda x: x.start_time or "", reverse=True)
        mining_tasks = mining_tasks[:limit]
        
        # 转换为响应格式
        history = []
        for task in mining_tasks:
            history.append({
                "task_id": task.task_id,
                "status": task.status.value,
                "start_time": task.start_time,
                "end_time": task.end_time,
                "progress": task.progress,
                "cif_count": task.metadata.get("cif_count", 0) if task.metadata else 0,
                "processed_samples": task.result.get("processed_samples", 0) if task.result else 0
            })
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": f"获取到 {len(history)} 条历史记录",
            "history": history,
            "total_count": len(mining_tasks)
        }
        
    except Exception as e:
        logger.error(f"获取历史记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {str(e)}")

@router.get("/statistics")
async def get_mining_statistics():
    """获取基因挖掘统计信息"""
    try:
        mining_tasks = task_manager.get_tasks_by_type("gene_mining")
        
        # 统计不同状态的任务数量
        status_counts = {}
        total_samples = 0
        total_duration = 0
        completed_count = 0
        
        for task in mining_tasks:
            status = task.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
            
            if task.result:
                total_samples += task.result.get("processed_samples", 0)
            
            if task.status == TaskStatus.COMPLETED and task.start_time and task.end_time:
                duration = (task.end_time - task.start_time).total_seconds()
                total_duration += duration
                completed_count += 1
        
        avg_duration = total_duration / completed_count if completed_count > 0 else 0
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "获取统计信息成功",
            "statistics": {
                "total_tasks": len(mining_tasks),
                "status_distribution": status_counts,
                "total_processed_samples": total_samples,
                "average_duration_seconds": avg_duration,
                "success_rate": (status_counts.get("completed", 0) / len(mining_tasks) * 100) if mining_tasks else 0
            }
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
import json  
import numpy as np
import io
@router.get("/download/{file_type}/{task_id}", 
           summary="下载特征数据文件",
           description="下载AFS或SOAP特征数据文件")
async def download_features(file_type: str, task_id: str):
    """
    根据任务ID和特征类型下载原始特征数据
    
    - **file_type**: 特征类型 (afs或soap)
    - **task_id**: 任务ID（从基因挖掘响应中获取）
    """
    try:
        # 参数校验
        if file_type not in ["afs", "soap"]:
            raise HTTPException(status_code=400, detail="文件类型必须是 'afs' 或 'soap'")

        file_id = f"{file_type}_{task_id}"
        
        # === AFS特征（JSON文件下载） ===
        if file_type == "afs":
            content = await file_handler.load_result(file_id, "json")
            return StreamingResponse(
                iter([json.dumps(content).encode('utf-8')]),
                media_type="application/json",
                headers={
                    "Content-Disposition": f"attachment; filename=afs_features_{task_id}.json"
                }
            )
        
        # === SOAP特征（NPY二进制下载） ===
        elif file_type == "soap":
            np_array = await file_handler.load_result(file_id, "npy")
            
            # 生成NPY文件字节流
            buffer = io.BytesIO()
            np.save(buffer, np_array)
            buffer.seek(0)
            
            return StreamingResponse(
                buffer,
                media_type="application/octet-stream",
                headers={
                    "Content-Disposition": f"attachment; filename=soap_features_{task_id}.npy"
                }
            )

    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="未找到对应的特征文件")
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail="文件下载服务异常")
