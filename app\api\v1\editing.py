"""
基因编辑API路由
提供SVM分类器和半监督学习的材料性能筛选功能
"""

from fastapi import APIRouter, HTTPException
from typing import List, Optional, Union, Tuple
import logging
import os
from app.models.request_models import GeneEditingRequest, LabelType
from app.models.response_models import (
    GeneEditingResponse, TaskResponse, ResponseStatus, TaskStatus
)
from app.services.task_manager import task_manager
from app.core.gene_algorithms import material_gene_algorithms
from app.services.file_handler import file_handler

from app.core.units_models import show_edit_2d,show_edit_3d
logger = logging.getLogger(__name__)
router = APIRouter(prefix="/editing", tags=["基因编辑"])

@router.post("/start", response_model=TaskResponse)
async def start_gene_editing(request: GeneEditingRequest):
    """
    启动基因编辑任务
    
    使用SVM分类器对材料进行性能筛选，支持三种标签模式：
    - boundary_left: 左边界筛选（小于阈值的材料）
    - boundary_right: 右边界筛选（大于阈值的材料）
    - interval: 区间筛选（在指定范围内的材料）
    """
    try:
        logger.info(f"启动基因编辑任务，标签类型: {request.label_type}")
        
        # 验证特征数据存在
        features_data = await file_handler.load_result(request.features_data, "csv")
        if features_data is None:
            features_data = await file_handler.load_result(request.features_data, "json")
        
        if features_data is None:
            raise HTTPException(
                status_code=404,
                detail=f"特征数据不存在: {request.features_data}"
            )
        
        # 验证标签参数
        if request.label_type == LabelType.INTERVAL:
            if not isinstance(request.flag, (list, tuple)) or len(request.flag) != 2:
                raise HTTPException(
                    status_code=400,
                    detail="区间模式需要提供包含两个值的范围 [min, max]"
                )
            if request.flag[0] >= request.flag[1]:
                raise HTTPException(
                    status_code=400,
                    detail="区间范围无效：最小值必须小于最大值"
                )
        else:
            if not isinstance(request.flag, (int, float)):
                raise HTTPException(
                    status_code=400,
                    detail="边界模式需要提供单个数值作为阈值"
                )
        
        # 创建异步任务
        task_id = await task_manager.create_task(
            task_type="gene_editing",
            task_func=material_gene_algorithms.gene_editing,
            features_data_id=request.features_data,
            flag=request.flag,
            label_type=request.label_type.value,
            is_train=request.is_train,
            metadata={
                "label_type": request.label_type.value,
                "flag": request.flag,
                "is_train": request.is_train,
                "auto_select_ratio": request.auto_select_ratio
            }
        )
        
        return TaskResponse(
            status=ResponseStatus.SUCCESS,
            message="基因编辑任务已启动",
            task_id=task_id,
            task_status=TaskStatus.PENDING,
            progress=0.0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动基因编辑任务失败: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"启动基因编辑任务失败: {str(e)}"
        )

@router.get("/status/{task_id}")
async def get_editing_status(task_id: str):
    """获取基因编辑任务状态"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "gene_editing":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "获取任务状态成功",
            "task_id": task_id,
            "status": task_info.status.value,
            "progress": task_info.progress,
            "start_time": task_info.start_time,
            "end_time": task_info.end_time,
            #"result": task_info.result if task_info.status == TaskStatus.COMPLETED else None,
            "error_message": task_info.error_message,
            "metadata": task_info.metadata
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取编辑状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/result/{task_id}", response_model=GeneEditingResponse)
async def get_editing_result(task_id: str):
    """获取基因编辑结果"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "gene_editing":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        if task_info.status != TaskStatus.COMPLETED:
            raise HTTPException(
                status_code=400, 
                detail=f"任务未完成，当前状态: {task_info.status.value}"
            )
        
        result = task_info.result
        if not result:
            raise HTTPException(status_code=404, detail="任务结果不存在")
        print(result)
        model_id=result["model_id"]
        if model_id:
            edit_model = await file_handler.load_result(model_id, "pkl")
        features_id = result.get("features_data_id", "")
        if features_id:
            final_feature = await file_handler.load_result(features_id, "csv")
            if final_feature is None:
                final_feature = await file_handler.load_result(features_id, "json")
        save_out_path = os.path.join(await file_handler.get_save_result(), task_id)
        os.makedirs(save_out_path,exist_ok=True)
        edit_2d_png_path= f"{save_out_path}/2d_edit.png"
        edit_3d_png_path= f"{save_out_path}/3d_edit.png"
        show_edit_2d(edit_model,final_feature,save_filename=edit_2d_png_path)
        show_edit_3d(edit_model,final_feature,save_filename=edit_3d_png_path)
        return GeneEditingResponse(
            status=ResponseStatus.SUCCESS,
            message="基因编辑完成",
            task_id=task_id,
            model_id=result.get("model_id", ""),
            #classification_accuracy=result.get("classification_accuracy", None),
            edit_3d_png_path=edit_3d_png_path,
            edit_2d_png_path=edit_2d_png_path,
            model_parameters=result.get("model_parameters", {}),
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取编辑结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取结果失败: {str(e)}")

# @router.post("/continue_from_fusion/{fusion_task_id}")
# async def continue_from_fusion(
#     fusion_task_id: str,
#     flag: Union[float, Tuple[float, float]],
#     label_type: LabelType,
#     is_train: bool = True,
#     auto_select_ratio: float = 0.3
# ):
#     """
#     从基因融合结果继续进行基因编辑
    
#     这是一个便捷方法，可以直接使用基因融合的结果进行编辑
#     """
#     try:
#         # 获取融合任务结果
#         fusion_task = task_manager.get_task_status(fusion_task_id)
#         if not fusion_task:
#             raise HTTPException(status_code=404, detail="基因融合任务不存在")
        
#         if fusion_task.task_type != "gene_fusion":
#             raise HTTPException(status_code=400, detail="任务类型不是基因融合")
        
#         if fusion_task.status != TaskStatus.COMPLETED:
#             raise HTTPException(
#                 status_code=400, 
#                 detail="基因融合任务未完成"
#             )
        
#         # 获取融合特征数据ID
#         fusion_features_id = f"fusion_{fusion_task_id}"
        
#         # 验证融合特征数据存在
#         features_data = await file_handler.load_result(fusion_features_id, "csv")
#         if features_data is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail="融合特征数据不存在"
#             )
        
#         # 创建编辑任务
#         task_id = await task_manager.create_task(
#             task_type="gene_editing",
#             task_func=material_gene_algorithms.gene_editing,
#             features_data_id=fusion_features_id,
#             flag=flag,
#             label_type=label_type.value,
#             is_train=is_train,
#             model_path=None,
#             metadata={
#                 "label_type": label_type.value,
#                 "flag": flag,
#                 "is_train": is_train,
#                 "auto_select_ratio": auto_select_ratio,
#                 "continued_from_fusion": fusion_task_id
#             }
#         )
        
#         return TaskResponse(
#             status=ResponseStatus.SUCCESS,
#             message=f"基因编辑任务已启动（继承自融合任务 {fusion_task_id}）",
#             task_id=task_id,
#             task_status=TaskStatus.PENDING,
#             progress=0.0
#         )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"从融合结果继续编辑失败: {e}")
#         raise HTTPException(
#             status_code=500, 
#             detail=f"从融合结果继续编辑失败: {str(e)}"
#         )

# @router.post("/predict/{model_id}")
# async def predict_materials(
#     model_id: str,
#     features_data_id: str
# ):
#     """
#     使用训练好的模型预测新材料
    
#     对新的材料特征数据进行分类预测
#     """
#     try:
#         # 加载SVM模型
#         svm_model = await file_handler.load_result(model_id, "pkl")
#         if svm_model is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"SVM模型不存在: {model_id}"
#             )
        
#         # 加载特征数据
#         features_data = await file_handler.load_result(features_data_id, "csv")
#         if features_data is None:
#             features_data = await file_handler.load_result(features_data_id, "json")
        
#         if features_data is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"特征数据不存在: {features_data_id}"
#             )
        
#         # 预测
#         import pandas as pd
#         import numpy as np
        
#         if isinstance(features_data, dict):
#             features_df = pd.DataFrame(features_data)
#         else:
#             features_df = features_data
        
#         # 假设特征在前384列
#         X = features_df.iloc[:, :384].values
        
#         # 进行预测
#         predictions = svm_model.predict(X)
#         probabilities = svm_model.predict_proba(X) if hasattr(svm_model, 'predict_proba') else None
        
#         # 统计结果
#         unique, counts = np.unique(predictions, return_counts=True)
#         prediction_counts = dict(zip(unique, counts))
        
#         # 准备结果
#         prediction_results = {
#             "predictions": predictions.tolist(),
#             "probabilities": probabilities.tolist() if probabilities is not None else None,
#             "summary": {
#                 "total_samples": len(predictions),
#                 "positive_predictions": prediction_counts.get(1, 0),
#                 "negative_predictions": prediction_counts.get(0, 0),
#                 "positive_ratio": prediction_counts.get(1, 0) / len(predictions)
#             }
#         }
        
#         # 保存预测结果
#         prediction_id = f"prediction_{model_id}_{int(time.time())}"
#         await file_handler.save_result(prediction_id, prediction_results, "json")
        
#         return {
#             "status": ResponseStatus.SUCCESS,
#             "message": "材料预测完成",
#             "model_id": model_id,
#             "prediction_id": prediction_id,
#             "results": prediction_results["summary"]
#         }
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"材料预测失败: {e}")
#         raise HTTPException(status_code=500, detail=f"材料预测失败: {str(e)}")

# @router.post("/evaluate_model/{model_id}")
# async def evaluate_model(
#     model_id: str,
#     test_features_id: str,
#     test_labels_id: Optional[str] = None
# ):
#     """
#     评估模型性能
    
#     使用测试数据评估SVM模型的分类性能
#     """
#     try:
#         # 加载模型
#         svm_model = await file_handler.load_result(model_id, "pkl")
#         if svm_model is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"SVM模型不存在: {model_id}"
#             )
        
#         # 加载测试特征
#         test_features = await file_handler.load_result(test_features_id, "csv")
#         if test_features is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"测试特征不存在: {test_features_id}"
#             )
        
#         import pandas as pd
#         import numpy as np
#         from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
        
#         if isinstance(test_features, dict):
#             test_df = pd.DataFrame(test_features)
#         else:
#             test_df = test_features
        
#         X_test = test_df.iloc[:, :384].values
        
#         # 如果提供了标签，进行完整评估
#         if test_labels_id:
#             test_labels = await file_handler.load_result(test_labels_id, "csv")
#             if test_labels is not None:
#                 if isinstance(test_labels, dict):
#                     y_test = list(test_labels.values())[0]
#                 else:
#                     y_test = test_labels.values.ravel()
                
#                 # 预测
#                 y_pred = svm_model.predict(X_test)
#                 y_proba = svm_model.predict_proba(X_test) if hasattr(svm_model, 'predict_proba') else None
                
#                 # 计算各种指标
#                 evaluation_metrics = {
#                     "accuracy": float(accuracy_score(y_test, y_pred)),
#                     "precision": float(precision_score(y_test, y_pred, average='weighted')),
#                     "recall": float(recall_score(y_test, y_pred, average='weighted')),
#                     "f1_score": float(f1_score(y_test, y_pred, average='weighted')),
#                     "confusion_matrix": confusion_matrix(y_test, y_pred).tolist(),
#                     "sample_count": len(y_test)
#                 }
                
#                 if y_proba is not None:
#                     from sklearn.metrics import roc_auc_score
#                     if len(np.unique(y_test)) == 2:  # 二分类
#                         evaluation_metrics["auc_score"] = float(roc_auc_score(y_test, y_proba[:, 1]))
#             else:
#                 raise HTTPException(
#                     status_code=404,
#                     detail=f"测试标签不存在: {test_labels_id}"
#                 )
#         else:
#             # 仅预测，无监督评估
#             y_pred = svm_model.predict(X_test)
#             unique, counts = np.unique(y_pred, return_counts=True)
#             prediction_dist = dict(zip(unique, counts))
            
#             evaluation_metrics = {
#                 "prediction_distribution": prediction_dist,
#                 "sample_count": len(y_pred),
#                 "note": "无标签数据，仅提供预测分布"
#             }
        
#         # 保存评估结果
#         evaluation_id = f"evaluation_{model_id}_{int(time.time())}"
#         await file_handler.save_result(evaluation_id, evaluation_metrics, "json")
        
#         return {
#             "status": ResponseStatus.SUCCESS,
#             "message": "模型评估完成",
#             "model_id": model_id,
#             "evaluation_id": evaluation_id,
#             "metrics": evaluation_metrics
#         }
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"模型评估失败: {e}")
#         raise HTTPException(status_code=500, detail=f"模型评估失败: {str(e)}")

# @router.post("/optimize_threshold/{model_id}")
# async def optimize_threshold(
#     model_id: str,
#     features_data_id: str,
#     target_metric: str = "f1_score",
#     validation_split: float = 0.2
# ):
#     """
#     优化分类阈值
    
#     通过网格搜索找到最佳的分类阈值
#     """
#     try:
#         # 加载模型和数据
#         svm_model = await file_handler.load_result(model_id, "pkl")
#         if svm_model is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"SVM模型不存在: {model_id}"
#             )
        
#         features_data = await file_handler.load_result(features_data_id, "csv")
#         if features_data is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"特征数据不存在: {features_data_id}"
#             )
        
#         import pandas as pd
#         import numpy as np
#         from sklearn.model_selection import train_test_split
#         from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
#         if isinstance(features_data, dict):
#             features_df = pd.DataFrame(features_data)
#         else:
#             features_df = features_data
        
#         X = features_df.iloc[:, :384].values
        
#         # 如果模型支持概率预测
#         if hasattr(svm_model, 'predict_proba'):
#             # 获取预测概率
#             y_proba = svm_model.predict_proba(X)[:, 1]
            
#             # 尝试不同的阈值
#             thresholds = np.arange(0.1, 0.9, 0.05)
#             best_threshold = 0.5
#             best_score = 0.0
#             threshold_results = []
            
#             # 生成伪标签用于阈值优化（在实际应用中应使用真实标签）
#             y_true = svm_model.predict(X)  # 使用当前模型预测作为伪标签
            
#             for threshold in thresholds:
#                 y_pred_threshold = (y_proba >= threshold).astype(int)
                
#                 # 计算指标
#                 if target_metric == "accuracy":
#                     score = accuracy_score(y_true, y_pred_threshold)
#                 elif target_metric == "precision":
#                     score = precision_score(y_true, y_pred_threshold, average='weighted', zero_division=0)
#                 elif target_metric == "recall":
#                     score = recall_score(y_true, y_pred_threshold, average='weighted', zero_division=0)
#                 else:  # f1_score
#                     score = f1_score(y_true, y_pred_threshold, average='weighted', zero_division=0)
                
#                 threshold_results.append({
#                     "threshold": float(threshold),
#                     "score": float(score)
#                 })
                
#                 if score > best_score:
#                     best_score = score
#                     best_threshold = threshold
            
#             optimization_result = {
#                 "best_threshold": float(best_threshold),
#                 "best_score": float(best_score),
#                 "target_metric": target_metric,
#                 "threshold_curve": threshold_results,
#                 "model_supports_probability": True
#             }
#         else:
#             optimization_result = {
#                 "message": "模型不支持概率预测，无法优化阈值",
#                 "model_supports_probability": False,
#                 "recommendation": "建议重新训练模型时设置probability=True"
#             }
        
#         # 保存优化结果
#         optimization_id = f"threshold_opt_{model_id}_{int(time.time())}"
#         await file_handler.save_result(optimization_id, optimization_result, "json")
        
#         return {
#             "status": ResponseStatus.SUCCESS,
#             "message": "阈值优化完成",
#             "model_id": model_id,
#             "optimization_id": optimization_id,
#             "result": optimization_result
#         }
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"阈值优化失败: {e}")
#         raise HTTPException(status_code=500, detail=f"阈值优化失败: {str(e)}")

# @router.post("/semi_supervised_learning")
# async def semi_supervised_learning(
#     labeled_features_id: str,
#     unlabeled_features_id: str,
#     confidence_threshold: float = 0.8,
#     max_iterations: int = 5
# ):
#     """
#     半监督学习
    
#     使用少量标注数据和大量无标注数据进行模型训练
#     """
#     try:
#         logger.info("启动半监督学习任务")
        
#         # 验证输入数据
#         labeled_data = await file_handler.load_result(labeled_features_id, "csv")
#         unlabeled_data = await file_handler.load_result(unlabeled_features_id, "csv")
        
#         if labeled_data is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"标注数据不存在: {labeled_features_id}"
#             )
        
#         if unlabeled_data is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"无标注数据不存在: {unlabeled_features_id}"
#             )
        
#         # 创建半监督学习任务
#         task_id = await task_manager.create_task(
#             task_type="semi_supervised_learning",
#             task_func=semi_supervised_learning_worker,
#             labeled_features_id=labeled_features_id,
#             unlabeled_features_id=unlabeled_features_id,
#             confidence_threshold=confidence_threshold,
#             max_iterations=max_iterations,
#             metadata={
#                 "learning_type": "semi_supervised",
#                 "confidence_threshold": confidence_threshold,
#                 "max_iterations": max_iterations
#             }
#         )
        
#         return TaskResponse(
#             status=ResponseStatus.SUCCESS,
#             message="半监督学习任务已启动",
#             task_id=task_id,
#             task_status=TaskStatus.PENDING,
#             progress=0.0
#         )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"启动半监督学习失败: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"启动半监督学习失败: {str(e)}"
#         )

# async def semi_supervised_learning_worker(
#     labeled_features_id: str,
#     unlabeled_features_id: str,
#     confidence_threshold: float,
#     max_iterations: int,
#     progress_callback=None
# ):
#     """半监督学习工作函数"""
#     try:
#         # 加载数据
#         labeled_data = await file_handler.load_result(labeled_features_id, "csv")
#         unlabeled_data = await file_handler.load_result(unlabeled_features_id, "csv")
        
#         import pandas as pd
#         import numpy as np
#         from sklearn.svm import SVC
#         from sklearn.metrics import accuracy_score
        
#         # 转换数据格式
#         if isinstance(labeled_data, dict):
#             labeled_df = pd.DataFrame(labeled_data)
#         else:
#             labeled_df = labeled_data
        
#         if isinstance(unlabeled_data, dict):
#             unlabeled_df = pd.DataFrame(unlabeled_data)
#         else:
#             unlabeled_df = unlabeled_data
        
#         # 准备训练数据
#         X_labeled = labeled_df.iloc[:, :384].values
#         y_labeled = labeled_df.iloc[:, -1].values  # 假设最后一列是标签
#         X_unlabeled = unlabeled_df.iloc[:, :384].values
        
#         # 初始模型训练
#         model = SVC(probability=True, random_state=42)
#         model.fit(X_labeled, y_labeled)
        
#         iteration_results = []
        
#         for iteration in range(max_iterations):
#             if progress_callback:
#                 progress_callback((iteration + 1) / max_iterations * 100)
            
#             # 预测无标注数据
#             probabilities = model.predict_proba(X_unlabeled)
#             predictions = model.predict(X_unlabeled)
            
#             # 选择高置信度的预测
#             max_proba = np.max(probabilities, axis=1)
#             confident_mask = max_proba >= confidence_threshold
            
#             if np.sum(confident_mask) == 0:
#                 break  # 没有高置信度的预测
            
#             # 添加高置信度的样本到训练集
#             X_new = X_unlabeled[confident_mask]
#             y_new = predictions[confident_mask]
            
#             X_labeled = np.vstack([X_labeled, X_new])
#             y_labeled = np.hstack([y_labeled, y_new])
            
#             # 从无标注数据中移除已标注的样本
#             X_unlabeled = X_unlabeled[~confident_mask]
            
#             # 重新训练模型
#             model.fit(X_labeled, y_labeled)
            
#             iteration_results.append({
#                 "iteration": iteration + 1,
#                 "new_samples": int(np.sum(confident_mask)),
#                 "total_labeled": len(X_labeled),
#                 "remaining_unlabeled": len(X_unlabeled),
#                 "avg_confidence": float(np.mean(max_proba[confident_mask])) if np.sum(confident_mask) > 0 else 0
#             })
            
#             if len(X_unlabeled) == 0:
#                 break  # 所有数据都已标注
        
#         # 保存最终模型
#         import time
#         final_model_id = f"semi_supervised_model_{int(time.time())}"
#         await file_handler.save_result(final_model_id, model, "pkl")
        
#         return {
#             "model_id": final_model_id,
#             "final_labeled_count": len(X_labeled),
#             "iteration_history": iteration_results,
#             "convergence": len(X_unlabeled) == 0 or iteration >= max_iterations - 1
#         }
        
#     except Exception as e:
#         logger.error(f"半监督学习执行失败: {e}")
#         raise

# @router.delete("/cancel/{task_id}")
# async def cancel_editing_task(task_id: str):
#     """取消基因编辑任务"""
#     try:
#         success = await task_manager.cancel_task(task_id)
        
#         if not success:
#             raise HTTPException(
#                 status_code=400, 
#                 detail="任务取消失败，可能任务不存在或已完成"
#             )
        
#         return {
#             "status": ResponseStatus.SUCCESS,
#             "message": "基因编辑任务已取消",
#             "task_id": task_id
#         }
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"取消任务失败: {e}")
#         raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

# @router.get("/models")
# async def list_trained_models():
#     """列出所有训练好的SVM模型"""
#     try:
#         # 获取所有基因编辑任务
#         editing_tasks = task_manager.get_tasks_by_type("gene_editing")
#         completed_tasks = [
#             task for task in editing_tasks 
#             if task.status == TaskStatus.COMPLETED and task.result
#         ]
        
#         models = []
#         for task in completed_tasks:
#             result = task.result
#             models.append({
#                 "model_id": result.get("model_id"),
#                 "task_id": task.task_id,
#                 "creation_time": task.end_time,
#                 "accuracy": result.get("classification_accuracy", 0.0),
#                 "positive_samples": result.get("positive_samples", 0),
#                 "negative_samples": result.get("negative_samples", 0),
#                 "label_type": task.metadata.get("label_type") if task.metadata else None,
#                 "flag": task.metadata.get("flag") if task.metadata else None
#             })
        
#         return {
#             "status": ResponseStatus.SUCCESS,
#             "message": f"找到 {len(models)} 个训练好的模型",
#             "models": models
#         }
        
#     except Exception as e:
#         logger.error(f"列出模型失败: {e}")
#         raise HTTPException(status_code=500, detail=f"列出模型失败: {str(e)}")
# from fastapi import APIRouter, HTTPException
# from fastapi.responses import FileResponse
# import os
# # 🔍 可视化图像下载接口
# @router.get("/editing/{task_id}")
# async def get_tsne_image(task_id: str):
    

#     # 构建图像路径
#     save_out_path =await file_handler.get_save_result()
#     save_out_path = os.path.join(save_out_path, task_id)
#     image_path = os.path.join(save_out_path, f"clip_tsne_result.png")

#     if not os.path.exists(image_path):
#         raise HTTPException(status_code=404, detail="图像未找到，请先生成可视化结果")

#     return FileResponse(image_path, media_type='image/png', filename=f"fusion_tsne_result.png")
# 🔍 可视化图像下载接口
from fastapi.responses import StreamingResponse
from fastapi.responses import FileResponse
@router.get("/edit_png/{task_id}")
async def get_tsne_image(task_id: str,type:str):
    

    # 构建图像路径
    save_out_path =await file_handler.get_save_result()
    save_out_path = os.path.join(save_out_path, task_id)
    image_path = os.path.join(save_out_path, f"{type}d_edit.png")

    if not os.path.exists(image_path):
        raise HTTPException(status_code=404, detail="图像未找到，请先生成可视化结果")

    return FileResponse(image_path, media_type='image/png', filename=f"edit_{type}d.png")
