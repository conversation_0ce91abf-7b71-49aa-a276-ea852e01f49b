from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from fastapi.responses import J<PERSON>NResponse
from typing import List, Optional
import logging

from app.models.request_models import FileUploadRequest
from app.models.response_models import (
    FileUploadResponse, FileListResponse, BaseResponse, ResponseStatus
)
from app.services.file_handler import file_handler
from app.utils.exceptions import FileHandlingError

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/upload", tags=["文件管理"])

@router.post("/file", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    file_type: str = Form(..., description="文件类型: cif 或 csv"),
    description: Optional[str] = Form(None, description="文件描述")
):
    """
    上传单个文件
    
    支持的文件类型:
    - CIF: 晶体结构文件
    - CSV: 材料性质数据文件
    """
    try:
        logger.info(f"接收文件上传请求: {file.filename}, 类型: {file_type}")
        
        # 验证文件类型
        if file_type not in ["cif", "csv"]:
            raise HTTPException(
                status_code=400, 
                detail="不支持的文件类型，只支持 'cif' 或 'csv'"
            )
        
        # 上传文件
        result = await file_handler.upload_file(file, file_type)
        
        return FileUploadResponse(
            status=ResponseStatus.SUCCESS,
            message="文件上传成功",
            **result
        )
        
    except FileHandlingError as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"文件上传异常: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post("/files/batch", response_model=List[FileUploadResponse])
async def upload_multiple_files(
    files: List[UploadFile] = File(...),
    file_type: str = Form(..., description="文件类型: cif 或 csv")
):
    """
    批量上传文件
    
    适用于上传多个CIF文件或多个CSV文件
    """
    try:
        logger.info(f"批量上传 {len(files)} 个文件，类型: {file_type}")
        
        # if len(files) > 50:  # 限制批量上传数量
        #     raise HTTPException(
        #         status_code=400,
        #         detail="批量上传文件数量不能超过50个"
        #     )
        
        results = []
        failed_files = []
        
        for file in files:
            try:
                result = await file_handler.upload_file(file, file_type)
                results.append(FileUploadResponse(
                    status=ResponseStatus.SUCCESS,
                    message="文件上传成功",
                    **result
                ))
            except Exception as e:
                logger.warning(f"文件 {file.filename} 上传失败: {e}")
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e)
                })
                results.append(FileUploadResponse(
                    status=ResponseStatus.ERROR,
                    message=f"文件上传失败: {str(e)}",
                    file_id="",
                    file_name=file.filename,
                    file_size=0,
                    file_type=file_type,
                    upload_path=""
                ))
        
        if failed_files:
            logger.warning(f"批量上传中有 {len(failed_files)} 个文件失败")
        
        return results
        
    except Exception as e:
        logger.error(f"批量上传异常: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/files", response_model=FileListResponse)
async def list_files(
    file_type: Optional[str] = None,
    page: int = 1,
    page_size: int = 20
):
    """
    获取文件列表
    
    Args:
        file_type: 文件类型过滤 (cif, csv)
        page: 页码
        page_size: 每页数量
    """
    try:
        logger.info(f"获取文件列表: type={file_type}, page={page}, size={page_size}")
        
        # 获取所有文件
        all_files = await file_handler.list_files(file_type)
        
        # 分页处理
        total_count = len(all_files)
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        files = all_files[start_index:end_index]
        
        return FileListResponse(
            status=ResponseStatus.SUCCESS,
            message=f"获取到 {len(files)} 个文件",
            files=files,
            total_count=total_count
        )
        
    except Exception as e:
        logger.error(f"获取文件列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")

@router.get("/file/{file_id}")
async def get_file_info(file_id: str):
    """获取单个文件的详细信息"""
    try:
        file_info = await file_handler.get_file_info(file_id)
        
        if not file_info:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message="获取文件信息成功",
            **{"data": file_info}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件信息失败: {str(e)}")

@router.delete("/file/{file_id}")
async def delete_file(file_id: str):
    """删除文件"""
    try:
        success = await file_handler.delete_file(file_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="文件不存在或删除失败")
        
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message="文件删除成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")

@router.post("/validate")
async def validate_files(
    file_ids: List[str],
    check_content: bool = True
):
    """
    验证文件有效性
    
    Args:
        file_ids: 文件ID列表
        check_content: 是否检查文件内容
    """
    try:
        logger.info(f"验证 {len(file_ids)} 个文件")
        
        validation_results = []
        
        for file_id in file_ids:
            file_info = await file_handler.get_file_info(file_id)
            
            if not file_info:
                validation_results.append({
                    "file_id": file_id,
                    "valid": False,
                    "error": "文件不存在"
                })
                continue
            
            # 检查文件是否存在于磁盘
            file_path = file_info.get("file_path")
            if not file_path or not Path(file_path).exists():
                validation_results.append({
                    "file_id": file_id,
                    "valid": False,
                    "error": "文件不存在于磁盘"
                })
                continue
            
            # TODO: 如果需要，添加内容验证逻辑
            
            validation_results.append({
                "file_id": file_id,
                "valid": True,
                "file_name": file_info.get("original_name"),
                "file_type": file_info.get("file_type"),
                "file_size": file_info.get("file_size")
            })
        
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message=f"验证完成，{len([r for r in validation_results if r['valid']])} 个文件有效",
            **{"validation_results": validation_results}
        )
        
    except Exception as e:
        logger.error(f"文件验证失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件验证失败: {str(e)}")

@router.get("/storage/info")
async def get_storage_info():
    """获取存储空间信息"""
    try:
        disk_usage = await file_handler.get_disk_usage()
        
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message="获取存储信息成功",
            **{"storage_info": disk_usage}
        )
        
    except Exception as e:
        logger.error(f"获取存储信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取存储信息失败: {str(e)}")

@router.post("/cleanup")
async def cleanup_old_files(days: int = 7):
    """清理旧文件"""
    try:
        await file_handler.cleanup_old_files(days)
        
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message=f"清理 {days} 天前的文件完成"
        )
        
    except Exception as e:
        logger.error(f"清理文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理文件失败: {str(e)}")

# 文件下载端点（如果需要）
@router.get("/download/{file_id}")
async def download_file(file_id: str):
    """下载文件"""
    try:
        file_info = await file_handler.get_file_info(file_id)
        
        if not file_info:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        from fastapi.responses import FileResponse
        return FileResponse(
            path=file_info["file_path"],
            filename=file_info["original_name"],
            media_type='application/octet-stream'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")