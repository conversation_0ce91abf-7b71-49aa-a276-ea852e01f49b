import os
import uuid
import shutil
import magic
import aiofiles
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from fastapi import UploadFile, HTTPException
from datetime import datetime
import pandas as pd
import json
import logging

from app.config import settings
from app.utils.exceptions import FileHandlingError

logger = logging.getLogger(__name__)

class FileHandler:
    """文件处理服务"""
    
    def __init__(self):
        self.upload_dir = Path(settings.upload_dir)
        self.results_dir = Path(settings.results_dir)
        self.max_file_size = settings.max_file_size
        self.allowed_extensions = settings.allowed_file_extensions
        
        # 确保目录存在
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        self.results_dir.mkdir(parents=True, exist_ok=True)
    
    async def upload_file(self, file: UploadFile, file_type: str) -> Dict[str, str]:
        """
        上传文件
        
        Args:
            file: 上传的文件
            file_type: 文件类型 (cif 或 csv)
            
        Returns:
            包含文件信息的字典
        """
        try:
            # 验证文件
            await self._validate_file(file, file_type)
            
            # 生成唯一文件ID和路径
            file_id = str(uuid.uuid4())
            file_extension = Path(file.filename).suffix.lower()
            safe_filename = f"{file_id}{file_extension}"
            file_path = self.upload_dir / safe_filename
            
            # 保存文件
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)
            
            # 保存文件元数据
            metadata = {
                "file_id": file_id,
                "original_name": file.filename,
                "file_type": file_type,
                "file_size": len(content),
                "upload_time": datetime.now().isoformat(),
                "file_path": str(file_path),
                "mime_type": file.content_type
            }
            
            await self._save_metadata(file_id, metadata)
            
            logger.info(f"文件上传成功: {file.filename} -> {file_id}")
            
            return {
                "file_id": file_id,
                "file_name": file.filename,
                "file_size": len(content),
                "file_type": file_type,
                "upload_path": str(file_path)
            }
            
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise FileHandlingError(f"文件上传失败: {str(e)}")
    
    async def _validate_file(self, file: UploadFile, file_type: str):
        """验证上传的文件"""
        
        # 检查文件名
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件扩展名
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in self.allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件类型: {file_extension}. 支持的类型: {self.allowed_extensions}"
            )
        
        # 验证文件类型匹配
        expected_extension = f".{file_type}"
        if file_extension != expected_extension:
            raise HTTPException(
                status_code=400,
                detail=f"文件类型不匹配: 期望 {expected_extension}, 实际 {file_extension}"
            )
        
        # 检查文件大小
        content = await file.read()
        await file.seek(0)  # 重置文件指针
        
        if len(content) > self.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"文件过大: {len(content)} bytes. 最大允许: {self.max_file_size} bytes"
            )
        
        # 验证文件内容（基础验证）
        if file_type == "cif":
            await self._validate_cif_content(content)
        elif file_type == "csv":
            await self._validate_csv_content(content)
    
    async def _validate_cif_content(self, content: bytes):
        """验证CIF文件内容"""
        try:
            content_str = content.decode('utf-8')
            # 基本CIF格式检查
            if not any(keyword in content_str for keyword in ['data_', '_cell_', '_atom_']):
                raise HTTPException(status_code=400, detail="无效的CIF文件格式")
        except UnicodeDecodeError:
            raise HTTPException(status_code=400, detail="CIF文件编码错误")
    
    async def _validate_csv_content(self, content: bytes):
        """验证CSV文件内容"""
        try:
            content_str = content.decode('utf-8')
            # 使用pandas验证CSV格式
            from io import StringIO
            df = pd.read_csv(StringIO(content_str))
            if df.empty:
                raise HTTPException(status_code=400, detail="CSV文件为空")
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"无效的CSV文件: {str(e)}")
    
    async def _save_metadata(self, file_id: str, metadata: Dict):
        """保存文件元数据"""
        metadata_path = self.upload_dir / f"{file_id}_metadata.json"
        async with aiofiles.open(metadata_path, 'w') as f:
            await f.write(json.dumps(metadata, indent=2, ensure_ascii=False))
    
    async def get_file_info(self, file_id: str) -> Optional[Dict]:
        """获取文件信息"""
        try:
            metadata_path = self.upload_dir / f"{file_id}_metadata.json"
            if not metadata_path.exists():
                return None
            
            async with aiofiles.open(metadata_path, 'r') as f:
                content = await f.read()
                return json.loads(content)
        except Exception as e:
            logger.error(f"获取文件信息失败: {str(e)}")
            return None
    
    async def get_file_path(self, file_id: str) -> Optional[str]:
        """获取文件路径"""
        file_info = await self.get_file_info(file_id)
        if file_info:
            return file_info.get("file_path")
        return None
    
    async def list_files(self, file_type: Optional[str] = None) -> List[Dict]:
        """列出所有文件"""
        try:
            files = []
            for metadata_file in self.upload_dir.glob("*_metadata.json"):
                async with aiofiles.open(metadata_file, 'r') as f:
                    content = await f.read()
                    metadata = json.loads(content)
                    
                    if file_type is None or metadata.get("file_type") == file_type:
                        files.append(metadata)
            
            return sorted(files, key=lambda x: x.get("upload_time", ""), reverse=True)
        except Exception as e:
            logger.error(f"列出文件失败: {str(e)}")
            return []
    
    async def delete_file(self, file_id: str) -> bool:
        """删除文件"""
        try:
            file_info = await self.get_file_info(file_id)
            if not file_info:
                return False
            
            # 删除原文件
            file_path = Path(file_info["file_path"])
            if file_path.exists():
                file_path.unlink()
            
            # 删除元数据文件
            metadata_path = self.upload_dir / f"{file_id}_metadata.json"
            if metadata_path.exists():
                metadata_path.unlink()
            
            logger.info(f"文件删除成功: {file_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除文件失败: {str(e)}")
            return False
    async def save_result(self, result_id: str, data: any, result_type: str) -> str:
        """增强版计算结果保存方法，支持所有数据类型的自动处理"""
        try:
            import numpy as np  # 动态导入以避免依赖问题
            import pandas as pd
            from datetime import datetime
            import pickle
            result_file = self.results_dir / f"{result_id}.{result_type}"

            class UniversalEncoder(json.JSONEncoder):
                """支持多种科学计算类型的通用JSON编码器"""
                def default(self, obj):
                    # 处理numpy类型
                    if isinstance(obj, np.ndarray):
                        return obj.tolist()
                    if isinstance(obj, (np.integer, np.floating, np.bool_)):
                        return obj.item()
                    
                    # 处理pandas类型
                    if isinstance(obj, pd.DataFrame):
                        return obj.to_dict(orient='records')
                    if isinstance(obj, pd.Series):
                        return obj.to_dict()
                    
                    # 处理时间类型
                    if isinstance(obj, datetime):
                        return obj.isoformat()
                    
                    # 处理Pydantic模型和其他有 dict() 方法的对象
                    if hasattr(obj, "dict"):
                        return obj.dict()
                    
                    # 最终回退到默认处理
                    return super().default(obj)

            if result_type == "json":
                async with aiofiles.open(result_file, 'w', encoding='utf-8') as f:
                    json_str = json.dumps(
                        data,
                        ensure_ascii=False,
                        indent=2,
                        cls=UniversalEncoder  # 使用自定义编码器
                    )
                    await f.write(json_str)
                
            elif result_type == "csv":
                if not isinstance(data, pd.DataFrame):
                    # 自动转换字典/列表为DataFrame
                    if isinstance(data, dict):
                        data = pd.DataFrame([data])
                    elif isinstance(data, list) and all(isinstance(x, dict) for x in data):
                        data = pd.DataFrame(data)
                    else:
                        raise ValueError("CSV格式需要DataFrame或字典/列表数据")
                data.to_csv(result_file, index=False)

            elif result_type == "npy":
                import numpy as np
                
                # 自动转换特殊情况
                conversion_required = False
                if isinstance(data, list):
                    # 检查列表元素是否包含numpy数组
                    if len(data) > 0 and isinstance(data[0], np.ndarray):
                        try:
                            # 智能维度匹配 例如列表中的一维数组自动堆叠为二维数组
                            converted_data = np.stack(data)
                            conversion_required = True
                        except ValueError:
                            # 行数不匹配时自动填充为一维数组
                            converted_data = np.concatenate(data)
                            conversion_required = True
                else:
                    # 非数组直接尝试转换
                    converted_data = np.asarray(data, dtype=np.float64)
                    conversion_required = True
                
                # 执行实际保存
                if conversion_required:
                    np.save(result_file, converted_data)
                else:
                    np.save(result_file, data)

            elif result_type == "pkl":
                with open(result_file, 'wb') as f:
                    pickle.dump(data, f)

            else:
                raise ValueError(f"不支持的结果类型: {result_type}")

            logger.info(f"结果保存成功: {result_id}.{result_type}")
            return str(result_file)
            
        except Exception as e:
            logger.error(f"保存结果失败: {str(e)}", exc_info=True)
            raise FileHandlingError(f"保存结果失败: {str(e)}")
    async def get_save_result(self) -> str:
        return self.results_dir
    # async def save_result(self, result_id: str, data: any, result_type: str) -> str:
    #     """保存计算结果"""
    #     try:
    #         result_file = self.results_dir / f"{result_id}.{result_type}"
            
    #         if result_type == "json":
    #             async with aiofiles.open(result_file, 'w') as f:
    #                 await f.write(json.dumps(data, indent=2, ensure_ascii=False))
    #         elif result_type == "csv":
    #             if hasattr(data, 'to_csv'):  # pandas DataFrame
    #                 data.to_csv(result_file, index=False)
    #             else:
    #                 raise ValueError("CSV格式需要pandas DataFrame")
    #         elif result_type == "npy":
    #             import numpy as np
    #             np.save(result_file, data)
    #         elif result_type == "pkl":
    #             import pickle
    #             with open(result_file, 'wb') as f:
    #                 pickle.dump(data, f)
    #         else:
    #             raise ValueError(f"不支持的结果类型: {result_type}")
            
    #         logger.info(f"结果保存成功: {result_id}.{result_type}")
    #         return str(result_file)
            
    #     except Exception as e:
    #         logger.error(f"保存结果失败: {str(e)}")
    #         raise FileHandlingError(f"保存结果失败: {str(e)}")
    
    async def load_result(self, result_id: str, result_type: str) -> any:
        """加载计算结果"""
        try:
            result_file = self.results_dir / f"{result_id}.{result_type}"
            
            if not result_file.exists():
                return None
            
            if result_type == "json":
                async with aiofiles.open(result_file, 'r') as f:
                    content = await f.read()
                    return json.loads(content)
            elif result_type == "csv":
                return pd.read_csv(result_file)
            elif result_type == "npy":
                import numpy as np
                return np.load(result_file)
            elif result_type == "pkl":
                import pickle
                with open(result_file, 'rb') as f:
                    return pickle.load(f)
            else:
                raise ValueError(f"不支持的结果类型: {result_type}")
                
        except Exception as e:
            logger.error(f"加载结果失败: {str(e)}")
            return None
    
    async def cleanup_old_files(self, days: int = 7):
        """清理旧文件"""
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
            
            for directory in [self.upload_dir, self.results_dir]:
                for file_path in directory.iterdir():
                    if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        logger.info(f"清理旧文件: {file_path}")
                        
        except Exception as e:
            logger.error(f"清理文件失败: {str(e)}")
    
    async def get_disk_usage(self) -> Dict[str, float]:
        """获取磁盘使用情况"""
        try:
            upload_size = sum(f.stat().st_size for f in self.upload_dir.rglob('*') if f.is_file())
            results_size = sum(f.stat().st_size for f in self.results_dir.rglob('*') if f.is_file())
            
            return {
                "upload_dir_size_mb": upload_size / (1024 * 1024),
                "results_dir_size_mb": results_size / (1024 * 1024),
                "total_size_mb": (upload_size + results_size) / (1024 * 1024)
            }
        except Exception as e:
            logger.error(f"获取磁盘使用情况失败: {str(e)}")
            return {"error": str(e)}

# 全局文件处理器实例
file_handler = FileHandler()