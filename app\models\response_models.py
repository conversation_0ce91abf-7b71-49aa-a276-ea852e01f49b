from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ResponseStatus(str, Enum):
    """响应状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"

# 基础响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    status: ResponseStatus = Field(..., description="响应状态")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    request_id: Optional[str] = Field(None, description="请求ID")

class ErrorResponse(BaseResponse):
    """错误响应模型"""
    error_code: Optional[str] = Field(None, description="错误代码")
    error_details: Optional[Dict[str, Any]] = Field(None, description="错误详情")

# 文件相关响应
class FileUploadResponse(BaseResponse):
    """文件上传响应"""
    file_id: str = Field(..., description="文件ID")
    file_name: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    file_type: str = Field(..., description="文件类型")
    upload_path: str = Field(..., description="上传路径")

class FileListResponse(BaseResponse):
    """文件列表响应"""
    files: List[Dict[str, Any]] = Field(..., description="文件列表")
    total_count: int = Field(..., description="文件总数")

# 任务相关响应
class TaskResponse(BaseResponse):
    """任务响应"""
    task_id: str = Field(..., description="任务ID")
    task_status: TaskStatus = Field(..., description="任务状态")
    progress: float = Field(default=0.0, ge=0.0, le=100.0, description="进度百分比")
    eta: Optional[int] = Field(None, description="预计剩余时间(秒)")

class TaskStatusResponse(BaseResponse):
    """任务状态响应"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    progress: float = Field(..., description="进度百分比")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    duration: Optional[float] = Field(None, description="执行时长(秒)")
    result: Optional[Dict[str, Any]] = Field(None, description="任务结果")
    error_message: Optional[str] = Field(None, description="错误信息")

# 算法结果响应
class GeneMiningResponse(BaseResponse):
    """基因挖掘响应"""
    task_id: str = Field(..., description="任务ID")
    afs_features_id: Optional[str] = Field(None, description="AFS特征数据ID")
    soap_features_id: Optional[str] = Field(None, description="SOAP特征数据ID")
    afs_download_url: Optional[str] = Field(  # 新增字段
        None, 
        description="AFS特征数据下载地址(需要时拼接下载地址)"
    )
    soap_download_url: Optional[str] = Field(  # 新增字段
        None,
        description="SOAP特征数据下载地址"
    )
    processed_samples: int = Field(..., description="处理样本数")
    feature_dimensions: Dict[str, int] = Field(..., description="特征维度信息")
    @validator('feature_dimensions', pre=True)
    def convert_tuples(cls, v):
        return {k: _handle_dim(v[k]) for k in v}
def _handle_dim(value):
    """自动转换 (952,) -> 952 这类数据"""
    if isinstance(value, tuple):
        if len(value) == 1:
            return value[0]
        elif len(value) == 2:
            return value[1]  # 默认取第二维度
        else:
            raise ValueError("无法处理的维度格式")
    return value
class GeneIdentificationResponse(BaseModel):
    status: str
    message: str
    task_id: str
    
    cgcl_features_id: Optional[str] = None
    egcl_features_id: Optional[str] = None
    cgcl_download_url: Optional[str] = None
    egcl_download_url: Optional[str] = None
    # 添加可视化图片下载路径字段
    cgcl_png_download_url: Optional[str] = None
    egcl_png_download_url: Optional[str] = None
    feature_dimensions: Dict[str, int] = Field(..., description="特征维度")
    fused_features_id: Optional[str] = None
    @validator('feature_dimensions', pre=True)
    def convert_tuples(cls, v):
        return {k: _handle_dim(v[k]) for k in v}

class GeneFusionResponse(BaseResponse):
    """基因融合响应"""
    status: str
    message: str
    task_id: str
    fused_features_id: str = Field(..., description="融合特征ID")
    fusion_dimension: int = Field(..., description="融合特征维度")
    clip_download_url: Optional[str] = None
    # 添加可视化图片下载路径字段
    clip_png_download_url: Optional[str] = None
    final_result: List[Dict[str, Any]] = Field(..., description="最终结果")
    performance_metrics: Dict[str, float] = Field(..., description="性能指标")
    cif_files_server_local: Dict[str, str] = Field(..., description="CIF文件服务器本地路径")
    #fused_features: List[List[float]]
class GeneEditingResponse(BaseResponse):
    """基因编辑响应"""
    status: str
    message: str
    task_id: str
    model_id: Optional[str] = None
    #classification_accuracy: Optional[float] = None
    edit_3d_png_path: Optional[str] = None
    edit_2d_png_path: Optional[str] = None
    model_parameters: Optional[Dict[str, Any]] = Field(None, description="模型参数")
class GeneDesignResponse(BaseResponse):
    """基因设计响应"""
    status: str
    message: str
    task_id: str
    optimal_value: float = Field(..., description="最优目标值")
    optimal_parameters: List[float] = Field(..., description="最优参数")
    convergence_iterations: int = Field(..., description="收敛迭代次数")
    optimization_history: Dict[str, Any] = Field(..., description="优化历史")

# 可视化响应
class VisualizationResponse(BaseResponse):
    """可视化响应"""
    task_id: str = Field(..., description="任务ID")
    visualization_type: str = Field(..., description="可视化类型")
    image_urls: List[str] = Field(..., description="图片URL列表")
    interactive_data: Optional[Dict[str, Any]] = Field(None, description="交互式数据")

# 完整流程响应
class FullPipelineResponse(BaseResponse):
    """完整流程响应"""
    task_id: str = Field(..., description="主任务ID")
    sub_tasks: List[str] = Field(..., description="子任务ID列表")
    overall_progress: float = Field(..., description="总体进度")
    current_stage: str = Field(..., description="当前阶段")
    stage_results: Dict[str, Any] = Field(..., description="各阶段结果")

# 统计信息响应
class StatisticsResponse(BaseResponse):
    """统计信息响应"""
    file_count: int = Field(..., description="文件数量")
    processed_samples: int = Field(..., description="已处理样本")
    feature_dimensions: int = Field(..., description="特征维度")
    model_accuracy: float = Field(..., description="模型精度")
    active_tasks: int = Field(..., description="活跃任务数")
    completed_tasks: int = Field(..., description="完成任务数")

# 模型信息响应
class ModelInfoResponse(BaseResponse):
    """模型信息响应"""
    model_type: str = Field(..., description="模型类型")
    model_status: str = Field(..., description="模型状态")
    model_path: str = Field(..., description="模型路径")
    model_size: Optional[int] = Field(None, description="模型大小(字节)")
    last_loaded: Optional[datetime] = Field(None, description="最后加载时间")
    performance_metrics: Optional[Dict[str, float]] = Field(None, description="性能指标")

# 批处理响应
class BatchProcessResponse(BaseResponse):
    """批处理响应"""
    batch_id: str = Field(..., description="批处理ID")
    total_requests: int = Field(..., description="总请求数")
    completed_requests: int = Field(..., description="完成请求数")
    failed_requests: int = Field(..., description="失败请求数")
    individual_results: List[Dict[str, Any]] = Field(..., description="单个结果列表")

# 系统状态响应
class SystemStatusResponse(BaseResponse):
    """系统状态响应"""
    api_version: str = Field(..., description="API版本")
    system_status: str = Field(..., description="系统状态")
    uptime: float = Field(..., description="运行时间(秒)")
    memory_usage: Dict[str, float] = Field(..., description="内存使用情况")
    disk_usage: Dict[str, float] = Field(..., description="磁盘使用情况")
    active_connections: int = Field(..., description="活跃连接数")
    loaded_models: List[str] = Field(..., description="已加载模型列表")

# 数据分析响应
class DataAnalysisResponse(BaseResponse):
    """数据分析响应"""
    analysis_id: str = Field(..., description="分析ID")
    data_summary: Dict[str, Any] = Field(..., description="数据摘要")
    statistical_measures: Dict[str, float] = Field(..., description="统计量")
    data_quality: Dict[str, Any] = Field(..., description="数据质量评估")
    recommendations: List[str] = Field(..., description="建议列表")

# 导出响应
class ExportResponse(BaseResponse):
    """导出响应"""
    export_id: str = Field(..., description="导出ID")
    export_format: str = Field(..., description="导出格式")
    file_url: str = Field(..., description="文件下载URL")
    file_size: int = Field(..., description="文件大小(字节)")
    expiry_time: datetime = Field(..., description="过期时间")

# 配置响应
class ConfigResponse(BaseResponse):
    """配置响应"""
    config_type: str = Field(..., description="配置类型")
    current_config: Dict[str, Any] = Field(..., description="当前配置")
    default_config: Dict[str, Any] = Field(..., description="默认配置")
    config_schema: Dict[str, Any] = Field(..., description="配置模式")

# 分页响应基类
class PaginatedResponse(BaseResponse):
    """分页响应基类"""
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小") 
    total_pages: int = Field(..., description="总页数")
    total_items: int = Field(..., description="总项目数")
    has_next: bool = Field(..., description="是否有下一页")
    has_previous: bool = Field(..., description="是否有上一页")