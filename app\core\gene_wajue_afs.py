###############特征提取##############################
import pandas as pd
import re
from functools import reduce
import numpy as np
###################依据formula返回 元素字典或元素列表
def mement_formula(formula):
    """
    :param formula:传入的化学式
    :return:返回一个字典{key=》元素，value=》计量数}
    """
    # 使用正则表达式匹配元素符号和其后的计量数
    matches = re.findall(r'([A-Z][a-z]?)(\d*\.?\d*)', formula)
    # 处理匹配结果，将计量数转换为 float，默认为 1.0
    elements_with_stoichiometry = {match[0]: float(match[1]) if match[1] else 1.0 for match in matches}

    return elements_with_stoichiometry
def mement_formula_list(formula):
    """
    :param formula:传入的化学式
    :return: 返回一个列表，包含化学式的元素
    """
    # 使用正则表达式匹配元素符号和其后的计量数
    matches = re.findall(r'([A-Z][a-z]?)', formula)
    # 处理匹配结果，将计量数转换为 float，默认为 1.0
    elements_with_stoichiometry = {match for match in matches}

    return elements_with_stoichiometry
##################定义特征提取的方法###############
def process_elements_avg(elements_dict):
    total = sum(elements_dict.values())
    return {key: value / total for key, value in elements_dict.items()}
def process_elements_min(elements_dict):
    min_element = min(elements_dict, key=elements_dict.get)
    return {min_element: elements_dict[min_element]}
def process_elements_max(elements_dict):
    max_element = max(elements_dict, key=elements_dict.get)
    return {max_element: elements_dict[max_element]}
def process_elements_reduce(elements_dict):
    product = reduce((lambda x, y: x * y), elements_dict.values())
    if product < 1e-10:
        return {key: np.nan for key in elements_dict}
    else:
        return {key: value / product for key, value in elements_dict.items()}
##################依据csv文件 产生双层字典 记录每个元素对应的特征的值#################
def csv_to_nested_dict_corrected(csv_file):
    df=pd.read_csv(csv_file)
    # 获取第一列的列名，假设它包含元素名
    element_col_name = df.columns[0]
    # 初始化双层字典
    nested_dict = {}
    # 遍历 DataFrame，填充双层字典
    for row in df.itertuples():
        element_name = getattr(row, element_col_name)
        if element_name not in nested_dict:
            nested_dict[element_name] = {}
        # 遍历每一列，添加可计算的列
        for col_name in df.columns[1:]:  # 跳过第一列（元素名）
            value = df.loc[row.Index, col_name]
            if isinstance(value, (np.int64, np.float64)):  # 确保列值是可计算的类型
                nested_dict[element_name][col_name] = value
    return nested_dict
############定义特征文件和特征提取方法字典#####################

features_file_path_list = ["magpie.csv", "mendeleev.csv", "villars.csv"]
features_method_dict = {"avg": process_elements_avg, "min": process_elements_min, "max": process_elements_max,
                        "reduce": process_elements_reduce, "range": ""}

#################依据传入的formulas，特征文件，提取方法来进行特征提取########################
def calculate_features_for_all_formulas(formulas
                                        , features_csv
                                        , method
                                        ,features_method_dict=features_method_dict):
    """
    :param formulas:化学式csv文件
    :param features_csv: 待提取的特征文件
    :param method: 提取特征的方法
    :return: 返回的是一个计算特征后的dataframe
    """
    # 读取化学式数据
    formulas_df = formulas
    # 使用 csv_to_nested_dict_corrected 函数将特征 CSV 转换为双层字典
    features_dict = csv_to_nested_dict_corrected(features_csv)
    # 存储每个化学式的特征计算结果
    results = []
    if method != "range":
        for formula in formulas_df['Formula']:
            #print("formula",formula)
            # 从化学式中提取元素及其计量数
            elements_dict = mement_formula(formula)
            #print("elements_dict",elements_dict)
            elements_dict = features_method_dict[method](elements_dict)
            #print("after elements_dict",elements_dict)
            # 初始化存储该化学式特征计算结果的字典
            formula_features = {'formula': formula}
            # 遍历每个特征进行计算
            #print("featurs",features_dict['H'].keys())
            for feature in features_dict["H"].keys():
                # 计算当前特征的加权平均值，权重为元素在化学式中的计量数
                feature_value_sum = 0
                for element, amount in elements_dict.items():
                    if element in features_dict and feature in features_dict[element]:
                        feature_value_sum += features_dict[element][feature] * amount

                # 保存计算结果
                formula_features[feature] = feature_value_sum

                # 将当前化学式的特征计算结果添加到结果列表中
            #(formula_features)
            results.append(formula_features)
    else:
        for formula in formulas_df['Formula']:
            # 从化学式中提取元素及其计量数
            elements_dict = mement_formula_list(formula)
            # 初始化存储该化学式特征计算结果的字典
            formula_features = {'formula': formula}

            # 遍历每个特征进行计算
            for feature in features_dict[next(iter(features_dict))].keys():  # 假设所有元素都有相同的特征集

                feature_value_list = [features_dict[i][feature] for i in elements_dict]
                # print(feature_value_list)
                range = max(feature_value_list) - min(feature_value_list)

                # 保存计算结果
                formula_features[feature] = range

                # 将当前化学式的特征计算结果添加到结果列表中
            results.append(formula_features)
    # 将结果列表转换为 DataFrame 并保存到 CSV 文件中
    results_df = pd.DataFrame(results)
    return results_df

