"""
优化的任务管理器
使用数据库存储任务状态，支持分布式部署和任务持久化
"""

import asyncio
import uuid
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from concurrent.futures import ThreadPoolExecutor
import psutil
import json

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select, update, delete

from app.database.models import TaskRecord, ResultRecord, SystemMetrics
from app.models.response_models import TaskStatus
from app.config import settings
from app.core.cache_manager import cache_manager

logger = logging.getLogger(__name__)

class OptimizedTaskManager:
    """优化的异步任务管理器"""

    def __init__(self, db_url: str = None, max_workers: int = None):
        # 数据库连接
        self.db_url = db_url or "sqlite+aiosqlite:///./tasks.db"
        self.engine = create_async_engine(self.db_url, echo=False)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )

        # 线程池配置
        self.max_workers = max_workers or min(32, (psutil.cpu_count() or 1) + 4)
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)

        # 运行时任务跟踪
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_callbacks: Dict[str, Callable] = {}

        # 性能监控
        self.metrics = {
            'tasks_created': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'average_execution_time': 0.0
        }

        # 启动清理任务
        asyncio.create_task(self._periodic_cleanup())
        asyncio.create_task(self._collect_system_metrics())

    async def create_task(
        self,
        task_type: str,
        task_func: Callable,
        priority: int = 0,
        *args,
        **kwargs
    ) -> str:
        """
        创建并启动异步任务

        Args:
            task_type: 任务类型
            task_func: 任务执行函数
            priority: 任务优先级（数字越大优先级越高）
            *args, **kwargs: 传递给任务函数的参数

        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())

        # 创建数据库记录
        async with self.async_session() as session:
            task_record = TaskRecord(
                id=task_id,
                task_type=task_type,
                status=TaskStatus.PENDING.value,
                metadata={
                    'priority': priority,
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys()),
                    **kwargs.get('metadata', {})
                }
            )
            session.add(task_record)
            await session.commit()

        # 启动异步任务
        async_task = asyncio.create_task(
            self._execute_task_with_monitoring(task_id, task_func, *args, **kwargs)
        )
        self.running_tasks[task_id] = async_task

        # 更新指标
        self.metrics['tasks_created'] += 1

        logger.info(f"任务创建成功: {task_id} ({task_type}), 优先级: {priority}")
        return task_id

    async def _execute_task_with_monitoring(
        self,
        task_id: str,
        task_func: Callable,
        *args,
        **kwargs
    ):
        """执行任务并监控性能"""
        start_time = time.time()

        try:
            # 更新任务状态为运行中
            await self._update_task_status(task_id, TaskStatus.RUNNING)

            logger.info(f"开始执行任务: {task_id}")

            # 包装进度回调
            def progress_callback(progress: float):
                asyncio.create_task(self._update_task_progress(task_id, progress))

            # 如果函数支持进度回调，添加回调参数
            if 'progress_callback' in task_func.__code__.co_varnames:
                kwargs['progress_callback'] = progress_callback

            # 执行任务
            if asyncio.iscoroutinefunction(task_func):
                result = await task_func(*args, **kwargs)
            else:
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    self.executor,
                    lambda: task_func(*args, **kwargs)
                )

            # 任务完成
            await self._update_task_status(task_id, TaskStatus.COMPLETED, progress=100.0)

            # 保存结果
            if result:
                await self._save_task_result(task_id, result)

            # 更新性能指标
            execution_time = time.time() - start_time
            self.metrics['tasks_completed'] += 1
            self._update_average_execution_time(execution_time)

            logger.info(f"任务执行完成: {task_id}, 耗时: {execution_time:.2f}s")

        except Exception as e:
            # 任务失败
            await self._update_task_status(
                task_id,
                TaskStatus.FAILED,
                error_message=str(e)
            )

            self.metrics['tasks_failed'] += 1
            logger.error(f"任务执行失败: {task_id} - {str(e)}")

        finally:
            # 清理运行中的任务引用
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            if task_id in self.task_callbacks:
                del self.task_callbacks[task_id]

    async def _update_task_status(
        self,
        task_id: str,
        status: TaskStatus,
        progress: Optional[float] = None,
        error_message: Optional[str] = None
    ):
        """更新任务状态"""
        async with self.async_session() as session:
            update_data = {'status': status.value}

            if progress is not None:
                update_data['progress'] = progress

            if error_message:
                update_data['error_message'] = error_message

            if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                update_data['end_time'] = datetime.now()

            await session.execute(
                update(TaskRecord)
                .where(TaskRecord.id == task_id)
                .values(**update_data)
            )
            await session.commit()

    async def _update_task_progress(self, task_id: str, progress: float):
        """更新任务进度"""
        async with self.async_session() as session:
            await session.execute(
                update(TaskRecord)
                .where(TaskRecord.id == task_id)
                .values(progress=min(max(progress, 0.0), 100.0))
            )
            await session.commit()

    async def _save_task_result(self, task_id: str, result: Any):
        """保存任务结果"""
        try:
            import pickle

            # 序列化结果
            result_data = pickle.dumps(result)

            # 选择存储方式（大于1MB的结果存储到文件）
            if len(result_data) > 1024 * 1024:  # 1MB
                # 大文件存储到磁盘
                file_path = f"results/{task_id}.pkl"
                with open(file_path, 'wb') as f:
                    f.write(result_data)
                result_data = None
            else:
                file_path = None

            async with self.async_session() as session:
                result_record = ResultRecord(
                    id=str(uuid.uuid4()),
                    task_id=task_id,
                    result_type="pkl",
                    result_data=result_data,
                    file_path=file_path
                )
                session.add(result_record)
                await session.commit()

        except Exception as e:
            logger.error(f"保存任务结果失败: {task_id} - {str(e)}")

    def _update_average_execution_time(self, execution_time: float):
        """更新平均执行时间"""
        completed = self.metrics['tasks_completed']
        if completed == 1:
            self.metrics['average_execution_time'] = execution_time
        else:
            # 使用指数移动平均
            alpha = 0.1
            self.metrics['average_execution_time'] = (
                alpha * execution_time +
                (1 - alpha) * self.metrics['average_execution_time']
            )