"""
基因设计API路由
提供贝叶斯优化的材料设计和性能优化功能
"""

from fastapi import APIRouter, HTTPException
from typing import List, Optional, Dict, Any
import logging
import time
import json,os,pickle
import numpy as np
from app.models.request_models import GeneDesignRequest
from app.models.response_models import (
    GeneDesignResponse, TaskResponse, ResponseStatus, TaskStatus
)
from app.services.task_manager import task_manager
from app.core.gene_algorithms import material_gene_algorithms
from app.services.file_handler import file_handler
from app.core.units_models import generate_optimization_gif,visualize_optimization
import datetime
logger = logging.getLogger(__name__)
router = APIRouter(prefix="/design", tags=["基因设计"])

@router.post("/start", response_model=TaskResponse)
async def start_gene_design(request: GeneDesignRequest):
    """
    启动基因设计任务
    
    使用贝叶斯优化在设计空间中搜索最优材料配置
    这是材料基因算法的最后一步，实现智能材料设计
    """
    try:
        logger.info(f"启动基因设计任务")
        
        # 验证SVM模型存在
        svc_model = await file_handler.load_result(request.svc_model_id, "pkl")
        if svc_model is None:
            raise HTTPException(
                status_code=404,
                detail=f"SVM模型不存在: {request.svc_model_id}"
            )
        
        # 验证搜索特征数据存在
        search_features = await file_handler.load_result(request.search_features, "csv")
        if search_features is None:
            search_features = await file_handler.load_result(request.search_features, "json")
        
        if search_features is None:
            raise HTTPException(
                status_code=404,
                detail=f"搜索特征数据不存在: {request.search_features}"
            )
        
        # 验证优化参数
        if request.n_calls < 50:
            raise HTTPException(
                status_code=400,
                detail="优化迭代次数不能少于50次"
            )
        
        
        # 创建异步任务
        task_id = await task_manager.create_task(
            task_type="gene_design",
            task_func=material_gene_algorithms.gene_design,
            svc_model_id=request.svc_model_id,
            search_features_id=request.search_features,
            n_calls=request.n_calls,
            target_value=request.target_value,
            metadata={
                "n_calls": request.n_calls,
                "svc_model_id": request.svc_model_id
            }
        )
        
        return TaskResponse(
            status=ResponseStatus.SUCCESS,
            message="基因设计任务已启动",
            task_id=task_id,
            task_status=TaskStatus.PENDING,
            progress=0.0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动基因设计任务失败: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"启动基因设计任务失败: {str(e)}"
        )

@router.get("/status/{task_id}")
async def get_design_status(task_id: str):
    """获取基因设计任务状态"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "gene_design":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "获取任务状态成功",
            "task_id": task_id,
            "status": task_info.status.value,
            "progress": task_info.progress,
            "start_time": task_info.start_time,
            "end_time": task_info.end_time,
            #"result": task_info.result if task_info.status == TaskStatus.COMPLETED else None,
            "error_message": task_info.error_message,
            "metadata": task_info.metadata
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设计状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")
@router.get("/result/{task_id}", response_model=GeneDesignResponse)
async def get_design_result(task_id: str):
    """获取基因设计结果"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "gene_design":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        if task_info.status != TaskStatus.COMPLETED:
            raise HTTPException(
                status_code=400, 
                detail=f"任务未完成，当前状态: {task_info.status.value}"
            )
        
        result = task_info.result
        target_value = result.get("target_value", 6.0)
        if not result:
            raise HTTPException(status_code=404, detail="任务结果不存在")
        
        # 保存优化历史到文件系统
        history_id = f"optimization_history_{task_id}"
        await file_handler.save_result(
            history_id, 
            result.get("optimization_history", {}), 
            "json"
        )
        #画图
        save_out_path = os.path.join(await file_handler.get_save_result(), task_id)
        os.makedirs(save_out_path,exist_ok=True)

        generate_optimization_gif(result.get("optimization_history", {}), result.get("class_1_points"),save_out_path,target=target_value,opt_type="range",gif_name="design_optimization.gif")
        visualize_optimization(result.get("optimization_history", {}),result.get("result"),result.get("class_1_points"),save_out_path,target=target_value,opt_type="range",img_name="design_optimization.png")
        def _convert_ndarray_to_list(obj):
            """
            Helper function: Recursively convert numpy arrays and scalar numpy types to Python native types.
            """
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: _convert_ndarray_to_list(val) for key, val in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [_convert_ndarray_to_list(x) for x in obj]
            elif isinstance(obj, np.generic):
                return obj.item()
            return obj
        result = _convert_ndarray_to_list(result)
        return GeneDesignResponse(
            status=ResponseStatus.SUCCESS,
            message="基因设计完成",
            task_id=task_id,
            optimal_value=result.get("optimal_value", 0.0),
            optimal_parameters=result.get("optimal_parameters", []),
            convergence_iterations=result.get("convergence_iterations", 0),
            optimization_history=result.get("optimization_history", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设计结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取结果失败: {str(e)}")

# @router.post("/continue_from_editing/{editing_task_id}")
# async def continue_from_editing(
#     editing_task_id: str,
#     n_calls: int = 200,
#     target_scenario: str = "min"
# ):
#     """
#     从基因编辑结果继续进行基因设计
    
#     这是一个便捷方法，可以直接使用基因编辑的SVM模型进行设计优化
#     """
#     try:
#         # 获取编辑任务结果
#         editing_task = task_manager.get_task_status(editing_task_id)
#         if not editing_task:
#             raise HTTPException(status_code=404, detail="基因编辑任务不存在")
        
#         if editing_task.task_type != "gene_editing":
#             raise HTTPException(status_code=400, detail="任务类型不是基因编辑")
        
#         if editing_task.status != TaskStatus.COMPLETED:
#             raise HTTPException(
#                 status_code=400, 
#                 detail="基因编辑任务未完成"
#             )
        
#         # 获取SVM模型ID
#         result = editing_task.result
#         if not result or not result.get("model_id"):
#             raise HTTPException(
#                 status_code=404,
#                 detail="编辑任务中没有找到训练好的模型"
#             )
        
#         svc_model_id = result["model_id"]
        
#         # 获取特征数据（通常使用编辑任务的输入特征）
#         # 这里假设特征数据ID存储在任务元数据中
#         metadata = editing_task.metadata or {}
#         search_features_id = metadata.get("features_data_id")
        
#         if not search_features_id:
#             # 如果没有找到，尝试从fusion任务中获取
#             fusion_task_id = metadata.get("continued_from_fusion")
#             if fusion_task_id:
#                 search_features_id = f"fusion_{fusion_task_id}"
#             else:
#                 raise HTTPException(
#                     status_code=400,
#                     detail="无法确定搜索特征数据源"
#                 )
        
#         # 创建设计任务
#         task_id = await task_manager.create_task(
#             task_type="gene_design",
#             task_func=material_gene_algorithms.gene_design,
#             svc_model_id=svc_model_id,
#             search_features_id=search_features_id,
#             n_calls=n_calls,
#             metadata={
#                 "target_scenario": target_scenario,
#                 "target_value": None,
#                 "n_calls": n_calls,
#                 "svc_model_id": svc_model_id,
#                 "continued_from_editing": editing_task_id
#             }
#         )
        
#         return TaskResponse(
#             status=ResponseStatus.SUCCESS,
#             message=f"基因设计任务已启动（继承自编辑任务 {editing_task_id}）",
#             task_id=task_id,
#             task_status=TaskStatus.PENDING,
#             progress=0.0
#         )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"从编辑结果继续设计失败: {e}")
#         raise HTTPException(
#             status_code=500, 
#             detail=f"从编辑结果继续设计失败: {str(e)}"
#         )

# @router.post("/multi_objective_optimization")
# async def multi_objective_optimization(
#     svc_model_ids: List[str],
#     search_features_id: str,
#     objectives: List[Dict[str, Any]],
#     n_calls: int = 200,
#     pareto_front: bool = True
# ):
#     """
#     多目标优化
    
#     同时优化多个材料性能指标，寻找帕累托最优解
#     """
#     try:
#         logger.info(f"启动多目标优化，目标数量: {len(objectives)}")
        
#         # 验证所有SVM模型
#         for model_id in svc_model_ids:
#             model = await file_handler.load_result(model_id, "pkl")
#             if model is None:
#                 raise HTTPException(
#                     status_code=404,
#                     detail=f"SVM模型不存在: {model_id}"
#                 )
        
#         # 验证搜索特征
#         search_features = await file_handler.load_result(search_features_id, "csv")
#         if search_features is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"搜索特征不存在: {search_features_id}"
#             )
        
#         # 验证目标配置
#         if len(objectives) != len(svc_model_ids):
#             raise HTTPException(
#                 status_code=400,
#                 detail="目标数量必须与SVM模型数量一致"
#             )
        
#         # 创建多目标优化任务
#         task_id = await task_manager.create_task(
#             task_type="multi_objective_optimization",
#             task_func=multi_objective_optimization_worker,
#             svc_model_ids=svc_model_ids,
#             search_features_id=search_features_id,
#             objectives=objectives,
#             n_calls=n_calls,
#             pareto_front=pareto_front,
#             metadata={
#                 "optimization_type": "multi_objective",
#                 "objective_count": len(objectives),
#                 "n_calls": n_calls,
#                 "pareto_front": pareto_front
#             }
#         )
        
#         return TaskResponse(
#             status=ResponseStatus.SUCCESS,
#             message="多目标优化任务已启动",
#             task_id=task_id,
#             task_status=TaskStatus.PENDING,
#             progress=0.0
#         )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"启动多目标优化失败: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"启动多目标优化失败: {str(e)}"
#         )

# async def multi_objective_optimization_worker(
#     svc_model_ids: List[str],
#     search_features_id: str,
#     objectives: List[Dict[str, Any]],
#     n_calls: int,
#     pareto_front: bool,
#     progress_callback=None
# ):
#     """多目标优化工作函数"""
#     try:
#         import numpy as np
#         from skopt import gp_minimize
#         from skopt.space import Real
#         from skopt.utils import use_named_args
        
#         # 加载模型和数据
#         models = []
#         for model_id in svc_model_ids:
#             model = await file_handler.load_result(model_id, "pkl")
#             models.append(model)
        
#         search_features = await file_handler.load_result(search_features_id, "csv")
        
#         import pandas as pd
#         if isinstance(search_features, dict):
#             features_df = pd.DataFrame(search_features)
#         else:
#             features_df = search_features
        
#         # 定义搜索空间（假设前384维是特征）
#         n_features = min(384, len(features_df.columns))
#         feature_bounds = []
        
#         for i in range(n_features):
#             feature_values = features_df.iloc[:, i].values
#             min_val = float(np.min(feature_values))
#             max_val = float(np.max(feature_values))
#             feature_bounds.append(Real(min_val, max_val, name=f'feature_{i}'))
        
#         # 多目标优化函数
#         def multi_objective_function(x):
#             x_array = np.array(x).reshape(1, -1)
            
#             # 计算所有目标
#             objective_values = []
#             for i, (model, objective) in enumerate(zip(models, objectives)):
#                 if hasattr(model, 'predict_proba'):
#                     prob = model.predict_proba(x_array)[0, 1]
#                     # 根据目标类型调整值
#                     if objective.get("type") == "maximize":
#                         objective_values.append(-prob)  # 负值用于最大化
#                     else:
#                         objective_values.append(prob)
#                 else:
#                     prediction = model.predict(x_array)[0]
#                     objective_values.append(prediction)
            
#             # 返回加权组合或主要目标
#             if len(objective_values) == 1:
#                 return objective_values[0]
#             else:
#                 # 简单加权组合
#                 weights = [obj.get("weight", 1.0) for obj in objectives]
#                 return sum(w * val for w, val in zip(weights, objective_values))
        
#         # 执行优化
#         optimization_result = gp_minimize(
#             func=multi_objective_function,
#             dimensions=feature_bounds,
#             n_calls=n_calls,
#             random_state=42,
#             callback=lambda result: progress_callback((len(result.x_iters) / n_calls) * 100) if progress_callback else None
#         )
        
#         # 如果需要帕累托前沿分析
#         pareto_solutions = []
#         if pareto_front and len(models) > 1:
#             # 评估所有试验点的多目标值
#             all_objectives = []
#             for x in optimization_result.x_iters:
#                 x_array = np.array(x).reshape(1, -1)
#                 obj_vals = []
#                 for model in models:
#                     if hasattr(model, 'predict_proba'):
#                         prob = model.predict_proba(x_array)[0, 1]
#                         obj_vals.append(prob)
#                     else:
#                         pred = model.predict(x_array)[0]
#                         obj_vals.append(pred)
#                 all_objectives.append(obj_vals)
            
#             # 找到帕累托最优解（简化版本）
#             all_objectives = np.array(all_objectives)
#             pareto_mask = np.ones(len(all_objectives), dtype=bool)
            
#             for i in range(len(all_objectives)):
#                 for j in range(len(all_objectives)):
#                     if i != j and np.all(all_objectives[j] >= all_objectives[i]) and np.any(all_objectives[j] > all_objectives[i]):
#                         pareto_mask[i] = False
#                         break
            
#             pareto_solutions = [
#                 {
#                     "parameters": optimization_result.x_iters[i],
#                     "objectives": all_objectives[i].tolist()
#                 }
#                 for i in range(len(all_objectives)) if pareto_mask[i]
#             ]
        
#         result = {
#             "optimal_parameters": optimization_result.x,
#             "optimal_value": optimization_result.fun,
#             "convergence_iterations": len(optimization_result.x_iters),
#             "optimization_history": {
#                 "x_iters": [x.tolist() for x in optimization_result.x_iters],
#                 "func_vals": optimization_result.func_vals.tolist(),
#                 "pareto_solutions": pareto_solutions
#             },
#             "multi_objective_summary": {
#                 "total_objectives": len(models),
#                 "pareto_front_size": len(pareto_solutions),
#                 "objective_names": [obj.get("name", f"Objective_{i}") for i, obj in enumerate(objectives)]
#             }
#         }
        
#         if progress_callback:
#             progress_callback(100.0)
        
#         return result
        
#     except Exception as e:
#         logger.error(f"多目标优化执行失败: {e}")
#         raise

# @router.post("/constraint_optimization")
# async def constraint_optimization(
#     svc_model_id: str,
#     search_features_id: str,
#     constraints: List[Dict[str, Any]],
#     n_calls: int = 200
# ):
#     """
#     约束优化
    
#     在满足特定约束条件下进行材料设计优化
#     """
#     try:
#         logger.info(f"启动约束优化，约束数量: {len(constraints)}")
        
#         # 验证模型和数据
#         model = await file_handler.load_result(svc_model_id, "pkl")
#         if model is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"SVM模型不存在: {svc_model_id}"
#             )
        
#         search_features = await file_handler.load_result(search_features_id, "csv")
#         if search_features is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"搜索特征不存在: {search_features_id}"
#             )
        
#         # 创建约束优化任务
#         task_id = await task_manager.create_task(
#             task_type="constraint_optimization",
#             task_func=constraint_optimization_worker,
#             svc_model_id=svc_model_id,
#             search_features_id=search_features_id,
#             constraints=constraints,
#             n_calls=n_calls,
#             metadata={
#                 "optimization_type": "constraint",
#                 "constraint_count": len(constraints),
#                 "n_calls": n_calls
#             }
#         )
        
#         return TaskResponse(
#             status=ResponseStatus.SUCCESS,
#             message="约束优化任务已启动",
#             task_id=task_id,
#             task_status=TaskStatus.PENDING,
#             progress=0.0
#         )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"启动约束优化失败: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"启动约束优化失败: {str(e)}"
#         )

# async def constraint_optimization_worker(
#     svc_model_id: str,
#     search_features_id: str,
#     constraints: List[Dict[str, Any]],
#     n_calls: int,
#     progress_callback=None
# ):
#     """约束优化工作函数"""
#     try:
#         import numpy as np
#         from skopt import gp_minimize
#         from skopt.space import Real
        
#         # 加载模型和数据
#         model = await file_handler.load_result(svc_model_id, "pkl")
#         search_features = await file_handler.load_result(search_features_id, "csv")
        
#         import pandas as pd
#         if isinstance(search_features, dict):
#             features_df = pd.DataFrame(search_features)
#         else:
#             features_df = search_features
        
#         # 定义搜索空间
#         n_features = min(384, len(features_df.columns))
#         feature_bounds = []
        
#         for i in range(n_features):
#             feature_values = features_df.iloc[:, i].values
#             min_val = float(np.min(feature_values))
#             max_val = float(np.max(feature_values))
#             feature_bounds.append(Real(min_val, max_val, name=f'feature_{i}'))
        
#         # 约束检查函数
#         def check_constraints(x):
#             x_array = np.array(x).reshape(1, -1)
            
#             for constraint in constraints:
#                 constraint_type = constraint.get("type")
#                 feature_index = constraint.get("feature_index")
#                 threshold = constraint.get("threshold")
                
#                 if constraint_type == "feature_range":
#                     min_val, max_val = threshold
#                     if not (min_val <= x[feature_index] <= max_val):
#                         return False
#                 elif constraint_type == "prediction_threshold":
#                     if hasattr(model, 'predict_proba'):
#                         prob = model.predict_proba(x_array)[0, 1]
#                         if prob < threshold:
#                             return False
#                     else:
#                         pred = model.predict(x_array)[0]
#                         if pred < threshold:
#                             return False
            
#             return True
        
#         # 目标函数（带约束惩罚）
#         def constrained_objective(x):
#             if not check_constraints(x):
#                 return 1e6  # 大惩罚值
            
#             x_array = np.array(x).reshape(1, -1)
#             if hasattr(model, 'predict_proba'):
#                 prob = model.predict_proba(x_array)[0, 1]
#                 return -prob  # 最大化概率
#             else:
#                 pred = model.predict(x_array)[0]
#                 return -pred
        
#         # 执行优化
#         optimization_result = gp_minimize(
#             func=constrained_objective,
#             dimensions=feature_bounds,
#             n_calls=n_calls,
#             random_state=42,
#             callback=lambda result: progress_callback((len(result.x_iters) / n_calls) * 100) if progress_callback else None
#         )
        
#         # 验证最优解是否满足约束
#         optimal_feasible = check_constraints(optimization_result.x)
        
#         result = {
#             "optimal_parameters": optimization_result.x,
#             "optimal_value": optimization_result.fun,
#             "feasible_solution": optimal_feasible,
#             "convergence_iterations": len(optimization_result.x_iters),
#             "constraint_summary": {
#                 "total_constraints": len(constraints),
#                 "constraint_types": [c.get("type") for c in constraints]
#             },
#             "optimization_history": {
#                 "x_iters": [x.tolist() for x in optimization_result.x_iters],
#                 "func_vals": optimization_result.func_vals.tolist()
#             }
#         }
        
#         if progress_callback:
#             progress_callback(100.0)
        
#         return result
        
#     except Exception as e:
#         logger.error(f"约束优化执行失败: {e}")
#         raise

# @router.post("/design_space_exploration")
# async def design_space_exploration(
#     svc_model_id: str,
#     search_features_id: str,
#     exploration_strategy: str = "random",
#     n_samples: int = 1000
# ):
#     """
#     设计空间探索
    
#     系统性地探索材料设计空间，发现有趣的区域
#     """
#     try:
#         logger.info(f"启动设计空间探索，策略: {exploration_strategy}")
        
#         # 验证输入
#         model = await file_handler.load_result(svc_model_id, "pkl")
#         if model is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"SVM模型不存在: {svc_model_id}"
#             )
        
#         search_features = await file_handler.load_result(search_features_id, "csv")
#         if search_features is None:
#             raise HTTPException(
#                 status_code=404,
#                 detail=f"搜索特征不存在: {search_features_id}"
#             )
        
#         # 创建探索任务
#         task_id = await task_manager.create_task(
#             task_type="design_space_exploration",
#             task_func=design_space_exploration_worker,
#             svc_model_id=svc_model_id,
#             search_features_id=search_features_id,
#             exploration_strategy=exploration_strategy,
#             n_samples=n_samples,
#             metadata={
#                 "exploration_type": exploration_strategy,
#                 "n_samples": n_samples
#             }
#         )
        
#         return TaskResponse(
#             status=ResponseStatus.SUCCESS,
#             message="设计空间探索任务已启动",
#             task_id=task_id,
#             task_status=TaskStatus.PENDING,
#             progress=0.0
#         )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"启动设计空间探索失败: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"启动设计空间探索失败: {str(e)}"
#         )

# async def design_space_exploration_worker(
#     svc_model_id: str,
#     search_features_id: str,
#     exploration_strategy: str,
#     n_samples: int,
#     progress_callback=None
# ):
#     """设计空间探索工作函数"""
#     try:
#         import numpy as np
#         import pandas as pd
#         from sklearn.cluster import KMeans
#         from sklearn.decomposition import PCA
        
#         # 加载模型和数据
#         model = await file_handler.load_result(svc_model_id, "pkl")
#         search_features = await file_handler.load_result(search_features_id, "csv")
        
#         if isinstance(search_features, dict):
#             features_df = pd.DataFrame(search_features)
#         else:
#             features_df = search_features
        
#         # 生成探索样本
#         n_features = min(384, len(features_df.columns))
        
#         if exploration_strategy == "random":
#             # 随机采样
#             samples = []
#             for i in range(n_features):
#                 feature_values = features_df.iloc[:, i].values
#                 min_val = np.min(feature_values)
#                 max_val = np.max(feature_values)
#                 random_samples = np.random.uniform(min_val, max_val, n_samples)
#                 samples.append(random_samples)
            
#             exploration_samples = np.array(samples).T
            
#         elif exploration_strategy == "grid":
#             # 网格采样（在高维空间中采用稀疏网格）
#             n_points_per_dim = max(2, int(n_samples ** (1/min(n_features, 10))))
#             grid_samples = []
            
#             for i in range(min(10, n_features)):  # 限制维度数量
#                 feature_values = features_df.iloc[:, i].values
#                 min_val = np.min(feature_values)
#                 max_val = np.max(feature_values)
#                 grid_points = np.linspace(min_val, max_val, n_points_per_dim)
#                 grid_samples.append(grid_points)
            
#             # 创建网格
#             from itertools import product
#             grid_combinations = list(product(*grid_samples[:min(5, len(grid_samples))]))
            
#             # 如果网格点太多，随机选择
#             if len(grid_combinations) > n_samples:
#                 selected_indices = np.random.choice(
#                     len(grid_combinations), 
#                     n_samples, 
#                     replace=False
#                 )
#                 grid_combinations = [grid_combinations[i] for i in selected_indices]
            
#             # 扩展到完整特征空间
#             exploration_samples = np.zeros((len(grid_combinations), n_features))
#             for i, combo in enumerate(grid_combinations):
#                 exploration_samples[i, :len(combo)] = combo
#                 # 对剩余特征使用均值
#                 for j in range(len(combo), n_features):
#                     exploration_samples[i, j] = np.mean(features_df.iloc[:, j].values)
            
#         else:  # latin_hypercube
#             from sklearn.utils import check_random_state
#             from scipy.stats import norm
            
#             # 拉丁超立方采样
#             rng = check_random_state(42)
#             samples = rng.random((n_samples, n_features))
            
#             # 缩放到特征范围
#             exploration_samples = np.zeros_like(samples)
#             for i in range(n_features):
#                 feature_values = features_df.iloc[:, i].values
#                 min_val = np.min(feature_values)
#                 max_val = np.max(feature_values)
#                 exploration_samples[:, i] = min_val + samples[:, i] * (max_val - min_val)
        
#         # 评估所有样本
#         predictions = []
#         probabilities = []
        
#         batch_size = 100  # 批处理以避免内存问题
#         for i in range(0, len(exploration_samples), batch_size):
#             batch = exploration_samples[i:i+batch_size]
            
#             if hasattr(model, 'predict_proba'):
#                 batch_proba = model.predict_proba(batch)
#                 probabilities.extend(batch_proba[:, 1].tolist())
#                 batch_pred = (batch_proba[:, 1] > 0.5).astype(int)
#             else:
#                 batch_pred = model.predict(batch)
            
#             predictions.extend(batch_pred.tolist())
            
#             if progress_callback:
#                 progress = min(((i + batch_size) / len(exploration_samples)) * 100, 100)
#                 progress_callback(progress)
        
#         # 分析探索结果
#         predictions = np.array(predictions)
#         probabilities = np.array(probabilities) if probabilities else None
        
#         # 聚类分析
#         n_clusters = min(10, len(exploration_samples) // 50)
#         if n_clusters >= 2:
#             # 使用PCA降维进行聚类
#             pca = PCA(n_components=min(10, n_features))
#             samples_pca = pca.fit_transform(exploration_samples)
            
#             kmeans = KMeans(n_clusters=n_clusters, random_state=42)
#             cluster_labels = kmeans.fit_predict(samples_pca)
            
#             # 分析每个聚类的性能
#             cluster_analysis = []
#             for cluster_id in range(n_clusters):
#                 cluster_mask = cluster_labels == cluster_id
#                 cluster_predictions = predictions[cluster_mask]
#                 cluster_probas = probabilities[cluster_mask] if probabilities is not None else None
                
#                 cluster_info = {
#                     "cluster_id": int(cluster_id),
#                     "sample_count": int(np.sum(cluster_mask)),
#                     "positive_ratio": float(np.mean(cluster_predictions)),
#                     "avg_probability": float(np.mean(cluster_probas)) if cluster_probas is not None else None,
#                     "cluster_center": kmeans.cluster_centers_[cluster_id].tolist()
#                 }
#                 cluster_analysis.append(cluster_info)
#         else:
#             cluster_analysis = []
        
#         # 找到最有希望的区域
#         if probabilities is not None:
#             top_indices = np.argsort(probabilities)[-min(100, len(probabilities)):]
#             promising_regions = exploration_samples[top_indices].tolist()
#             promising_probabilities = probabilities[top_indices].tolist()
#         else:
#             positive_indices = np.where(predictions == 1)[0]
#             top_indices = positive_indices[:min(100, len(positive_indices))]
#             promising_regions = exploration_samples[top_indices].tolist()
#             promising_probabilities = [1.0] * len(promising_regions)
        
#         result = {
#             "exploration_summary": {
#                 "total_samples": len(exploration_samples),
#                 "positive_predictions": int(np.sum(predictions)),
#                 "positive_ratio": float(np.mean(predictions)),
#                 "avg_probability": float(np.mean(probabilities)) if probabilities is not None else None
#             },
#             "cluster_analysis": cluster_analysis,
#             "promising_regions": {
#                 "coordinates": promising_regions,
#                 "probabilities": promising_probabilities,
#                 "count": len(promising_regions)
#             },
#             "exploration_strategy": exploration_strategy,
#             "feature_importance": {
#                 "analyzed_features": n_features,
#                 "pca_explained_variance": pca.explained_variance_ratio_.tolist() if 'pca' in locals() else []
#             }
#         }
        
#         if progress_callback:
#             progress_callback(100.0)
        
#         return result
        
#     except Exception as e:
#         logger.error(f"设计空间探索执行失败: {e}")
#         raise

@router.delete("/cancel/{task_id}")
async def cancel_design_task(task_id: str):
    """取消基因设计任务"""
    try:
        success = await task_manager.cancel_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=400, 
                detail="任务取消失败，可能任务不存在或已完成"
            )
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "基因设计任务已取消",
            "task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

@router.get("/optimization_history/{task_id}")
async def get_optimization_history(task_id: str):
    """获取优化历史详情"""
    try:
        # 加载优化历史数据
        history_id = f"optimization_history_{task_id}"
        history_data = await file_handler.load_result(history_id, "json")
        
        if history_data is None:
            raise HTTPException(
                status_code=404,
                detail="优化历史数据不存在"
            )
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "获取优化历史成功",
            "task_id": task_id,
            "history": history_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取优化历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取优化历史失败: {str(e)}")
import json  
import numpy as np
import io
from fastapi.responses import StreamingResponse
import zipfile
@router.get("/download/{task_id}", 
           summary="下载特征数据文件",
           description="下载基因设计数据文件")
async def download_features(task_id: str):

    try:
        history_id = f"optimization_history_{task_id}"
        result_df = await file_handler.load_result(history_id, "json")
        json_data = json.dumps(result_df, indent=2).encode("utf-8")
        return StreamingResponse(
            io.BytesIO(json_data),
            media_type="application/json",
            headers={
                "Content-Disposition": f"attachment; filename=fusion_results_{task_id}.json"
            }
        )

    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="未找到对应的特征文件")
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail="文件下载服务异常")
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse

# 🔍 可视化图像下载接口
@router.get("/gif/{task_id}")
async def get_tsne_image(task_id: str):
    """
    获取与指定 task_id 对应的优化轨迹 GIF 动图
    
    - **task_id**: 任务唯一标识符，如 "task_12345"
    """
    try:
        save_out_path = await file_handler.get_save_result()
        save_out_path = os.path.join(save_out_path, task_id)
        image_path = os.path.join(save_out_path, "design_optimization.gif")

        if not os.path.exists(image_path):
            raise HTTPException(status_code=404, detail="图像未找到，请先生成可视化结果")

        return FileResponse(
            image_path,
            media_type='image/gif',
            filename=f"design_optimization_{task_id}.gif"
        )
    
    except Exception as e:
        logger.error(f"获取 GIF 图像失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取图像失败")

@router.get("/png/{task_id}")
async def get_tsne_image(task_id: str):
    

    # 构建图像路径
    save_out_path =await file_handler.get_save_result()
    save_out_path = os.path.join(save_out_path, task_id)
    image_path = os.path.join(save_out_path, f"design_optimization.png")

    if not os.path.exists(image_path):
        raise HTTPException(status_code=404, detail="图像未找到，请先生成可视化结果")

    return FileResponse(image_path, media_type='image/png', filename=f"fdesign_optimization.png")