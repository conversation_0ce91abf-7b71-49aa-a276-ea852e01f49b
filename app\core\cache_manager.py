"""
高效缓存管理器
实现多层缓存策略，减少重复计算，提高系统性能
"""

import hashlib
import pickle
import time
import asyncio
from typing import Any, Optional, Dict, Callable, Union
from functools import wraps
from collections import OrderedDict
import logging
import redis
from pathlib import Path

logger = logging.getLogger(__name__)

class LRUCache:
    """内存LRU缓存实现"""

    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = OrderedDict()
        self.access_times = {}

    def get(self, key: str) -> Optional[Any]:
        if key in self.cache:
            # 更新访问时间，移到末尾
            self.cache.move_to_end(key)
            self.access_times[key] = time.time()
            return self.cache[key]
        return None

    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        if key in self.cache:
            self.cache.move_to_end(key)
        else:
            if len(self.cache) >= self.max_size:
                # 移除最久未使用的项
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
                del self.access_times[oldest_key]

        self.cache[key] = value
        self.access_times[key] = time.time()

        # 如果设置了TTL，可以在后台清理过期项
        if ttl:
            asyncio.create_task(self._expire_key(key, ttl))

    async def _expire_key(self, key: str, ttl: int):
        """异步过期键"""
        await asyncio.sleep(ttl)
        if key in self.cache:
            del self.cache[key]
            del self.access_times[key]

    def delete(self, key: str):
        if key in self.cache:
            del self.cache[key]
            del self.access_times[key]

    def clear(self):
        self.cache.clear()
        self.access_times.clear()

    def size(self) -> int:
        return len(self.cache)

class CacheManager:
    """多层缓存管理器"""

    def __init__(self, redis_url: Optional[str] = None, memory_cache_size: int = 1000):
        # 内存缓存（L1）
        self.memory_cache = LRUCache(memory_cache_size)

        # Redis缓存（L2）
        self.redis_client = None
        if redis_url:
            try:
                self.redis_client = redis.from_url(redis_url, decode_responses=False)
                self.redis_client.ping()
                logger.info("Redis缓存连接成功")
            except Exception as e:
                logger.warning(f"Redis连接失败: {e}")

        # 文件缓存（L3）
        self.file_cache_dir = Path("cache")
        self.file_cache_dir.mkdir(exist_ok=True)

    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = f"{prefix}:{str(args)}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()

    async def get(self, key: str) -> Optional[Any]:
        """多层缓存获取"""
        # L1: 内存缓存
        value = self.memory_cache.get(key)
        if value is not None:
            logger.debug(f"内存缓存命中: {key}")
            return value

        # L2: Redis缓存
        if self.redis_client:
            try:
                redis_value = self.redis_client.get(key)
                if redis_value:
                    value = pickle.loads(redis_value)
                    # 回写到内存缓存
                    self.memory_cache.set(key, value)
                    logger.debug(f"Redis缓存命中: {key}")
                    return value
            except Exception as e:
                logger.warning(f"Redis获取失败: {e}")

        # L3: 文件缓存
        file_path = self.file_cache_dir / f"{key}.pkl"
        if file_path.exists():
            try:
                with open(file_path, 'rb') as f:
                    value = pickle.load(f)
                # 回写到上层缓存
                self.memory_cache.set(key, value)
                if self.redis_client:
                    try:
                        self.redis_client.setex(key, 3600, pickle.dumps(value))
                    except Exception:
                        pass
                logger.debug(f"文件缓存命中: {key}")
                return value
            except Exception as e:
                logger.warning(f"文件缓存读取失败: {e}")

        return None

    async def set(self, key: str, value: Any, ttl: int = 3600):
        """多层缓存设置"""
        # L1: 内存缓存
        self.memory_cache.set(key, value, ttl)

        # L2: Redis缓存
        if self.redis_client:
            try:
                self.redis_client.setex(key, ttl, pickle.dumps(value))
            except Exception as e:
                logger.warning(f"Redis设置失败: {e}")

        # L3: 文件缓存（异步写入）
        asyncio.create_task(self._write_file_cache(key, value))

    async def _write_file_cache(self, key: str, value: Any):
        """异步写入文件缓存"""
        try:
            file_path = self.file_cache_dir / f"{key}.pkl"
            with open(file_path, 'wb') as f:
                pickle.dump(value, f)
        except Exception as e:
            logger.warning(f"文件缓存写入失败: {e}")

    async def delete(self, key: str):
        """删除缓存"""
        self.memory_cache.delete(key)

        if self.redis_client:
            try:
                self.redis_client.delete(key)
            except Exception:
                pass

        file_path = self.file_cache_dir / f"{key}.pkl"
        if file_path.exists():
            file_path.unlink()

    def cache_result(self, prefix: str, ttl: int = 3600):
        """装饰器：缓存函数结果"""
        def decorator(func: Callable):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 生成缓存键
                cache_key = self._generate_key(prefix, *args, **kwargs)

                # 尝试从缓存获取
                cached_result = await self.get(cache_key)
                if cached_result is not None:
                    logger.info(f"缓存命中: {func.__name__}")
                    return cached_result

                # 执行函数
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)

                # 缓存结果
                await self.set(cache_key, result, ttl)
                logger.info(f"缓存设置: {func.__name__}")

                return result
            return wrapper
        return decorator

# 全局缓存管理器实例
cache_manager = CacheManager()