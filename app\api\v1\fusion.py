"""
基因融合API路由
提供CLIP模型的多模态特征融合功能
"""

from fastapi import APIRouter, HTTPException
from typing import List, Optional
import logging

from app.models.request_models import GeneFusionRequest
from app.models.response_models import (
    GeneFusionResponse, TaskResponse, ResponseStatus, TaskStatus
)
from app.services.task_manager import task_manager
from app.core.gene_algorithms import material_gene_algorithms
from app.services.file_handler import file_handler
from app.core.units_models import show_tsne_result
logger = logging.getLogger(__name__)
router = APIRouter(prefix="/fusion", tags=["基因融合"])

@router.post("/start", response_model=TaskResponse)
async def start_gene_fusion(request: GeneFusionRequest):
    """
    启动基因融合任务
    
    使用CLIP模型将晶体结构特征和材料性质数据进行多模态融合
    这是材料基因算法的第三步，创建统一的材料表示
    """
    try:
        logger.info(f"启动基因融合任务，处理 {len(request.cif_files)} 个CIF文件和CSV数据")
        
        # 验证CIF文件
        for file_id in request.cif_files:
            file_info = await file_handler.get_file_info(file_id)
            if not file_info:
                raise HTTPException(
                    status_code=404, 
                    detail=f"CIF文件不存在: {file_id}"
                )
            if file_info.get("file_type") != "cif":
                raise HTTPException(
                    status_code=400,
                    detail=f"文件类型错误，期望CIF文件: {file_id}"
                )
        
        # 验证CSV文件
        csv_file_info = await file_handler.get_file_info(request.csv_file)
        if not csv_file_info:
            raise HTTPException(
                status_code=404, 
                detail=f"CSV文件不存在: {request.csv_file}"
            )
        if csv_file_info.get("file_type") != "csv":
            raise HTTPException(
                status_code=400,
                detail="CSV文件类型错误"
            )
        
        # 创建异步任务
        task_id = await task_manager.create_task(
            task_type="gene_fusion",
            task_func=material_gene_algorithms.gene_fusion,
            cif_file_ids=request.cif_files,
            csv_file_id=request.csv_file,
            metadata={
                "cif_count": len(request.cif_files),
                "csv_file": request.csv_file,
                "use_clip": request.use_clip
            }
        )
        
        return TaskResponse(
            status=ResponseStatus.SUCCESS,
            message="基因融合任务已启动",
            task_id=task_id,
            task_status=TaskStatus.PENDING,
            progress=0.0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动基因融合任务失败: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"启动基因融合任务失败: {str(e)}"
        )

@router.get("/status/{task_id}")
async def get_fusion_status(task_id: str):
    """获取基因融合任务状态"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "gene_fusion":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "获取任务状态成功",
            "task_id": task_id,
            "status": task_info.status.value,
            "progress": task_info.progress,
            "start_time": task_info.start_time,
            "end_time": task_info.end_time,
            #"result": task_info.result if task_info.status == TaskStatus.COMPLETED else None,
            "error_message": task_info.error_message,
            "metadata": task_info.metadata
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取融合状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")
import os
import pandas as pd
@router.get("/result/{task_id}", response_model=GeneFusionResponse)
async def get_fusion_result(task_id: str):
    """获取基因融合结果"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "gene_fusion":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        if task_info.status != TaskStatus.COMPLETED:
            raise HTTPException(
                status_code=400, 
                detail=f"任务未完成，当前状态: {task_info.status.value}"
            )
        
        result = task_info.result
        if not result:
            raise HTTPException(status_code=404, detail="任务结果不存在")
        
        # 保存融合特征数据到文件系统
        fused_features_id = f"fusion_{task_id}"
        clip_download_url=await file_handler.save_result(
            fused_features_id, 
            result["fused_features"], 
            "csv"
        )
        save_out_path = os.path.join(await file_handler.get_save_result(), task_id)
        os.makedirs(save_out_path,exist_ok=True)
        clip_out_png_path= f"{save_out_path}/clip_tsne_result.png"
        png_data=result["fused_features"].iloc[:,:-1]
        show_tsne_result(png_data, clip_out_png_path)

        return GeneFusionResponse(
            status=ResponseStatus.SUCCESS,
            message="基因融合完成",
            task_id=task_id,
            fused_features_id=fused_features_id,
            clip_download_url=clip_download_url,
            clip_png_download_url=clip_out_png_path,
            fusion_dimension=result.get("fusion_dimension", 0),
            final_result=result.get("final_result", {}),
            performance_metrics=result.get("metrics", {}),
            cif_files_server_local=result.get("cif_files_server_local", {}),
            # fused_features=result.get("fused_features", pd.Dataframe()).to_list()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取融合结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取结果失败: {str(e)}")

# @router.post("/continue_from_identification/{identification_task_id}")
# async def continue_from_identification(
#     identification_task_id: str,
#     csv_file_id: str
# ):
#     """
#     从基因识别结果继续进行基因融合
    
#     这是一个便捷方法，可以直接使用基因识别的结果进行融合
#     """
#     try:
#         # 获取识别任务结果
#         identification_task = task_manager.get_task_status(identification_task_id)
#         if not identification_task:
#             raise HTTPException(status_code=404, detail="基因识别任务不存在")
        
#         if identification_task.task_type != "gene_identification":
#             raise HTTPException(status_code=400, detail="任务类型不是基因识别")
        
#         if identification_task.status != TaskStatus.COMPLETED:
#             raise HTTPException(
#                 status_code=400, 
#                 detail="基因识别任务未完成"
#             )
        
#         # 验证CSV文件
#         csv_file_info = await file_handler.get_file_info(csv_file_id)
#         if not csv_file_info or csv_file_info.get("file_type") != "csv":
#             raise HTTPException(
#                 status_code=400,
#                 detail="无效的CSV文件"
#             )
        
#         # 获取原始CIF文件列表
#         metadata = identification_task.metadata or {}
#         cif_file_ids = metadata.get("original_cif_files", [])
        
#         if not cif_file_ids:
#             raise HTTPException(
#                 status_code=400,
#                 detail="无法从识别任务中获取原始CIF文件信息"
#             )
        
#         # 创建融合任务
#         task_id = await task_manager.create_task(
#             task_type="gene_fusion",
#             task_func=material_gene_algorithms.gene_fusion,
#             cif_file_ids=cif_file_ids,
#             csv_file_id=csv_file_id,
#             metadata={
#                 "cif_count": len(cif_file_ids),
#                 "csv_file": csv_file_id,
#                 "use_clip": True,
#                 "continued_from_identification": identification_task_id
#             }
#         )
        
#         return TaskResponse(
#             status=ResponseStatus.SUCCESS,
#             message=f"基因融合任务已启动（继承自识别任务 {identification_task_id}）",
#             task_id=task_id,
#             task_status=TaskStatus.PENDING,
#             progress=0.0
#         )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"从识别结果继续融合失败: {e}")
#         raise HTTPException(
#             status_code=500, 
#             detail=f"从识别结果继续融合失败: {str(e)}"
#         )

# @router.post("/analyze_fusion_quality/{task_id}")
# async def analyze_fusion_quality(task_id: str):
#     """
#     分析融合质量
    
#     评估CLIP模型融合的效果和质量指标
#     """
#     try:
#         task_info = task_manager.get_task_status(task_id)
        
#         if not task_info or task_info.task_type != "gene_fusion":
#             raise HTTPException(status_code=404, detail="基因融合任务不存在")
        
#         if task_info.status != TaskStatus.COMPLETED:
#             raise HTTPException(status_code=400, detail="任务未完成")
        
#         result = task_info.result
#         if not result:
#             raise HTTPException(status_code=404, detail="任务结果不存在")
        
#         # 加载融合特征进行分析
#         fused_features_id = f"fusion_{task_id}"
#         fused_features = await file_handler.load_result(fused_features_id, "csv")
        
#         if fused_features is None:
#             raise HTTPException(status_code=404, detail="融合特征数据不存在")
        
#         # 特征质量分析
#         import numpy as np
#         import pandas as pd
        
#         if isinstance(fused_features, dict):
#             # 转换为DataFrame进行分析
#             fused_df = pd.DataFrame(fused_features)
#         else:
#             fused_df = fused_features
        
#         # 计算质量指标
#         quality_metrics = {
#             "feature_statistics": {
#                 "sample_count": len(fused_df),
#                 "feature_dimension": len(fused_df.columns) if not fused_df.empty else 0,
#                 "mean_values": fused_df.mean().to_dict() if not fused_df.empty else {},
#                 "std_values": fused_df.std().to_dict() if not fused_df.empty else {},
#                 "missing_values": fused_df.isnull().sum().to_dict() if not fused_df.empty else {}
#             },
#             "fusion_quality": {
#                 "matching_accuracy": result.get("matching_accuracy", 0.0),
#                 "contrast_loss": result.get("contrast_loss", 0.0),
#                 "feature_diversity": float(np.std(fused_df.std())) if not fused_df.empty else 0.0,
#                 "data_completeness": (1.0 - fused_df.isnull().sum().sum() / (len(fused_df) * len(fused_df.columns))) if not fused_df.empty else 0.0
#             },
#             "recommendations": []
#         }
        
#         # 生成建议
#         if quality_metrics["fusion_quality"]["matching_accuracy"] > 0.9:
#             quality_metrics["recommendations"].append("融合质量优秀，可以继续进行基因编辑")
#         elif quality_metrics["fusion_quality"]["matching_accuracy"] > 0.8:
#             quality_metrics["recommendations"].append("融合质量良好，建议检查数据质量后继续")
#         else:
#             quality_metrics["recommendations"].append("融合质量较低，建议检查输入数据或调整模型参数")
        
#         if quality_metrics["fusion_quality"]["contrast_loss"] > 0.1:
#             quality_metrics["recommendations"].append("对比损失较高，建议增加训练数据或调整模型")
        
#         if quality_metrics["fusion_quality"]["data_completeness"] < 0.95:
#             quality_metrics["recommendations"].append("数据完整性较低，建议检查数据预处理步骤")
        
#         return {
#             "status": ResponseStatus.SUCCESS,
#             "message": "融合质量分析完成",
#             "task_id": task_id,
#             "quality_analysis": quality_metrics
#         }
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"融合质量分析失败: {e}")
#         raise HTTPException(status_code=500, detail=f"融合质量分析失败: {str(e)}")

# @router.post("/optimize_fusion_parameters")
# async def optimize_fusion_parameters(
#     cif_file_ids: List[str],
#     csv_file_id: str,
#     parameter_ranges: dict = None
# ):
#     """
#     优化融合参数
    
#     通过网格搜索或贝叶斯优化寻找最佳的CLIP模型参数
#     """
#     try:
#         logger.info("启动融合参数优化")
        
#         # 验证输入文件
#         for file_id in cif_file_ids:
#             file_info = await file_handler.get_file_info(file_id)
#             if not file_info or file_info.get("file_type") != "cif":
#                 raise HTTPException(
#                     status_code=400,
#                     detail=f"无效的CIF文件: {file_id}"
#                 )
        
#         csv_file_info = await file_handler.get_file_info(csv_file_id)
#         if not csv_file_info or csv_file_info.get("file_type") != "csv":
#             raise HTTPException(
#                 status_code=400,
#                 detail="无效的CSV文件"
#             )
        
#         # 设置默认参数范围
#         if parameter_ranges is None:
#             parameter_ranges = {
#                 "embedding_size": [256, 384, 512],
#                 "n_layers": [2, 3, 4],
#                 "n_heads": [4, 6, 8],
#                 "learning_rate": [1e-4, 1e-3, 1e-2]
#             }
        
#         # 创建参数优化任务
#         task_id = await task_manager.create_task(
#             task_type="fusion_optimization",
#             task_func=optimize_fusion_parameters_worker,
#             cif_file_ids=cif_file_ids,
#             csv_file_id=csv_file_id,
#             parameter_ranges=parameter_ranges,
#             metadata={
#                 "cif_count": len(cif_file_ids),
#                 "optimization_type": "fusion_parameters",
#                 "parameter_count": len(parameter_ranges)
#             }
#         )
        
#         return TaskResponse(
#             status=ResponseStatus.SUCCESS,
#             message="融合参数优化任务已启动",
#             task_id=task_id,
#             task_status=TaskStatus.PENDING,
#             progress=0.0
#         )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"启动参数优化失败: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"启动参数优化失败: {str(e)}"
#         )

# async def optimize_fusion_parameters_worker(
#     cif_file_ids: List[str],
#     csv_file_id: str,
#     parameter_ranges: dict,
#     progress_callback=None
# ):
#     """融合参数优化工作函数"""
#     try:
#         # 这里实现参数优化逻辑
#         # 可以使用scikit-optimize或其他优化库
        
#         best_params = {
#             "embedding_size": 384,
#             "n_layers": 3,
#             "n_heads": 6,
#             "learning_rate": 1e-3
#         }
        
#         best_score = 0.92  # 示例分数
        
#         optimization_history = {
#             "parameter_combinations": [],
#             "scores": [],
#             "best_params": best_params,
#             "best_score": best_score
#         }
        
#         if progress_callback:
#             progress_callback(100.0)
        
#         return {
#             "best_parameters": best_params,
#             "best_score": best_score,
#             "optimization_history": optimization_history
#         }
        
#     except Exception as e:
#         logger.error(f"参数优化执行失败: {e}")
#         raise

# @router.post("/validate_fusion_data")
# async def validate_fusion_data(
#     cif_file_ids: List[str],
#     csv_file_id: str
# ):
#     """
#     验证融合数据兼容性
    
#     检查CIF文件和CSV数据是否可以进行有效融合
#     """
#     try:
#         logger.info("验证融合数据兼容性")
        
#         validation_results = {
#             "cif_validation": {},
#             "csv_validation": {},
#             "compatibility": {},
#             "recommendations": []
#         }
        
#         # 验证CIF文件
#         cif_info = []
#         for file_id in cif_file_ids:
#             file_info = await file_handler.get_file_info(file_id)
#             if file_info:
#                 cif_info.append({
#                     "file_id": file_id,
#                     "file_name": file_info.get("original_name"),
#                     "file_size": file_info.get("file_size"),
#                     "valid": True
#                 })
#             else:
#                 cif_info.append({
#                     "file_id": file_id,
#                     "valid": False,
#                     "error": "文件不存在"
#                 })
        
#         validation_results["cif_validation"] = {
#             "total_files": len(cif_file_ids),
#             "valid_files": len([info for info in cif_info if info.get("valid")]),
#             "file_details": cif_info
#         }
        
#         # 验证CSV文件
#         csv_file_info = await file_handler.get_file_info(csv_file_id)
#         if csv_file_info:
#             # 加载并分析CSV数据
#             csv_path = await file_handler.get_file_path(csv_file_id)
#             if csv_path:
#                 import pandas as pd
#                 try:
#                     csv_data = pd.read_csv(csv_path)
#                     validation_results["csv_validation"] = {
#                         "valid": True,
#                         "row_count": len(csv_data),
#                         "column_count": len(csv_data.columns),
#                         "columns": list(csv_data.columns),
#                         "missing_values": csv_data.isnull().sum().to_dict(),
#                         "data_types": csv_data.dtypes.to_dict()
#                     }
#                 except Exception as e:
#                     validation_results["csv_validation"] = {
#                         "valid": False,
#                         "error": f"CSV读取失败: {str(e)}"
#                     }
#             else:
#                 validation_results["csv_validation"] = {
#                     "valid": False,
#                     "error": "CSV文件路径无效"
#                 }
#         else:
#             validation_results["csv_validation"] = {
#                 "valid": False,
#                 "error": "CSV文件不存在"
#             }
        
#         # 兼容性检查
#         valid_cif_count = validation_results["cif_validation"]["valid_files"]
#         csv_valid = validation_results["csv_validation"].get("valid", False)
        
#         if valid_cif_count > 0 and csv_valid:
#             csv_row_count = validation_results["csv_validation"].get("row_count", 0)
            
#             validation_results["compatibility"] = {
#                 "compatible": True,
#                 "cif_csv_ratio": valid_cif_count / csv_row_count if csv_row_count > 0 else 0,
#                 "recommended_batch_size": min(valid_cif_count, csv_row_count)
#             }
            
#             if valid_cif_count == csv_row_count:
#                 validation_results["recommendations"].append("CIF文件数量与CSV行数完全匹配，最佳融合条件")
#             elif valid_cif_count < csv_row_count:
#                 validation_results["recommendations"].append(f"CIF文件数量少于CSV行数，建议增加CIF文件或截取CSV数据")
#             else:
#                 validation_results["recommendations"].append("CIF文件数量多于CSV行数，系统将自动匹配")
#         else:
#             validation_results["compatibility"] = {
#                 "compatible": False,
#                 "issues": []
#             }
            
#             if valid_cif_count == 0:
#                 validation_results["compatibility"]["issues"].append("没有有效的CIF文件")
#             if not csv_valid:
#                 validation_results["compatibility"]["issues"].append("CSV文件无效")
        
#         return {
#             "status": ResponseStatus.SUCCESS,
#             "message": "数据兼容性验证完成",
#             "validation": validation_results
#         }
        
#     except Exception as e:
#         logger.error(f"数据验证失败: {e}")
#         raise HTTPException(status_code=500, detail=f"数据验证失败: {str(e)}")

# @router.delete("/cancel/{task_id}")
# async def cancel_fusion_task(task_id: str):
    # """取消基因融合任务"""
    # try:
    #     success = await task_manager.cancel_task(task_id)
        
    #     if not success:
    #         raise HTTPException(
    #             status_code=400, 
    #             detail="任务取消失败，可能任务不存在或已完成"
    #         )
        
    #     return {
    #         "status": ResponseStatus.SUCCESS,
    #         "message": "基因融合任务已取消",
    #         "task_id": task_id
    #     }
        
    # except HTTPException:
    #     raise
    # except Exception as e:
    #     logger.error(f"取消任务失败: {e}")
    #     raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

@router.delete("/cancel/{task_id}")
async def cancel_fusion_task(task_id: str):
    """取消基因识别任务"""
    try:
        success = await task_manager.cancel_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=400, 
                detail="任务取消失败，可能任务不存在或已完成"
            )
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "基因融合任务已取消",
            "task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")
import json  
import numpy as np
import io
from fastapi.responses import StreamingResponse
@router.get("/download/{task_id}", 
           summary="下载特征数据文件",
           description="下载特征融合数据文件")
async def download_features(task_id: str):
    """
    根据任务ID和特征类型下载原始特征数据
    
    - **file_type**: 特征类型 (cgcl或egcl)
    - **task_id**: 任务ID（从基因融合响应中获取）
    """
    try:
        file_id = f"fusion_{task_id}"
        result_df = await file_handler.load_result(file_id, "csv")
        # 生成NPY文件字节流
        csv_buffer = io.StringIO()
        result_df.to_csv(csv_buffer, index=False)
        csv_data = csv_buffer.getvalue()
        csv_buffer.close()
        
        return StreamingResponse(
            io.BytesIO(csv_data.encode()),
            media_type="text/csv",
            headers={
                "Content-Disposition": f"attachment; filename=fusion_results_{task_id}.csv"
            }
        )

    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="未找到对应的特征文件")
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail="文件下载服务异常")
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse

# 🔍 可视化图像下载接口
@router.get("/tsne/{task_id}")
async def get_tsne_image(task_id: str):
    

    # 构建图像路径
    save_out_path =await file_handler.get_save_result()
    save_out_path = os.path.join(save_out_path, task_id)
    image_path = os.path.join(save_out_path, f"clip_tsne_result.png")

    if not os.path.exists(image_path):
        raise HTTPException(status_code=404, detail="图像未找到，请先生成可视化结果")

    return FileResponse(image_path, media_type='image/png', filename=f"fusion_tsne_result.png")
