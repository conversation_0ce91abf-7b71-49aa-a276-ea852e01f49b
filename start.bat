@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🧬 材料基因算法系统 - 启动脚本
echo ========================================
echo.

:: 检查环境文件
if not exist ".env" (
    echo ⚠️ 未找到 .env 配置文件
    echo 请先运行 install.bat 安装环境
    pause
    exit /b 1
)

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

:: 启动服务
echo 🚀 启动材料基因算法系统...
echo 📍 访问地址: http://localhost:8000
echo 📍 API文档: http://localhost:8000/docs
echo.
echo 按 Ctrl+C 停止服务
echo.

python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

echo.
echo 👋 服务已停止
pause