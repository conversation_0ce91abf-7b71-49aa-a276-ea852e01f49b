@echo off
echo.
echo ========================================
echo 材料基因算法系统 - 启动脚本
echo ========================================
echo.

:: 检查环境文件
if not exist ".env" (
    echo [WARNING] 未找到 .env 配置文件
    echo 请先运行 install.bat 安装环境
    pause
    exit /b 1
)

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 未检测到Python环境
    pause
    exit /b 1
)

echo [OK] 环境检查通过
echo.

:: 启动服务
echo [INFO] 启动材料基因算法系统...
echo [INFO] 访问地址: http://localhost:8000
echo [INFO] API文档: http://localhost:8000/docs
echo.
echo 按 Ctrl+C 停止服务
echo.

python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

echo.
echo [INFO] 服务已停止
pause