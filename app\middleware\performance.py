"""
性能优化中间件
提供请求压缩、缓存控制、性能监控等功能
"""

import time
import gzip
import json
from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import StreamingResponse
import logging

logger = logging.getLogger(__name__)

class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能优化中间件"""

    def __init__(self, app, enable_compression: bool = True, enable_caching: bool = True):
        super().__init__(app)
        self.enable_compression = enable_compression
        self.enable_caching = enable_caching
        self.metrics = {
            'total_requests': 0,
            'total_response_time': 0.0,
            'slow_requests': 0
        }

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 记录请求开始时间
        start_time = time.time()

        # 添加请求ID
        request_id = f"req_{int(start_time * 1000)}"
        request.state.request_id = request_id

        # 处理请求
        response = await call_next(request)

        # 计算响应时间
        process_time = time.time() - start_time

        # 更新性能指标
        self._update_metrics(process_time)

        # 添加性能头
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-Request-ID"] = request_id

        # 启用压缩
        if self.enable_compression and self._should_compress(request, response):
            response = await self._compress_response(response)

        # 添加缓存控制
        if self.enable_caching:
            self._add_cache_headers(request, response)

        # 记录慢请求
        if process_time > 5.0:  # 超过5秒的请求
            logger.warning(f"慢请求: {request.url} - {process_time:.2f}s")

        return response

    def _update_metrics(self, process_time: float):
        """更新性能指标"""
        self.metrics['total_requests'] += 1
        self.metrics['total_response_time'] += process_time

        if process_time > 5.0:
            self.metrics['slow_requests'] += 1

    def _should_compress(self, request: Request, response: Response) -> bool:
        """判断是否应该压缩响应"""
        # 检查Accept-Encoding头
        accept_encoding = request.headers.get('accept-encoding', '')
        if 'gzip' not in accept_encoding:
            return False

        # 检查响应类型
        content_type = response.headers.get('content-type', '')
        compressible_types = [
            'application/json',
            'text/html',
            'text/css',
            'text/javascript',
            'application/javascript'
        ]

        return any(ct in content_type for ct in compressible_types)

    async def _compress_response(self, response: Response) -> Response:
        """压缩响应内容"""
        try:
            # 获取响应内容
            if hasattr(response, 'body'):
                content = response.body
            else:
                # 对于StreamingResponse等特殊响应类型
                return response

            # 压缩内容
            compressed_content = gzip.compress(content)

            # 创建新的响应
            new_response = Response(
                content=compressed_content,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )

            # 添加压缩头
            new_response.headers["Content-Encoding"] = "gzip"
            new_response.headers["Content-Length"] = str(len(compressed_content))

            return new_response

        except Exception as e:
            logger.error(f"压缩响应失败: {e}")
            return response

    def _add_cache_headers(self, request: Request, response: Response):
        """添加缓存控制头"""
        path = request.url.path

        # 静态资源缓存策略
        if path.startswith('/static/'):
            response.headers["Cache-Control"] = "public, max-age=86400"  # 1天

        # API结果缓存策略
        elif '/result/' in path:
            response.headers["Cache-Control"] = "public, max-age=3600"  # 1小时

        # 任务状态不缓存
        elif '/status/' in path:
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"

        # 默认缓存策略
        else:
            response.headers["Cache-Control"] = "public, max-age=300"  # 5分钟

    def get_metrics(self) -> dict:
        """获取性能指标"""
        total_requests = self.metrics['total_requests']
        if total_requests == 0:
            return self.metrics

        avg_response_time = self.metrics['total_response_time'] / total_requests
        slow_request_rate = self.metrics['slow_requests'] / total_requests * 100

        return {
            **self.metrics,
            'average_response_time': avg_response_time,
            'slow_request_rate': slow_request_rate
        }