"""
API模块 - RESTful API接口定义

提供材料基因算法系统的所有HTTP API端点，包括：
- 文件管理
- 算法执行
- 任务管理
- 结果查询
- 可视化生成
"""

from app.api.v1 import (
    upload,
    mining,
    identification,
    fusion,
    editing,
    design,
    visualization,
    pipeline
)

# API版本信息
API_VERSION = "v1"
API_PREFIX = "/api"

# API模块映射
API_MODULES = {
    "upload": upload,
    "mining": mining,
    "identification": identification,
    "fusion": fusion,
    "editing": editing,
    "design": design,
    "visualization": visualization,
    "pipeline": pipeline
}

# API标签定义
API_TAGS = [
    {
        "name": "文件管理",
        "description": "文件上传、下载、删除等操作"
    },
    {
        "name": "基因挖掘",
        "description": "从晶体结构中提取SOAP和AFS特征"
    },
    {
        "name": "基因识别",
        "description": "使用深度学习模型识别材料特征"
    },
    {
        "name": "基因融合",
        "description": "多模态特征融合"
    },
    {
        "name": "基因编辑",
        "description": "材料性能筛选和分类"
    },
    {
        "name": "基因设计",
        "description": "智能材料设计和优化"
    },
    {
        "name": "可视化",
        "description": "数据可视化和分析图表"
    },
    {
        "name": "完整流程",
        "description": "一键执行完整算法流程"
    },
    {
        "name": "系统",
        "description": "系统状态和健康检查"
    }
]

__all__ = [
    "upload",
    "mining",
    "identification",
    "fusion",
    "editing",
    "design",
    "visualization",
    "pipeline",
    "API_VERSION",
    "API_PREFIX",
    "API_MODULES",
    "API_TAGS"
]