"""
材料基因算法API服务主应用
FastAPI应用的入口点
"""

import time
import os
import sys
from contextlib import asynccontextmanager
from pathlib import Path
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.config import settings
from app.utils.logger import get_logger, audit_log, setup_logging
from app.utils.exceptions import MaterialGeneAPIException
from app.models.response_models import ResponseStatus, ErrorResponse

# 导入所有API路由
from app.api.v1 import (
    upload, mining, identification, fusion, 
    editing, design, visualization, pipeline
)

# 设置日志
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    
    # 启动时执行
    logger.info("=" * 60)
    logger.info("🧬 材料基因算法API服务启动中...")
    logger.info(f"📊 应用名称: {settings.app_name}")
    logger.info(f"🔢 版本: {settings.app_version}")
    logger.info(f"🐛 调试模式: {settings.debug}")
    logger.info(f"🌐 主机: {settings.host}:{settings.port}")
    logger.info("=" * 60)
    
    # 检查必要的目录
    directories = [
        settings.upload_dir,
        settings.results_dir,
        "logs",
        "static/images"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 创建目录: {directory}")
    
    # 检查模型文件
    model_files = [
        settings.cgcl_model_path,
        settings.egcl_model_path,
        settings.clip_model_path
    ]
    
    missing_models = []
    for model_path in model_files:
        if not os.path.exists(model_path):
            missing_models.append(model_path)
            logger.warning(f"⚠️  模型文件不存在: {model_path}")
        else:
            logger.info(f"✅ 模型文件就绪: {model_path}")
    
    if missing_models:
        logger.warning("⚠️  部分模型文件缺失，某些功能可能不可用")
    
    # 初始化服务组件
    from app.services.task_manager import task_manager
    from app.services.file_handler import file_handler
    
    logger.info("⚡ 任务管理器初始化完成")
    logger.info("📂 文件处理器初始化完成")
    
    # 记录启动审计日志
    audit_log(
        action="system_startup",
        resource="api_service",
        user_id="system",
        version=settings.app_version,
        debug_mode=settings.debug
    )
    
    logger.info("🚀 材料基因算法API服务启动完成！")
    
    yield
    
    # 关闭时执行
    logger.info("🛑 材料基因算法API服务正在关闭...")
    
    # 清理任务
    try:
        from app.services.task_manager import task_manager
        running_tasks = len(task_manager.running_tasks)
        if running_tasks > 0:
            logger.info(f"⏹️  等待 {running_tasks} 个任务完成...")
            # 这里可以添加优雅关闭逻辑
    except Exception as e:
        logger.error(f"❌ 任务清理失败: {e}")
    
    # 记录关闭审计日志
    audit_log(
        action="system_shutdown",
        resource="api_service",
        user_id="system"
    )
    
    logger.info("✅ 材料基因算法API服务已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="""
    ## 🧬 材料基因算法在线系统

    这是一个基于人工智能的材料发现与设计系统，采用"材料基因"概念，
    从材料的晶体结构中提取决定其性能的关键特征信息。

    ### 🔬 主要功能

    1. **基因挖掘** - 从CIF文件提取SOAP描述符和AFS特征
    2. **基因识别** - 使用CGCL和EGCL深度学习模型识别高层次特征  
    3. **基因融合** - 使用CLIP模型融合多模态特征
    4. **基因编辑** - 使用SVM分类器进行材料性能筛选
    5. **基因设计** - 使用贝叶斯优化进行智能材料设计
    6. **可视化** - 提供t-SNE、决策边界等多种可视化

    ### 🎯 应用场景

    - 半导体材料缺陷工程优化
    - 材料性能预测与筛选
    - 高通量材料发现
    - 智能材料设计

    ### 🚀 快速开始

    1. 上传CIF格式的晶体结构文件
    2. 上传CSV格式的材料性质数据
    3. 启动完整流程或分步执行各个算法
    4. 查看结果和可视化
    """,
    docs_url=None,  # 禁用默认文档
    redoc_url=None,  # 禁用ReDoc
    lifespan=lifespan,
    debug=settings.debug
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# # 挂载静态文件
# app.mount("../static", StaticFiles(directory="static"), name="static")


# 请求处理中间件
@app.middleware("http")
async def request_middleware(request: Request, call_next):
    """请求处理中间件"""
    start_time = time.time()
    
    # 生成请求ID
    request_id = f"req_{int(time.time() * 1000)}_{id(request)}"
    
    # 记录请求开始
    logger.info(
        f"📨 请求开始: {request.method} {request.url.path}",
        extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "client_ip": request.client.host if request.client else "unknown"
        }
    )
    
    # 处理请求
    try:
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = str(process_time)
        
        # 记录请求完成
        logger.info(
            f"✅ 请求完成: {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s",
            extra={
                "request_id": request_id,
                "status_code": response.status_code,
                "process_time": process_time
            }
        )
        
        return response
        
    except Exception as e:
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 记录请求失败
        logger.error(
            f"❌ 请求失败: {request.method} {request.url.path} - {str(e)} - {process_time:.3f}s",
            extra={
                "request_id": request_id,
                "error": str(e),
                "process_time": process_time
            },
            exc_info=True
        )
        
        # 返回统一错误响应
        return JSONResponse(
            status_code=500,
            content={
                "status": ResponseStatus.ERROR,
                "message": "服务器内部错误",
                "request_id": request_id,
                "timestamp": time.time()
            },
            headers={
                "X-Request-ID": request_id,
                "X-Process-Time": str(process_time)
            }
        )


# 全局异常处理器
@app.exception_handler(MaterialGeneAPIException)
async def material_gene_exception_handler(request: Request, exc: MaterialGeneAPIException):
    """材料基因API异常处理器"""
    logger.error(
        f"业务异常: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "details": exc.details,
            "path": request.url.path
        }
    )
    
    return JSONResponse(
        status_code=400,
        content=ErrorResponse(
            status=ResponseStatus.ERROR,
            message=exc.message,
            error_code=exc.error_code,
            error_details=exc.details
        ).dict()
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.warning(
        f"HTTP异常: {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "path": request.url.path
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "status": ResponseStatus.ERROR,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(
        f"未处理异常: {str(exc)}",
        extra={"path": request.url.path},
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "status": ResponseStatus.ERROR,
            "message": "服务器内部错误" if not settings.debug else str(exc),
            "type": "internal_server_error"
        }
    )


# 包含所有API路由
app.include_router(upload.router, prefix="/api/v1")
app.include_router(mining.router, prefix="/api/v1")
app.include_router(identification.router, prefix="/api/v1")
app.include_router(fusion.router, prefix="/api/v1")
app.include_router(editing.router, prefix="/api/v1")
app.include_router(design.router, prefix="/api/v1")
app.include_router(visualization.router, prefix="/api/v1")
app.include_router(pipeline.router, prefix="/api/v1")


# 根路径
@app.get("/", tags=["根目录"])
async def root():
    """API根路径"""
    return {
        "message": "🧬 材料基因算法在线系统",
        "version": settings.app_version,
        "status": "running",
        "docs": "/docs",
        "timestamp": time.time()
    }


# 健康检查
@app.get("/health", tags=["系统"])
async def health_check():
    """健康检查端点"""
    from app.services.task_manager import task_manager
    from app.services.file_handler import file_handler
    
    # 检查各个组件状态
    health_status = {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.app_version,
        "components": {
            "task_manager": {
                "status": "healthy",
                "active_tasks": len(task_manager.running_tasks),
                "total_tasks": len(task_manager.tasks)
            },
            "file_system": {
                "status": "healthy",
                "upload_dir": os.path.exists(settings.upload_dir),
                "results_dir": os.path.exists(settings.results_dir)
            }
        }
    }
    
    # 检查模型文件
    model_status = {}
    model_files = {
        "cgcl": settings.cgcl_model_path,
        "egcl": settings.egcl_model_path,
        "clip": settings.clip_model_path
    }
    
    for model_name, model_path in model_files.items():
        model_status[model_name] = {
            "exists": os.path.exists(model_path),
            "path": model_path
        }
    
    health_status["components"]["models"] = model_status
    
    return health_status


# 系统信息
@app.get("/info", tags=["系统"])
async def system_info():
    """获取系统信息"""
    import psutil
    import platform
    
    from app.services.task_manager import task_manager
    
    # 获取系统资源信息
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    system_info = {
        "application": {
            "name": settings.app_name,
            "version": settings.app_version,
            "debug": settings.debug,
            "python_version": platform.python_version()
        },
        "system": {
            "platform": platform.platform(),
            "architecture": platform.architecture()[0],
            "processor": platform.processor(),
            "cpu_count": psutil.cpu_count()
        },
        "resources": {
            "memory": {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_percent": memory.percent
            },
            "disk": {
                "total_gb": round(disk.total / (1024**3), 2),
                "free_gb": round(disk.free / (1024**3), 2),
                "used_percent": round((disk.used / disk.total) * 100, 2)
            }
        },
        "tasks": {
            "active": len(task_manager.running_tasks),
            "total": len(task_manager.tasks),
            "statistics": task_manager.get_task_statistics()
        }
    }
    
    return system_info


# 自定义OpenAPI文档
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=settings.app_name,
        version=settings.app_version,
        description=app.description,
        routes=app.routes,
    )
    
    # 添加自定义信息
    openapi_schema["info"]["contact"] = {
        "name": "材料基因算法团队",
        "email": "<EMAIL>"
    }
    
    openapi_schema["info"]["license"] = {
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT"
    }
    
    # 添加服务器信息
    openapi_schema["servers"] = [
        {
            "url": f"http://{settings.host}:{settings.port}",
            "description": "开发服务器"
        }
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


# # 自定义文档页面
# @app.get("/docs", include_in_schema=False)
# async def custom_swagger_ui_html():
#     return get_swagger_ui_html(
#         openapi_url=app.openapi_url,
#         title=f"{app.title} - 接口文档",
#         oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
#         swagger_js_url="/static/swagger-ui-bundle.js",
#         swagger_css_url="/static/swagger-ui.css",
#     )


# 开发模式专用路由
if settings.debug:
    
    @app.get("/debug/logs", tags=["调试"])
    async def get_recent_logs(lines: int = 100):
        """获取最近的日志（仅开发模式）"""
        try:
            if os.path.exists(settings.log_file):
                with open(settings.log_file, 'r', encoding='utf-8') as f:
                    all_lines = f.readlines()
                    recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                    return {"logs": recent_lines}
            else:
                return {"logs": []}
        except Exception as e:
            return {"error": f"读取日志失败: {str(e)}"}
    
    @app.post("/debug/cleanup", tags=["调试"])
    async def debug_cleanup():
        """调试清理（仅开发模式）"""
        from app.services.task_manager import task_manager
        from app.services.file_handler import file_handler
        
        # 清理完成的任务
        cleanup_count = await task_manager.cleanup_old_tasks(days=0)
        
        # 清理临时文件
        await file_handler.cleanup_old_files(days=0)
        
        return {
            "message": "调试清理完成",
            "cleaned_tasks": cleanup_count
        }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        workers=settings.workers,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )