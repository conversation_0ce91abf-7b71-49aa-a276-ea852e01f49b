import asyncio
import uuid
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import json

from app.models.response_models import TaskStatus
from app.config import settings
from app.services.file_handler import file_handler

logger = logging.getLogger(__name__)

@dataclass
class TaskInfo:
    """任务信息数据类"""
    task_id: str
    task_type: str
    status: TaskStatus
    progress: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.executor = ThreadPoolExecutor(max_workers=settings.max_concurrent_tasks)
        self.running_tasks: Dict[str, asyncio.Task] = {}
        
    async def create_task(
        self, 
        task_type: str, 
        task_func: Callable,
        *args,
        **kwargs
    ) -> str:
        """
        创建并启动异步任务
        
        Args:
            task_type: 任务类型
            task_func: 任务执行函数
            *args, **kwargs: 传递给任务函数的参数
            
        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 创建任务信息
        task_info = TaskInfo(
            task_id=task_id,
            task_type=task_type,
            status=TaskStatus.PENDING,
            start_time=datetime.now(),
            metadata=kwargs.get('metadata', {})
        )
        
        self.tasks[task_id] = task_info
        
        # 启动异步任务
        async_task = asyncio.create_task(
            self._execute_task(task_id, task_func, *args, **kwargs)
        )
        self.running_tasks[task_id] = async_task
        
        logger.info(f"任务创建成功: {task_id} ({task_type})")
        return task_id
    
    async def _execute_task(
        self, 
        task_id: str, 
        task_func: Callable,
        *args,
        **kwargs
    ):
        """执行任务的内部方法"""
        task_info = self.tasks[task_id]
        
        try:
            # 更新任务状态为运行中
            task_info.status = TaskStatus.RUNNING
            task_info.start_time = datetime.now()
            
            logger.info(f"开始执行任务: {task_id}")
            
            # 包装进度回调
            def progress_callback(progress: float):
                if task_id in self.tasks:
                    self.tasks[task_id].progress = min(max(progress, 0.0), 100.0)
            
            # 如果函数支持进度回调，添加回调参数
            if 'progress_callback' in task_func.__code__.co_varnames:
                kwargs['progress_callback'] = progress_callback
            
            # 在线程池中执行CPU密集型任务
            loop = asyncio.get_event_loop()
            if asyncio.iscoroutinefunction(task_func):
                # 如果任务函数是协程函数，直接运行
                result = await task_func(*args, **kwargs)
            else:
                # 如果任务函数是普通函数，使用线程池执行
                result = await loop.run_in_executor(
                    self.executor, 
                    lambda: task_func(*args, **kwargs)
                )
                
            # 任务完成
            task_info.status = TaskStatus.COMPLETED
            task_info.end_time = datetime.now()
            task_info.progress = 100.0
            task_info.result = result
            
            # 保存结果到文件
            if result:
                print("now_result")
                await self._save_task_result(task_id, result)
            
            logger.info(f"任务执行完成: {task_id}")
            
        except Exception as e:
            # 任务失败
            task_info.status = TaskStatus.FAILED
            task_info.end_time = datetime.now()
            task_info.error_message = str(e)
            
            logger.error(f"任务执行失败: {task_id} - {str(e)}")
            
        finally:
            # 清理运行中的任务引用
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    async def _save_task_result(self, task_id: str, result: Any):
        """保存任务结果"""
        try:
            # 根据结果类型选择保存格式
            if isinstance(result, dict):
                await file_handler.save_result(task_id, result, "json")
            elif hasattr(result, 'to_csv'):  # pandas DataFrame
                await file_handler.save_result(task_id, result, "csv")
            else:
                # 使用pickle保存其他类型
                await file_handler.save_result(task_id, result, "pkl")
                
        except Exception as e:
            logger.error(f"保存任务结果失败: {task_id} - {str(e)}")
    
    def get_task_status(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[TaskInfo]:
        """获取所有任务"""
        return list(self.tasks.values())
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[TaskInfo]:
        """根据状态获取任务"""
        return [task for task in self.tasks.values() if task.status == status]
    
    def get_tasks_by_type(self, task_type: str) -> List[TaskInfo]:
        """根据类型获取任务"""
        return [task for task in self.tasks.values() if task.task_type == task_type]
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self.tasks:
            return False
        
        task_info = self.tasks[task_id]
        
        # 只能取消等待中或运行中的任务
        if task_info.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            return False
        
        # 取消异步任务
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            del self.running_tasks[task_id]
        
        # 更新任务状态
        task_info.status = TaskStatus.CANCELLED
        task_info.end_time = datetime.now()
        
        logger.info(f"任务已取消: {task_id}")
        return True
    
    async def retry_task(self, task_id: str) -> Optional[str]:
        """重试失败的任务"""
        if task_id not in self.tasks:
            return None
        
        task_info = self.tasks[task_id]
        
        # 只能重试失败的任务
        if task_info.status != TaskStatus.FAILED:
            return None
        
        # 创建新的任务ID
        new_task_id = str(uuid.uuid4())
        
        # 复制任务信息
        new_task_info = TaskInfo(
            task_id=new_task_id,
            task_type=task_info.task_type,
            status=TaskStatus.PENDING,
            metadata={**task_info.metadata, "retry_of": task_id}
        )
        
        self.tasks[new_task_id] = new_task_info
        
        logger.info(f"任务重试: {task_id} -> {new_task_id}")
        return new_task_id
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        total_tasks = len(self.tasks)
        status_counts = {}
        type_counts = {}
        
        for task in self.tasks.values():
            # 状态统计
            status_counts[task.status.value] = status_counts.get(task.status.value, 0) + 1
            
            # 类型统计
            type_counts[task.task_type] = type_counts.get(task.task_type, 0) + 1
        
        # 计算平均执行时间
        completed_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]
        avg_duration = 0
        if completed_tasks:
            durations = []
            for task in completed_tasks:
                if task.start_time and task.end_time:
                    duration = (task.end_time - task.start_time).total_seconds()
                    durations.append(duration)
            
            if durations:
                avg_duration = sum(durations) / len(durations)
        
        return {
            "total_tasks": total_tasks,
            "status_distribution": status_counts,
            "type_distribution": type_counts,
            "average_duration_seconds": avg_duration,
            "active_tasks": len(self.running_tasks),
            "success_rate": (status_counts.get("completed", 0) / total_tasks * 100) if total_tasks > 0 else 0
        }
    
    async def cleanup_old_tasks(self, days: int = 7):
        """清理旧任务"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        tasks_to_remove = []
        for task_id, task_info in self.tasks.items():
            # 只清理已完成、失败或取消的任务
            if (task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] 
                and task_info.end_time 
                and task_info.end_time < cutoff_time):
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
            logger.info(f"清理旧任务: {task_id}")
        
        return len(tasks_to_remove)
    
    async def export_task_history(self, task_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """导出任务历史"""
        tasks_to_export = []
        
        if task_ids:
            tasks_to_export = [self.tasks[tid] for tid in task_ids if tid in self.tasks]
        else:
            tasks_to_export = list(self.tasks.values())
        
        # 转换为可序列化的格式
        export_data = []
        for task in tasks_to_export:
            task_dict = asdict(task)
            # 转换datetime对象为字符串
            if task_dict['start_time']:
                task_dict['start_time'] = task_dict['start_time'].isoformat()
            if task_dict['end_time']:
                task_dict['end_time'] = task_dict['end_time'].isoformat()
            
            export_data.append(task_dict)
        
        return {
            "export_time": datetime.now().isoformat(),
            "total_tasks": len(export_data),
            "tasks": export_data
        }
    
    async def get_task_progress_stream(self, task_id: str):
        """获取任务进度流（用于实时更新）"""
        if task_id not in self.tasks:
            return
        
        while True:
            task_info = self.tasks.get(task_id)
            if not task_info:
                break
            
            yield {
                "task_id": task_id,
                "status": task_info.status.value,
                "progress": task_info.progress,
                "timestamp": datetime.now().isoformat()
            }
            
            # 如果任务完成或失败，停止流
            if task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                break
            
            await asyncio.sleep(1)  # 每秒更新一次

# 全局任务管理器实例
task_manager = TaskManager()