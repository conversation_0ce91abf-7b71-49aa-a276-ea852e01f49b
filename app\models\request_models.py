from pydantic import BaseModel, Field, validator
from typing import Optional, List, Union, Tuple
from enum import Enum

class LabelType(str, Enum):
    """标签类型枚举"""
    BOUNDARY_LEFT = "boundary_left"
    BOUNDARY_RIGHT = "boundary_right"
    INTERVAL = "interval"

class TrainMode(str, Enum):
    """训练模式枚举"""
    TRAIN = "true"
    INFERENCE = "false"

class FileUploadRequest(BaseModel):
    """文件上传请求"""
    file_type: str = Field(..., description="文件类型: cif 或 csv")
    description: Optional[str] = Field(None, description="文件描述")

class AlgorithmParametersRequest(BaseModel):
    """算法参数设置请求"""
    target_value: float = Field(default=12.04, description="目标性质值")
    label_type: LabelType = Field(default=LabelType.BOUNDARY_RIGHT, description="标签类型")
    n_calls: int = Field(default=200, ge=50, le=1000, description="优化次数")
    train_mode: TrainMode = Field(default=TrainMode.TRAIN, description="训练模式")
    
    @validator('target_value')
    def validate_target_value(cls, v):
        if not -100 <= v <= 100:
            raise ValueError('目标值应在-100到100之间')
        return v

class GeneMiningRequest(BaseModel):
    """基因挖掘请求"""
    cif_files: List[str] = Field(..., description="CIF文件ID列表")
    use_soap: bool = Field(default=True, description="是否使用SOAP描述符")
    use_afs: bool = Field(default=True, description="是否使用AFS特征")

class GeneIdentificationRequest(BaseModel):
    """基因识别请求"""
    cif_files: List[str] = Field(..., description="CIF文件ID列表")
    use_cgcl: bool = Field(default=True, description="是否使用CGCL模型")
    use_egcl: bool = Field(default=True, description="是否使用EGCL模型")

class GeneFusionRequest(BaseModel):
    """基因融合请求"""
    cif_files: List[str] = Field(..., description="CIF文件ID列表")
    csv_file: str = Field(..., description="CSV数据文件ID")
    use_clip: bool = Field(default=True, description="是否使用CLIP模型")

class GeneEditingRequest(BaseModel):
    """基因编辑请求"""
    features_data: str = Field(..., description="融合特征数据ID")
    flag: Union[float, Tuple[float, float]] = Field(..., description="性能阈值或范围")
    label_type: LabelType = Field(..., description="边界类型")
    is_train: bool = Field(default=True, description="是否训练模式")
    auto_select_ratio: float = Field(default=0.3, ge=0.1, le=0.9, description="自动选择比例")

class GeneDesignRequest(BaseModel):
    """基因设计请求"""
    svc_model_id: str = Field(..., description="SVM模型ID")
    search_features: str = Field(..., description="搜索特征数据ID")
    n_calls: int = Field(default=200, ge=10, le=1000, description="优化迭代次数")
    target_value: float = Field(default=12.04, description="目标性质值")

class VisualizationRequest(BaseModel):
    """可视化请求"""
    data_id: str = Field(..., description="数据ID")
    visualization_type: str = Field(..., description="可视化类型: tsne, edit_2d, edit_3d, optimization")
    save_filename: Optional[str] = Field(None, description="保存文件名")

class FullPipelineRequest(BaseModel):
    """完整流程请求"""
    cif_files: List[str] = Field(..., description="CIF文件ID列表")
    csv_file: str = Field(..., description="CSV数据文件ID")
    parameters: AlgorithmParametersRequest = Field(..., description="算法参数")
    run_visualization: bool = Field(default=True, description="是否运行可视化")

class TaskStatusRequest(BaseModel):
    """任务状态查询请求"""
    task_id: str = Field(..., description="任务ID")

class BatchProcessRequest(BaseModel):
    """批处理请求"""
    requests: List[Union[
        GeneMiningRequest,
        GeneIdentificationRequest, 
        GeneFusionRequest,
        GeneEditingRequest,
        GeneDesignRequest
    ]] = Field(..., description="批处理请求列表")
    execute_sequential: bool = Field(default=True, description="是否顺序执行")

# 高级配置请求
class AdvancedConfigRequest(BaseModel):
    """高级配置请求"""
    cgcl_config: Optional[dict] = Field(None, description="CGCL模型配置")
    egcl_config: Optional[dict] = Field(None, description="EGCL模型配置")
    clip_config: Optional[dict] = Field(None, description="CLIP模型配置")
    svm_config: Optional[dict] = Field(None, description="SVM模型配置")
    optimization_config: Optional[dict] = Field(None, description="优化算法配置")

# 模型管理请求
class ModelManagementRequest(BaseModel):
    """模型管理请求"""
    action: str = Field(..., description="操作类型: load, unload, reload, status")
    model_type: str = Field(..., description="模型类型: cgcl, egcl, clip, svm")
    model_path: Optional[str] = Field(None, description="模型路径")

# 数据预处理请求
class DataPreprocessRequest(BaseModel):
    """数据预处理请求"""
    file_ids: List[str] = Field(..., description="文件ID列表")
    preprocessing_options: dict = Field(default_factory=dict, description="预处理选项")
    
    class Config:
        schema_extra = {
            "example": {
                "file_ids": ["file_001", "file_002"],
                "preprocessing_options": {
                    "normalize": True,
                    "remove_outliers": True,
                    "fill_missing": "mean"
                }
            }
        }