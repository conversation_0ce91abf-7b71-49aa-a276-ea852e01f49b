@echo off
echo.
echo ========================================
echo 材料基因算法系统 - 环境安装脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 未检测到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [OK] 检测到Python环境
python --version

echo.
echo [INFO] 开始安装依赖包...
echo.

:: 升级pip
echo [STEP 1] 升级pip...
pip install --upgrade pip

:: 安装基础包
echo [STEP 2] 安装基础包...
pip install --upgrade setuptools wheel

:: 安装项目依赖
echo [STEP 3] 安装项目依赖...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo [ERROR] 依赖安装失败，尝试使用国内镜像...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
)

:: 创建必要目录
echo [STEP 4] 创建项目目录...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "results" mkdir results
if not exist "cache" mkdir cache
if not exist "config" mkdir config

:: 创建环境配置文件
echo [STEP 5] 创建配置文件...
if not exist ".env" (
    echo # 材料基因算法系统环境配置 > .env
    echo DEBUG=false >> .env
    echo HOST=0.0.0.0 >> .env
    echo PORT=8000 >> .env
    echo DATABASE_URL=sqlite+aiosqlite:///./app.db >> .env
    echo LOG_LEVEL=INFO >> .env
    echo MAX_FILE_SIZE=104857600 >> .env
)

echo.
echo ========================================
echo [SUCCESS] 安装完成！
echo ========================================
echo.
echo 下一步操作:
echo 1. 编辑 .env 文件配置系统参数
echo 2. 运行启动脚本: start.bat
echo 3. 或手动启动: python -m uvicorn app.main:app --reload
echo.
pause