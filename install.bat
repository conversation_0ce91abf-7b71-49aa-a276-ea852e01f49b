@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🧬 材料基因算法系统 - 环境安装脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ 检测到Python环境
python --version

echo.
echo 🔄 开始安装依赖包...
echo.

:: 运行Python安装脚本
python install_dependencies.py

if errorlevel 1 (
    echo.
    echo ❌ 安装过程中出现错误
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 安装完成！
echo ========================================
echo.
echo 📝 下一步操作:
echo 1. 编辑 .env 文件配置系统参数
echo 2. 运行启动脚本: start.bat
echo 3. 或手动启动: python -m uvicorn app.main:app --reload
echo.
pause