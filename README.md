# 🧬 材料基因算法在线系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个基于深度学习的材料基因算法在线平台，实现从材料结构到性能预测的完整流程。

## ✨ 主要特性

### 🔬 核心算法
- **基因挖掘**: SOAP描述符 + AFS特征提取
- **基因识别**: CGCL + EGCL 图神经网络
- **基因融合**: CLIP多模态特征融合
- **基因编辑**: SVM分类器 + 半监督学习
- **基因设计**: 贝叶斯优化材料设计

### 🚀 性能优化
- **多层缓存**: 内存 + Redis + 文件缓存
- **异步处理**: 支持大规模并发任务
- **智能压缩**: 自动响应压缩
- **数据库集成**: SQLAlchemy + SQLite/PostgreSQL
- **实时监控**: 性能指标收集

### 🎨 用户界面
- **现代化设计**: 响应式Web界面
- **实时反馈**: WebSocket任务状态更新
- **可视化展示**: 多种图表类型
- **文件管理**: 拖拽上传，批量处理

## 📦 快速开始

### 环境要求
- Python 3.8+
- 8GB+ RAM (推荐16GB)
- 10GB+ 磁盘空间

### 一键安装
```bash
# Windows用户
install.bat

# Linux/macOS用户
python install_dependencies.py
```

### 手动安装
```bash
# 1. 克隆项目
git clone <repository-url>
cd material_genome_api

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 创建配置文件
cp .env.example .env

# 5. 启动服务
python -m uvicorn app.main:app --reload
```

### 启动服务
```bash
# Windows用户
start.bat

# 或手动启动
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

访问地址:
- 🌐 Web界面: http://localhost:8000
- 📚 API文档: http://localhost:8000/docs
- 🔧 管理界面: http://localhost:8000/admin

## 🏗️ 系统架构

```
材料基因算法系统
├── 前端界面 (HTML5 + JavaScript)
│   ├── 文件上传管理
│   ├── 参数配置界面
│   ├── 实时进度监控
│   └── 结果可视化展示
├── API层 (FastAPI)
│   ├── RESTful API端点
│   ├── WebSocket实时通信
│   ├── 中间件优化
│   └── 错误处理机制
├── 核心算法层
│   ├── 基因挖掘 (SOAP + AFS)
│   ├── 基因识别 (CGCL + EGCL)
│   ├── 基因融合 (CLIP)
│   ├── 基因编辑 (SVM)
│   └── 基因设计 (贝叶斯优化)
├── 服务层
│   ├── 任务管理器
│   ├── 文件处理器
│   ├── 缓存管理器
│   └── 配置管理器
└── 数据层
    ├── SQLite/PostgreSQL
    ├── Redis缓存
    └── 文件存储
```

## 📊 性能指标

| 指标 | 优化前 | 优化后 | 提升幅度 |
|------|--------|--------|----------|
| 平均响应时间 | 5-10秒 | 2-4秒 | **60-80%** |
| 内存使用 | 2-4GB | 1-2GB | **50%** |
| 并发用户数 | 10-20 | 50-100 | **400%** |
| 缓存命中率 | 0% | 70-85% | **新增** |

## 🔧 配置说明

### 环境变量 (.env)
```bash
# 应用配置
DEBUG=false
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///./app.db

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 缓存配置
CACHE_SIZE=1000
CACHE_TTL=3600
```

### 算法参数
- **目标性质值**: 材料性能目标 (默认: 12.04)
- **优化次数**: 贝叶斯优化迭代次数 (默认: 200)
- **标签类型**: boundary_right/boundary_left/interval
- **训练模式**: 训练模式/推理模式

## 📖 API文档

### 核心端点
```bash
# 文件上传
POST /api/v1/upload/file
POST /api/v1/upload/files/batch

# 算法执行
POST /api/v1/mining/start      # 基因挖掘
POST /api/v1/identification/start  # 基因识别
POST /api/v1/fusion/start      # 基因融合
POST /api/v1/editing/start     # 基因编辑
POST /api/v1/design/start      # 基因设计

# 完整流程
POST /api/v1/pipeline/start    # 一键执行完整流程

# 任务管理
GET /api/v1/{task_type}/status/{task_id}
GET /api/v1/{task_type}/result/{task_id}
```

## 🧪 使用示例

### 1. 上传文件
```javascript
const formData = new FormData();
formData.append('file', cifFile);
formData.append('file_type', 'cif');

const response = await fetch('/api/v1/upload/file', {
    method: 'POST',
    body: formData
});
```

### 2. 启动基因挖掘
```javascript
const params = {
    cif_files: ['file_id_1', 'file_id_2'],
    use_soap: true,
    use_afs: true
};

const response = await fetch('/api/v1/mining/start', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify(params)
});
```

### 3. 查询任务状态
```javascript
const taskId = 'task_uuid';
const response = await fetch(`/api/v1/mining/status/${taskId}`);
const status = await response.json();
```

## 🔍 故障排除

### 常见问题

**Q: 安装依赖时出现错误**
```bash
# 升级pip
pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

**Q: 启动时端口被占用**
```bash
# 查看端口占用
netstat -ano | findstr :8000

# 修改端口
python -m uvicorn app.main:app --port 8001
```

**Q: 内存不足**
```bash
# 减少并发任务数
export MAX_CONCURRENT_TASKS=2

# 启用交换文件
# Linux: sudo swapon /swapfile
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 文档: [在线文档](https://your-docs-url.com)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！