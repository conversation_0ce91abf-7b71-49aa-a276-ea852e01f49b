"""
数据模型模块

定义API请求和响应的数据模型，使用Pydantic进行数据验证。

模型分类：
- 请求模型：定义API输入参数的结构和验证规则
- 响应模型：定义API返回数据的格式
- 枚举类型：定义常量和选项
- 基础模型：可复用的模型组件
"""

# 导入请求模型
from app.models.request_models import (
    # 基础请求
    FileUploadRequest,
    AlgorithmParametersRequest,
    
    # 算法请求
    GeneMiningRequest,
    GeneIdentificationRequest,
    GeneFusionRequest,
    GeneEditingRequest,
    GeneDesignRequest,
    
    # 可视化请求
    VisualizationRequest,
    
    # 流程请求
    FullPipelineRequest,
    
    # 其他请求
    TaskStatusRequest,
    BatchProcessRequest,
    AdvancedConfigRequest,
    ModelManagementRequest,
    DataPreprocessRequest,
    
    # 枚举类型
    LabelType,
    TrainMode
)

# 导入响应模型
from app.models.response_models import (
    # 基础响应
    BaseResponse,
    ErrorResponse,
    
    # 文件相关
    FileUploadResponse,
    FileListResponse,
    
    # 任务相关
    TaskResponse,
    TaskStatusResponse,
    
    # 算法结果
    GeneMiningResponse,
    GeneIdentificationResponse,
    GeneFusionResponse,
    GeneEditingResponse,
    GeneDesignResponse,
    
    # 可视化
    VisualizationResponse,
    
    # 流程
    FullPipelineResponse,
    
    # 其他响应
    StatisticsResponse,
    ModelInfoResponse,
    BatchProcessResponse,
    SystemStatusResponse,
    DataAnalysisResponse,
    ExportResponse,
    ConfigResponse,
    PaginatedResponse,
    
    # 枚举类型
    TaskStatus,
    ResponseStatus
)

# 通用验证函数
def validate_file_extension(filename: str, allowed_extensions: list) -> bool:
    """验证文件扩展名"""
    if not filename:
        return False
    ext = filename.lower().split('.')[-1]
    return f".{ext}" in allowed_extensions

def validate_cif_filename(filename: str) -> bool:
    """验证CIF文件名"""
    return validate_file_extension(filename, ['.cif'])

def validate_csv_filename(filename: str) -> bool:
    """验证CSV文件名"""
    return validate_file_extension(filename, ['.csv'])

# 模型工厂函数
def create_error_response(
    message: str, 
    error_code: str = None, 
    details: dict = None
) -> ErrorResponse:
    """创建错误响应"""
    return ErrorResponse(
        status=ResponseStatus.ERROR,
        message=message,
        error_code=error_code,
        error_details=details
    )

def create_success_response(
    message: str = "操作成功",
    **kwargs
) -> BaseResponse:
    """创建成功响应"""
    return BaseResponse(
        status=ResponseStatus.SUCCESS,
        message=message,
        **kwargs
    )

# 模型常量
MODEL_CONSTANTS = {
    "MAX_FILE_SIZE": 100 * 1024 * 1024,  # 100MB
    "MAX_BATCH_SIZE": 50,
    "MAX_TASK_TIMEOUT": 3600,  # 1小时
    "SUPPORTED_FILE_TYPES": [".cif", ".csv"],
    "SUPPORTED_VIZ_TYPES": [
        "tsne", "edit_2d", "edit_3d", "optimization",
        "feature_importance", "correlation_matrix",
        "pca", "cluster_analysis", "distribution_plot"
    ]
}

__all__ = [
    # 请求模型
    "FileUploadRequest",
    "AlgorithmParametersRequest",
    "GeneMiningRequest",
    "GeneIdentificationRequest", 
    "GeneFusionRequest",
    "GeneEditingRequest",
    "GeneDesignRequest",
    "VisualizationRequest",
    "FullPipelineRequest",
    "TaskStatusRequest",
    "BatchProcessRequest",
    "AdvancedConfigRequest",
    "ModelManagementRequest",
    "DataPreprocessRequest",
    
    # 响应模型
    "BaseResponse",
    "ErrorResponse",
    "FileUploadResponse",
    "FileListResponse",
    "TaskResponse",
    "TaskStatusResponse",
    "GeneMiningResponse",
    "GeneIdentificationResponse",
    "GeneFusionResponse",
    "GeneEditingResponse",
    "GeneDesignResponse",
    "VisualizationResponse",
    "FullPipelineResponse",
    "StatisticsResponse",
    "ModelInfoResponse",
    "BatchProcessResponse",
    "SystemStatusResponse",
    "DataAnalysisResponse",
    "ExportResponse",
    "ConfigResponse",
    "PaginatedResponse",
    
    # 枚举类型
    "LabelType",
    "TrainMode",
    "TaskStatus",
    "ResponseStatus",
    
    # 验证函数
    "validate_file_extension",
    "validate_cif_filename",
    "validate_csv_filename",
    
    # 工厂函数
    "create_error_response",
    "create_success_response",
    
    # 常量
    "MODEL_CONSTANTS"
]