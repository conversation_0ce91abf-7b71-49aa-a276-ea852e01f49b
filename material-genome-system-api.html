<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>材料基因算法在线系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            height: fit-content;
        }


        .visualization-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 600px;
        }

        .section {
            margin-bottom: 25px;
            padding: 20px;
            border: 2px solid #e1e8f0;
            border-radius: 15px;
            background: #fafbfc;
            transition: all 0.3s ease;
        }

        .section:hover {
            border-color: #667eea;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
        }

        .section h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            width: 24px;
            height: 24px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e1e8f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }

        .result-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e1e8f0;
        }

        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .visualization-placeholder {
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #f0f2f5, #e1e8f0);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            margin: 20px 0;
            border: 2px dashed #ccc;
        }

        .file-upload {
            border: 2px dashed #667eea;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            background: rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: #764ba2;
        }

        .file-upload.dragging {
            background: rgba(102, 126, 234, 0.3);
            border-color: #764ba2;
            transform: scale(1.02);
        }

        .file-upload input {
            display: none;
        }

        .parameter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .algorithm-flow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px 0;
            padding: 15px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
        }

        .flow-step {
            padding: 10px 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            flex: 1;
            margin: 0 5px;
            text-align: center;
            font-weight: 600;
            color: #667eea;
            transition: all 0.3s ease;
        }

        .flow-step.active {
            background: #667eea;
            color: white;
            transform: scale(1.05);
        }

        .flow-step.completed {
            background: #28a745;
            color: white;
        }

        .flow-arrow {
            color: #667eea;
            font-size: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f0f2f5;
            border-radius: 6px;
            margin: 5px 0;
            font-size: 14px;
        }

        .file-item .remove-btn {
            cursor: pointer;
            color: #dc3545;
            font-weight: bold;
            padding: 0 5px;
        }

        .file-item .remove-btn:hover {
            color: #c82333;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 90%;
            max-height: 90%;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .parameter-grid {
                grid-template-columns: 1fr;
            }
        }

        /* new */
        .collapse-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            margin: 10px 0;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .collapse-header {
            padding: 12px 16px;
            background-color: #f0f4f8;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 15px;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
        }

        .collapse-header:hover {
            background-color: #e6f0fb;
        }

        .collapse-content {
            padding: 10px;
        }

        .toggle-icon {
            margin-left: 10px;
            font-size: 16px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px solid #eee;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .remove-btn {
            color: #dc3545;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
        }

        .remove-btn:hover {
            color: darkred;
        }
        /* === 下载链接样式优化 === */
        .download-links a {
            border: 2px solid #e1e8f0;
            background: rgba(255,255,255,0.8);
            backdrop-filter: blur(5px);
            padding: 12px;
            border-radius: 12px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }

        .download-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.15);
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .download-links small {
            display: block;
            color: #666;
            margin-top: 3px;
        }


    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 材料基因算法在线系统</h1>
            <p>Material Genome Algorithm Online Platform</p>
        </div>

        <div class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <!-- 数据上传部分 -->
                <div class="section">
                    <h3>
                        <span class="section-icon">1</span>
                        数据上传
                    </h3>
                    <div class="form-group">
                        <label>CIF文件上传</label>
                        <div class="file-upload" id="cifDropZone" onclick="document.getElementById('cifFiles').click()">
                            <input type="file" id="cifFiles" multiple accept=".cif" onchange="handleFileUpload(this, 'cif')">
                            <div>📁 点击或拖拽上传CIF文件</div>
                            <div style="font-size: 12px; color: #666; margin-top: 5px;">支持多文件上传，最大100MB/文件</div>
                        </div>
                        <div class="collapse-card">
                            <div class="collapse-header" onclick="togglePanel('cifPanel', this)">
                                🧊 CIF 文件 <span class="toggle-icon">▶</span>
                            </div>
                            <div class="collapse-content" id="cifPanel" style="display: none;">
                                <div id="cifFileList" class="file-list"></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>CSV数据文件</label>
                        <div class="file-upload" id="csvDropZone" onclick="document.getElementById('csvFile').click()">
                            <input type="file" id="csvFile" accept=".csv" onchange="handleFileUpload(this, 'csv')">
                            <div>📊 点击或拖拽上传CSV文件</div>
                            <div style="font-size: 12px; color: #666; margin-top: 5px;">包含材料性质数据</div>
                        </div>
                        <div class="collapse-card">
                            <div class="collapse-header" onclick="togglePanel('csvPanel', this)">
                                📊 CSV 文件 <span class="toggle-icon">▶</span>
                            </div>
                            <div class="collapse-content" id="csvPanel" style="display: none;">
                                <div id="csvFileList" class="file-list"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 算法参数设置 -->
                <div class="section">
                    <h3>
                        <span class="section-icon">2</span>
                        算法参数
                    </h3>
                    <div class="parameter-grid">
                        <div class="form-group">
                            <label>目标性质值</label>
                            <input type="number" id="targetValue" value="12.04" step="0.01">
                        </div>
                        <div class="form-group">
                            <label>标签类型</label>
                            <select id="labelType">
                                <option value="boundary_right">右边界</option>
                                <option value="boundary_left">左边界</option>
                                <option value="interval">区间</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>优化次数</label>
                            <input type="number" id="nCalls" value="200" min="50" max="1000">
                        </div>
                        <div class="form-group">
                            <label>训练模式</label>
                            <select id="trainMode">
                                <option value="true">训练模式</option>
                                <option value="false">推理模式</option>
                            </select>
                        </div>
                    </div>
                    <!-- <div class="form-group">
                        <label>高级参数</label>
                        <details>
                            <summary style="cursor: pointer; padding: 5px; background: #f0f2f5; border-radius: 5px;">点击展开</summary>
                            <div style="margin-top: 10px;" class="parameter-grid">
                                <div class="form-group">
                                    <label>SOAP截断半径</label>
                                    <input type="number" id="soapCutoff" value="6.0" step="0.1">
                                </div>
                                <div class="form-group">
                                    <label>学习率</label>
                                    <input type="number" id="learningRate" value="0.001" step="0.0001">
                                </div>
                                <div class="form-group">
                                    <label>批次大小</label>
                                    <input type="number" id="batchSize" value="32" min="1" max="256">
                                </div>
                                <div class="form-group">
                                    <label>随机种子</label>
                                    <input type="number" id="randomSeed" value="42">
                                </div>
                            </div>
                        </details>
                    </div> -->
                </div>

                <!-- 算法流程控制 -->
                <div class="section">
                    <h3>
                        <span class="section-icon">3</span>
                        算法执行
                    </h3>
                    <div class="algorithm-flow">
                        <div class="flow-step" id="step-mining">挖掘</div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-step" id="step-identification">识别</div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-step" id="step-fusion">融合</div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-step" id="step-editing">编辑</div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-step" id="step-design">设计</div>
                    </div>
                    
                    <button class="btn" onclick="runGeneMining()" id="btnMining">
                        🔍 开始基因挖掘
                    </button>
                    <button class="btn btn-secondary" onclick="runGeneIdentification()" id="btnIdentification" disabled>
                        🧬 基因识别
                    </button>
                    <button class="btn btn-success" onclick="runGeneFusion()" id="btnFusion" disabled>
                        🔄 基因融合
                    </button>
                    <button class="btn btn-warning" onclick="runGeneEditing()" id="btnEditing" disabled>
                        ✏️ 基因编辑
                    </button>
                    <button class="btn" onclick="runGeneDesign()" id="btnDesign" disabled>
                        🎨 基因设计
                    </button>
                    
                    <div style="margin-top: 15px;">
                        <button class="btn" onclick="runFullPipeline()" style="width: 100%;" id="btnFullPipeline">
                            🚀 运行完整流程
                        </button>
                    </div>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div id="statusMessage" class="status info" style="display: none;">
                        准备就绪
                    </div>
                    
                    <!-- 任务ID显示 -->
                    <div id="taskInfo" style="margin-top: 10px; display: none;">
                        <small style="color: #666;">任务ID: <span id="currentTaskId"></span></small>
                    </div>
                </div>
            </div>

            <!-- 可视化区域 -->
            <div class="visualization-area">
                <div class="result-tabs">
                    <div class="tab active" onclick="switchTab('overview')">概览</div>
                    <div class="tab" onclick="switchTab('mining')">基因挖掘</div>
                    <div class="tab" onclick="switchTab('identification')">基因识别</div>
                    <div class="tab" onclick="switchTab('fusion')">基因融合</div>
                    <div class="tab" onclick="switchTab('editing')">基因编辑</div>
                    <div class="tab" onclick="switchTab('design')">基因设计</div>
                </div>

                <!-- 概览标签页 -->
                <div class="tab-content active" id="overview">
                    <h3>算法流程概览</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="fileCount">0</div>
                            <div class="stat-label">上传文件数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="processedCount">0</div>
                            <div class="stat-label">已处理样本</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="featureCount">0</div>
                            <div class="stat-label">特征维度</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="accuracyScore">0%</div>
                            <div class="stat-label">模型精度</div>
                        </div>
                    </div>
                    <div class="visualization-placeholder" id="overviewChart">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 10px;">📊</div>
                            <div>请上传数据并运行算法查看结果</div>
                        </div>
                    </div>
                </div>

                <!-- 基因挖掘结果 -->
                <div class="tab-content" id="mining">
                    <h3>基因挖掘结果</h3>
                    <div class="chart-container" id="miningChart" style="display: none;">
                        <canvas id="miningCanvas"></canvas>
                    </div>
                    <div class="visualization-placeholder" id="miningPlaceholder">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 10px;">⛏️</div>
                            <div>SOAP描述符和AFS特征提取结果将在此显示</div>
                        </div>
                    </div>
                    <div id="miningResults"></div>
                </div>

                <!-- 基因识别结果 -->
                <div class="tab-content" id="identification">
                    <h3>基因识别结果</h3>
                    <div class="chart-container" id="identificationChart" style="display: none;">
                        <canvas id="identificationCanvas"></canvas>
                    </div>
                    <div class="visualization-placeholder" id="identificationPlaceholder">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 10px;">🔬</div>
                            <div>CGCL和EGCL特征识别的t-SNE可视化将在此显示</div>
                        </div>
                    </div>
                    <div id="identificationResults"></div>
                </div>

                <!-- 基因融合结果 -->
                <div class="tab-content" id="fusion">
                    <h3>基因融合结果</h3>
                    <div class="chart-container" id="fusionChart" style="display: none;">
                        <canvas id="fusionCanvas"></canvas>
                    </div>
                    <div class="visualization-placeholder" id="fusionPlaceholder">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 10px;">🔄</div>
                            <div>CLIP模型融合特征的可视化将在此显示</div>
                        </div>
                    </div>
                    <div id="fusionResults"></div>
                </div>

                <!-- 基因编辑结果 -->
                <div class="tab-content" id="editing">
                    <h3>基因编辑结果</h3>
                    <div class="chart-container" id="editingChart" style="display: none;">
                        <canvas id="editingCanvas"></canvas>
                    </div>
                    <div class="visualization-placeholder" id="editingPlaceholder">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 10px;">✏️</div>
                            <div>SVM决策边界的2D/3D可视化将在此显示</div>
                        </div>
                    </div>
                    <div id="editingResults"></div>
                </div>

                <!-- 基因设计结果 -->
                <div class="tab-content" id="design">
                    <h3>基因设计结果</h3>
                    <div class="chart-container" id="designChart" style="display: none;">
                        <canvas id="designCanvas"></canvas>
                    </div>
                    <div class="visualization-placeholder" id="designPlaceholder">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 10px;">🎨</div>
                            <div>贝叶斯优化过程和结果可视化将在此显示</div>
                        </div>
                    </div>
                    <div id="designResults"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    
    <script>
        // API配置
        const API_BASE_URL = 'http://localhost:8000/api/v1';  // 根据实际后端地址修改
        const API_ENDPOINTS = {
            upload: `${API_BASE_URL}/upload`,
            mining: `${API_BASE_URL}/mining`,
            identification: `${API_BASE_URL}/identification`,
            fusion: `${API_BASE_URL}/fusion`,
            editing: `${API_BASE_URL}/editing`,
            design: `${API_BASE_URL}/design`,
            pipeline: `${API_BASE_URL}/pipeline`,
            // status: `${API_BASE_URL}/task/status`,
            // result: `${API_BASE_URL}/task/result`,
            download: `${API_BASE_URL}/download`
        };

        // 全局变量
        let uploadedFiles = {
            cif: [],
            csv: null
        };
        let currentTaskId = null;
        let currentResults = {};
        let isProcessing = false;
        let statusCheckInterval = null;
        let charts = {};

        // 初始化拖拽功能
        function initDragAndDrop() {
            const cifDropZone = document.getElementById('cifDropZone');
            const csvDropZone = document.getElementById('csvDropZone');

            [cifDropZone, csvDropZone].forEach(zone => {
                zone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    zone.classList.add('dragging');
                });

                zone.addEventListener('dragleave', () => {
                    zone.classList.remove('dragging');
                });

                zone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    zone.classList.remove('dragging');
                    
                    const files = Array.from(e.dataTransfer.files);
                    const type = zone.id === 'cifDropZone' ? 'cif' : 'csv';
                    
                    if (type === 'cif') {
                        const cifFiles = files.filter(f => f.name.endsWith('.cif'));
                        if (cifFiles.length > 0) {
                            handleDroppedFiles(cifFiles, 'cif');
                        }
                    } else {
                        const csvFiles = files.filter(f => f.name.endsWith('.csv'));
                        if (csvFiles.length > 0) {
                            handleDroppedFiles([csvFiles[0]], 'csv');
                        }
                    }
                });
            });
        }

        // 处理拖拽的文件
        function handleDroppedFiles(files, type) {
            if (type === 'cif') {
                uploadedFiles.cif = [...uploadedFiles.cif, ...files];
                displayFileList('cifFileList', uploadedFiles.cif, '🧊');
            } else if (type === 'csv') {
                uploadedFiles.csv = files[0];
                displayFileList('csvFileList', [files[0]], '📊');
            }
            updateFileCount();
            uploadFilesToServer(files, type);
        }

        // 文件上传处理
        function handleFileUpload(input, type) {
            const files = Array.from(input.files);
            if (type === 'cif') {
                uploadedFiles.cif = [...uploadedFiles.cif, ...files];
                displayFileList('cifFileList', uploadedFiles.cif, '🧊');
            } else if (type === 'csv') {
                uploadedFiles.csv = files[0];
                displayFileList('csvFileList', [files[0]], '📊');
            }
            updateFileCount();
            uploadFilesToServer(files, type);
        }

        async function uploadFilesToServer(files, type) {
            const formData = new FormData();

            // 判断是单个文件还是多个文件
            if (files.length === 1) {
                formData.append('file', files[0]); // 单个文件字段
            } else {
                files.forEach(file => {
                    formData.append('files', file); // 多文件字段
                });
            }
            formData.append('file_type', type); // ✅ 正确的参数名

            try {
                showStatus('正在上传文件...', 'info');
                
                const uploadEndpoint = files.length === 1 ? `${API_BASE_URL}/upload/file` : `${API_BASE_URL}/upload/files/batch`;
                
                const response = await fetch(uploadEndpoint, {
                    method: 'POST',
                    body: formData // 注意：上传文件不需要设置 Content-Type，由浏览器自动设置 multipart/form-data
                });

                if (!response.ok) {
                    throw new Error(`上传失败: ${response.statusText}`);
                }

                const result = await response.json();
                
                showStatus('文件上传成功', 'success');

                // ✅ 保存返回的文件ID
                if (result.file_id) {
                    if (type === 'cif') {
                        // 单文件
                        uploadedFiles.cifIds = [...(uploadedFiles.cifIds || []), result.file_id];
                    } else {
                        // csv
                        uploadedFiles.csvId = result.file_id;
                    }
                } else if (Array.isArray(result)) {
                    // 批量文件上传的响应
                    const fileIds = result.map(res => res.file_id).filter(id => id);
                    if (type === 'cif') {
                        uploadedFiles.cifIds = [...(uploadedFiles.cifIds || []), ...fileIds];
                    } else {
                        uploadedFiles.csvId = fileIds[0] || null;
                    }
                }

                updateFileCount();

            } catch (error) {
                showStatus(`文件上传失败: ${error.message}`, 'error');
                console.error('Upload error:', error);
            }
        }

        // 显示文件列表
        function togglePanel(panelId, headerEl) {
            const panel = document.getElementById(panelId);
            const isVisible = panel.style.display === 'block';
            panel.style.display = isVisible ? 'none' : 'block';
            headerEl.querySelector('.toggle-icon').textContent = isVisible ? '▶' : '▼';
        }
        function displayFileList(containerId, files, icon) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            if (!files || files.length === 0) return;
            files.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span>${icon} ${file.name} (${(file.size / 1024).toFixed(1)} KB)</span>
                    <span class="remove-btn" onclick="removeFile('${containerId}', ${index})">×</span>
                `;
                container.appendChild(fileItem);
            });
        }
        function removeFile(containerId, index) {
            if (containerId === 'cifFileList') {
                uploadedFiles.cif.splice(index, 1);
                displayFileList('cifFileList', uploadedFiles.cif, '🧊');
            } else if (containerId === 'csvFileList') {
                uploadedFiles.csv = null;
                displayFileList('csvFileList', [], '📊');
            }
            updateFileCount();
        }

        // 更新文件计数
        function updateFileCount() {
            const count = uploadedFiles.cif.length + (uploadedFiles.csv ? 1 : 0);
            document.getElementById('fileCount').textContent = count;
        }

        // 切换标签页
        function switchTab(tabName) {
            // 清除所有 tab 的 active 类
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // 根据名称找到当前 tab 并高亮
            const activeTab = document.querySelector(`.tab[onclick="switchTab('${tabName}')"]`);
            if (activeTab) {
                activeTab.classList.add('active');
            }

            // 显示对应的内容面板
            const content = document.getElementById(tabName);
            if (content) {
                content.classList.add('active');
            }
        }


        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('statusMessage');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }

        // 更新进度条
        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        // 更新流程步骤状态
        function updateFlowStep(stepName, status) {
            const stepEl = document.getElementById(`step-${stepName}`);
            if (stepEl) {
                stepEl.classList.remove('active', 'completed');
                if (status === 'active') {
                    stepEl.classList.add('active');
                } else if (status === 'completed') {
                    stepEl.classList.add('completed');
                }
            }
        }

        // API请求封装
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || `请求失败: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                throw error;
            }
        }

        // 启用/禁用按钮
        function setButtonState(buttonId, enabled) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.disabled = !enabled;
            }
        }

        //基因挖掘
        async function runGeneMining() {
            if (uploadedFiles.cif.length === 0) {
                showStatus('请先上传CIF文件', 'error');
                return;
            }

            if (isProcessing) {
                showStatus('请等待当前任务完成', 'error');
                return;
            }

            isProcessing = true;
            
            updateFlowStep('mining', 'active');
            showStatus('正在执行基因挖掘...', 'info');
            updateProgress(0);

            try {
                const params = {
                    cif_files: uploadedFiles.cifIds,  // ✅ 保持用 cif_files 字段
                    use_soap: true,  // 假设保持默认开启（根据你的需求可以改为 false）
                    use_afs: true    // 同上，保持默认值
                };

                const result = await apiRequest(`${API_ENDPOINTS.mining}/start`, {
                    method: 'POST',
                    body: JSON.stringify(params)
                });
                console.log('基因挖掘任务提交成功:', result);
                currentTaskId = result.task_id;
                document.getElementById('currentTaskId').textContent = currentTaskId;
                document.getElementById('taskInfo').style.display = 'block';

                // 开始轮询任务状态
                console.log(`开始轮询任务状态，当前任务ID: ${currentTaskId}`);
                await pollTaskStatus('mining');

            } catch (error) {
                showStatus(`基因挖掘失败: ${error.message}`, 'error');
                updateFlowStep('mining', '');
                isProcessing = false;
            }
        }

        // 基因识别
        async function runGeneIdentification() {
            if (!currentResults.mining) {
                showStatus('请先完成基因挖掘', 'error');
                return;
            }

            if (isProcessing) {
                showStatus('请等待当前任务完成', 'error');
                return;
            }
            console.log('开始基因识别1');
            isProcessing = true;
            updateFlowStep('identification', 'active');
            // 更新状态消息
            console.log('正在执行基因识别...');
            showStatus('正在执行基因识别...', 'info');
            updateProgress(0);

            try {
                console.log('开始基因识别2');
                // const params = {
                //     miningResult: currentResults.mining.outputId,
                //     learningRate: parseFloat(document.getElementById('learningRate').value),
                //     batchSize: parseInt(document.getElementById('batchSize').value)
                // };
                const params = {
                    miningResult: currentResults.mining.outputId,
                    learningRate: 0.001,
                    batchSize: 32
                };
                console.log('基因识别参数:', params);
                const result = await apiRequest(`${API_ENDPOINTS.identification}/start`, {
                    method: 'POST',
                    body: JSON.stringify(params)
                });
                console.log('基因识别任务提交成功:', result);
                currentTaskId = result.task_id;
                document.getElementById('currentTaskId').textContent = currentTaskId;
                console.log(`开始轮询任务状态，当前任务ID: ${currentTaskId}`);
                await pollTaskStatus('identification');
                
            } catch (error) {
                showStatus(`基因识别失败: ${error.message}`, 'error');
                updateFlowStep('identification', '');
                isProcessing = false;
            }
        }

        // 基因融合
        async function runGeneFusion() {
            if (!uploadedFiles.csv) {
                showStatus('请先上传CSV数据文件', 'error');
                return;
            }

            if (!currentResults.identification) {
                showStatus('请先完成基因识别', 'error');
                return;
            }

            if (isProcessing) {
                showStatus('请等待当前任务完成', 'error');
                return;
            }

            isProcessing = true;
            updateFlowStep('fusion', 'active');
            showStatus('正在执行基因融合...', 'info');
            updateProgress(0);

            try {
                const params = {
                    identificationResult: currentResults.identification.outputId,
                    csvFileId: uploadedFiles.csvId,
                    targetValue: parseFloat(document.getElementById('targetValue').value)
                };

                const result = await apiRequest(API_ENDPOINTS.fusion, {
                    method: 'POST',
                    body: JSON.stringify(params)
                });

                currentTaskId = result.taskId;
                document.getElementById('currentTaskId').textContent = currentTaskId;

                await pollTaskStatus('fusion');
                
            } catch (error) {
                showStatus(`基因融合失败: ${error.message}`, 'error');
                updateFlowStep('fusion', '');
                isProcessing = false;
            }
        }

        // 基因编辑
        async function runGeneEditing() {
            if (!currentResults.fusion) {
                showStatus('请先完成基因融合', 'error');
                return;
            }

            if (isProcessing) {
                showStatus('请等待当前任务完成', 'error');
                return;
            }

            isProcessing = true;
            updateFlowStep('editing', 'active');
            showStatus('正在执行基因编辑...', 'info');
            updateProgress(0);

            try {
                const params = {
                    fusionResult: currentResults.fusion.outputId,
                    targetValue: parseFloat(document.getElementById('targetValue').value),
                    labelType: document.getElementById('labelType').value
                };

                const result = await apiRequest(API_ENDPOINTS.editing, {
                    method: 'POST',
                    body: JSON.stringify(params)
                });

                currentTaskId = result.taskId;
                document.getElementById('currentTaskId').textContent = currentTaskId;

                await pollTaskStatus('editing');
                
            } catch (error) {
                showStatus(`基因编辑失败: ${error.message}`, 'error');
                updateFlowStep('editing', '');
                isProcessing = false;
            }
        }

        // 基因设计
        async function runGeneDesign() {
            if (!currentResults.editing) {
                showStatus('请先完成基因编辑', 'error');
                return;
            }

            if (isProcessing) {
                showStatus('请等待当前任务完成', 'error');
                return;
            }

            isProcessing = true;
            updateFlowStep('design', 'active');
            showStatus('正在执行基因设计...', 'info');
            updateProgress(0);

            try {
                const params = {
                    editingResult: currentResults.editing.outputId,
                    nCalls: parseInt(document.getElementById('nCalls').value),
                    trainMode: document.getElementById('trainMode').value === 'true'
                };

                const result = await apiRequest(API_ENDPOINTS.design, {
                    method: 'POST',
                    body: JSON.stringify(params)
                });

                currentTaskId = result.taskId;
                document.getElementById('currentTaskId').textContent = currentTaskId;

                await pollTaskStatus('design');
                
            } catch (error) {
                showStatus(`基因设计失败: ${error.message}`, 'error');
                updateFlowStep('design', '');
                isProcessing = false;
            }
        }

        // 运行完整流程
        async function runFullPipeline() {
            if (uploadedFiles.cif.length === 0 || !uploadedFiles.csv) {
                showStatus('请先上传CIF文件和CSV数据文件', 'error');
                return;
            }
            
            if (isProcessing) {
                showStatus('请等待当前任务完成', 'error');
                return;
            }

            isProcessing = true;
            showStatus('正在执行完整算法流程...', 'info');
            updateProgress(0);

            try {
                const params = {
                    cifFileIds: uploadedFiles.cifIds,
                    csvFileId: uploadedFiles.csvId,
                    targetValue: parseFloat(document.getElementById('targetValue').value),
                    labelType: document.getElementById('labelType').value,
                    nCalls: parseInt(document.getElementById('nCalls').value),
                    trainMode: document.getElementById('trainMode').value === 'true',
                    soapCutoff: parseFloat(document.getElementById('soapCutoff').value),
                    learningRate: parseFloat(document.getElementById('learningRate').value),
                    batchSize: parseInt(document.getElementById('batchSize').value),
                    randomSeed: parseInt(document.getElementById('randomSeed').value)
                };

                const result = await apiRequest(API_ENDPOINTS.pipeline, {
                    method: 'POST',
                    body: JSON.stringify(params)
                });

                currentTaskId = result.taskId;
                document.getElementById('currentTaskId').textContent = currentTaskId;
                document.getElementById('taskInfo').style.display = 'block';

                await pollTaskStatus('pipeline');
                
            } catch (error) {
                showStatus(`执行失败: ${error.message}`, 'error');
                isProcessing = false;
            }
        }

        // 轮询任务状态
        async function pollTaskStatus(taskType) {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }

            statusCheckInterval = setInterval(async () => {
                try {
                    console.log(`Checking status for task: ${taskType}, ID: ${currentTaskId}`);
                    const statusEndpoint = `${API_ENDPOINTS[taskType]}/status`;
                    const status = await apiRequest(`${statusEndpoint}/${currentTaskId}`);
                    
                    updateProgress(status.progress || 0);
                    
                    if (status.status === 'completed') {
                        clearInterval(statusCheckInterval);
                        await handleTaskCompletion(taskType, status);
                    } else if (status.status === 'failed') {
                        clearInterval(statusCheckInterval);
                        throw new Error(status.error || '任务执行失败');
                    } else {
                        // 更新当前步骤状态
                        if (status.currentStep) {
                            updateFlowStep(status.currentStep, 'active');
                        }
                        showStatus(`${status.message || '处理中'}... (${status.progress}%)`, 'info');
                    }
                } catch (error) {
                    clearInterval(statusCheckInterval);
                    showStatus(`任务失败: ${error.message}`, 'error');
                    updateFlowStep(taskType, '');
                    isProcessing = false;
                }
            }, 2000);
        }

        // 处理任务完成
        async function handleTaskCompletion(taskType, status) {
            try {
                console.log(`get result for task: ${taskType}, ID: ${currentTaskId}`);
                const resultEndpoint = `${API_ENDPOINTS[taskType]}/result`;
                const result = await apiRequest(`${resultEndpoint}/${currentTaskId}`);
                
                if (taskType === 'pipeline') {
                    // 完整流程的结果
                    currentResults = result.results;
                    updateAllResults();
                    updateFlowStep('design', 'completed');
                    setButtonState('btnDesign', true);
                } else {
                    // 单步骤的结果
                    currentResults[taskType] = result;
                    console.log(currentResults);
                    updateSingleResult(taskType, result);
                    console.log(`更新 ${taskType} 结果:`, result);
                    updateFlowStep(taskType, 'completed');
                    console.log(`任务 ${taskType} 完成，结果:`, result);
                    
                    // 启用下一步按钮
                    const nextSteps = {
                        'mining': 'btnIdentification',
                        'identification': 'btnFusion',
                        'fusion': 'btnEditing',
                        'editing': 'btnDesign'
                    };
                    if (nextSteps[taskType]) {
                        setButtonState(nextSteps[taskType], true);
                    }
                }
                
                showStatus(`${getTaskName(taskType)}完成`, 'success');
                isProcessing = false;
                
            } catch (error) {
                showStatus(`获取结果失败: ${error.message}`, 'error');
                isProcessing = false;
            }
        }

        // 获取任务名称
        function getTaskName(taskType) {
            const names = {
                'mining': '基因挖掘',
                'identification': '基因识别',
                'fusion': '基因融合',
                'editing': '基因编辑',
                'design': '基因设计',
                'pipeline': '完整流程'
            };
            return names[taskType] || taskType;
        }

        // 更新单个结果
        function updateSingleResult(taskType, result) {
            switch (taskType) {
                case 'mining':
                    updateMiningResults(result);
                    break;
                case 'identification':
                    updateIdentificationResults(result);
                    break;
                case 'fusion':
                    updateFusionResults(result);
                    break;
                case 'editing':
                    updateEditingResults(result);
                    break;
                case 'design':
                    updateDesignResults(result);
                    break;
            }
            updateStats();
        }

        // 更新基因挖掘结果
        function updateMiningResults(result) {
            console.log('更新基因挖掘结果:', result);
            const resultsContainer = document.getElementById('miningResults');
            const downloadBaseUrl = `${API_BASE_URL}/mining/download`;
            console.log('下载链接基础地址:', downloadBaseUrl);
            const miningChart = document.getElementById('miningChart');
            const placeholder = document.getElementById('miningPlaceholder');
            if (miningChart) miningChart.style.display = "none";
            if (placeholder) placeholder.style.display = "none";
            resultsContainer.innerHTML = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <h4>🔍 SOAP描述符提取结果</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 10px 0;">
                        <div style="background: white; padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-size: 1.5em; color: #667eea; font-weight: bold;">${result.processed_samples}</div>
                            <div style="font-size: 0.8em; color: #666;">处理材料数</div>
                        </div>
                        <div style="background: white; padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-size: 1.5em; color: #667eea; font-weight: bold;">${result.feature_dimensions.soap_dim}</div>
                            <div style="font-size: 0.8em; color: #666;">SOAP特征维度</div>
                        </div>
                        <div style="background: white; padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-size: 1.5em; color: #667eea; font-weight: bold;">${result.feature_dimensions.afs_dim}</div>
                            <div style="font-size: 0.8em; color: #666;">AFS特征维度</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 5px; border-left: 4px solid #4caf50;">
                        ✅ 材料分子式解析完成<br>
                        ✅ 原子环境描述符计算完成<br>
                        ✅ 原子特征统计量提取完成<br>
                    </div>
                    <div style="margin-top: 20px; border-top: 1px solid #eee; padding-top: 15px;">
                    <h5>特征数据下载</h5>
                    <div class="download-links" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 10px;">
                        <!-- AFS特征下载 -->
                        <a href="${downloadBaseUrl}/afs/${result.task_id}" 
                        class="btn" 
                        download
                        style="display: flex; align-items: center; justify-content: center;">
                            <span style="margin-right: 10px;">⬇️</span>
                            <div>
                                <div>AFS特征文件</div>
                                <small style="font-size: 0.8em;">.json格式 (统计特征)</small>
                            </div>
                        </a>
                        <!-- SOAP特征下载 -->
                        <a href="${downloadBaseUrl}/soap/${result.task_id}" 
                        class="btn" 
                        download
                        style="display: flex; align-items: center; justify-content: center;">
                            <span style="margin-right: 10px;">⬇️</span>
                            <div>
                                <div>SOAP特征文件</div>
                                <small style="font-size: 0.8em;">.npy格式 (矩阵数据)</small>
                            </div>
                        </a>
                    </div>
                    <div style="color: #666; font-size: 0.9em; margin-top: 10px;">
                        💡 文件说明：<br>
                        - AFS特征：原子频次统计特征，包含化学组成信息<br>
                        - SOAP特征：三维原子环境描述符矩阵
                    </div>
                </div>
                </div>
            `;

            // // 显示可视化
            // if (result.visualization) {
            //     displayVisualization('mining', result.visualization);
            // }
            console.log('基因挖掘结果更新完成:', result);
            switchTab('mining');
            console.log('切换到基因挖掘标签页');
        }

        // 更新基因识别结果
        function updateIdentificationResults(result) {
            const resultsContainer = document.getElementById('identificationResults');
            resultsContainer.innerHTML = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <h4>🧬 深度学习特征识别结果</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #ff6b6b;">
                            <h5 style="margin: 0 0 10px 0; color: #ff6b6b;">CGCL模型</h5>
                            <p style="margin: 5px 0; font-size: 14px;">📊 输出维度: ${result.cgclDim}</p>
                            <p style="margin: 5px 0; font-size: 14px;">⚡ 推理时间: ${result.cgclTime}s</p>
                            <p style="margin: 5px 0; font-size: 14px;">🎯 对比损失: ${result.cgclLoss}</p>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #4ecdc4;">
                            <h5 style="margin: 0 0 10px 0; color: #4ecdc4;">EGCL模型</h5>
                            <p style="margin: 5px 0; font-size: 14px;">📊 输出维度: ${result.egclDim}</p>
                            <p style="margin: 5px 0; font-size: 14px;">⚡ 推理时间: ${result.egclTime}s</p>
                            <p style="margin: 5px 0; font-size: 14px;">🎯 角度特征: ${result.angleFeatures ? '完整' : '部分'}</p>
                        </div>
                    </div>
                    <div style="background: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107; margin-top: 15px;">
                        📈 t-SNE降维可视化已生成，显示了良好的聚类结构
                    </div>
                </div>
            `;

            if (result.visualization) {
                displayVisualization('identification', result.visualization);
            }
            
            switchTab('identification');
        }

        // 更新基因融合结果
        function updateFusionResults(result) {
            const resultsContainer = document.getElementById('fusionResults');
            resultsContainer.innerHTML = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <h4>🔄 多模态特征融合结果</h4>
                    <div style="background: white; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h5 style="margin: 0;">CLIP模型性能指标</h5>
                            <span style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">${result.performance}</span>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                                <div style="font-size: 1.2em; font-weight: bold; color: #667eea;">${result.fusionDim}</div>
                                <div style="font-size: 0.8em; color: #666;">融合特征维度</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                                <div style="font-size: 1.2em; font-weight: bold; color: #667eea;">${result.accuracy}%</div>
                                <div style="font-size: 0.8em; color: #666;">匹配精度</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                                <div style="font-size: 1.2em; font-weight: bold; color: #667eea;">${result.contrastiveLoss}</div>
                                <div style="font-size: 0.8em; color: #666;">对比损失</div>
                            </div>
                        </div>
                    </div>
                    <div style="background: #d1ecf1; padding: 10px; border-radius: 5px; border-left: 4px solid #17a2b8;">
                        🎯 结构-性质关系建模成功，可用于后续的材料设计优化
                    </div>
                </div>
            `;

            if (result.visualization) {
                displayVisualization('fusion', result.visualization);
            }
            
            switchTab('fusion');
        }

        // 更新基因编辑结果
        function updateEditingResults(result) {
            const targetValue = document.getElementById('targetValue').value;
            const labelType = document.getElementById('labelType').value;
            const resultsContainer = document.getElementById('editingResults');
            resultsContainer.innerHTML = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <h4>✏️ 智能决策边界构建结果</h4>
                    <div style="background: white; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <h5 style="color: #667eea; margin-bottom: 10px;">设置参数</h5>
                                <p style="margin: 5px 0;"><strong>目标性质值:</strong> ${targetValue}</p>
                                <p style="margin: 5px 0;"><strong>标签类型:</strong> ${labelType}</p>
                                <p style="margin: 5px 0;"><strong>核函数:</strong> ${result.kernel}</p>
                                <p style="margin: 5px 0;"><strong>正则化参数:</strong> C=${result.C}</p>
                            </div>
                            <div>
                                <h5 style="color: #667eea; margin-bottom: 10px;">模型性能</h5>
                                <p style="margin: 5px 0;"><strong>分类精度:</strong> ${result.accuracy}%</p>
                                <p style="margin: 5px 0;"><strong>正样本数:</strong> ${result.positiveSamples}</p>
                                <p style="margin: 5px 0;"><strong>负样本数:</strong> ${result.negativeSamples}</p>
                                <p style="margin: 5px 0;"><strong>支持向量数:</strong> ${result.supportVectors}</p>
                            </div>
                        </div>
                    </div>
                    <div style="background: #f8d7da; padding: 10px; border-radius: 5px; border-left: 4px solid #dc3545; margin-top: 15px;">
                        🎨 2D/3D决策边界可视化已生成，可清晰观察目标区域分布
                    </div>
                </div>
            `;

            if (result.visualization) {
                displayVisualization('editing', result.visualization);
            }
            
            switchTab('editing');
        }

        // 更新基因设计结果
        function updateDesignResults(result) {
            const nCalls = document.getElementById('nCalls').value;
            const resultsContainer = document.getElementById('designResults');
            resultsContainer.innerHTML = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <h4>🎨 智能材料设计结果</h4>
                    <div style="background: white; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h5 style="margin: 0;">贝叶斯优化结果</h5>
                            <span style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">${result.convergenceStatus}</span>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 15px;">
                            <div style="text-align: center; padding: 10px; background: #e8f5e8; border-radius: 5px;">
                                <div style="font-size: 1.3em; font-weight: bold; color: #28a745;">${result.optimalValue}</div>
                                <div style="font-size: 0.8em; color: #666;">最优目标值</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: #fff3cd; border-radius: 5px;">
                                <div style="font-size: 1.3em; font-weight: bold; color: #ffc107;">${nCalls}</div>
                                <div style="font-size: 0.8em; color: #666;">总迭代次数</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: #d1ecf1; border-radius: 5px;">
                                <div style="font-size: 1.3em; font-weight: bold; color: #17a2b8;">${result.convergenceIteration}</div>
                                <div style="font-size: 0.8em; color: #666;">收敛代数</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: #f8d7da; border-radius: 5px;">
                                <div style="font-size: 1.3em; font-weight: bold; color: #dc3545;">${result.convergenceError}</div>
                                <div style="font-size: 0.8em; color: #666;">收敛误差</div>
                            </div>
                        </div>
                        <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <h6 style="margin: 0 0 5px 0;">最优设计参数 (前5维):</h6>
                            <code style="font-size: 12px;">
                                [${result.optimalParams ? result.optimalParams.slice(0, 5).join(', ') : ''}...]
                            </code>
                        </div>
                    </div>
                    <div style="background: #d4edda; padding: 10px; border-radius: 5px; border-left: 4px solid #28a745; margin-top: 15px;">
                        🎬 优化过程动态GIF已生成<br>
                        📊 收敛曲线和最优解可视化已完成<br>
                        🎯 新材料设计方案已输出<br>
                        <button class="btn btn-sm" onclick="downloadResults('${result.outputId}')" style="margin-top: 10px; padding: 5px 15px; font-size: 12px;">
                            📥 下载设计结果
                        </button>
                    </div>
                </div>
            `;

            if (result.visualization) {
                displayVisualization('design', result.visualization);
            }
            
            switchTab('design');
        }

        // 显示可视化结果
        function displayVisualization(type, data) {
            const chartContainer = document.getElementById(`${type}Chart`);
            const placeholder = document.getElementById(`${type}Placeholder`);
            
            if (placeholder) placeholder.style.display = 'none';
            if (chartContainer) chartContainer.style.display = 'block';
            
            const canvas = document.getElementById(`${type}Canvas`);
            if (!canvas) return;
            
            // 销毁旧图表
            if (charts[type]) {
                charts[type].destroy();
            }
            
            // 根据不同类型创建不同的图表
            switch(type) {
                case 'mining':
                    //createFeatureChart(canvas, data);
                    break;
                case 'identification':
                    createTSNEChart(canvas, data);
                    break;
                case 'fusion':
                    createFusionChart(canvas, data);
                    break;
                case 'editing':
                    createDecisionBoundaryChart(canvas, data);
                    break;
                case 'design':
                    createOptimizationChart(canvas, data);
                    break;
            }
        }

        // 创建特征图表
        function createFeatureChart(canvas, data) {
            const ctx = canvas.getContext('2d');
            charts.mining = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.labels || ['SOAP特征', 'AFS特征'],
                    datasets: [{
                        label: '特征重要性',
                        data: data.values || [0.75, 0.85],
                        backgroundColor: ['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.8)'],
                        borderColor: ['#667eea', '#764ba2'],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: '特征提取结果'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 1
                        }
                    }
                }
            });
        }

        // 创建t-SNE可视化
        function createTSNEChart(canvas, data) {
            const ctx = canvas.getContext('2d');
            charts.identification = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'CGCL特征',
                        data: data.cgcl || [],
                        backgroundColor: 'rgba(255, 107, 107, 0.6)',
                        borderColor: '#ff6b6b',
                        pointRadius: 5
                    }, {
                        label: 'EGCL特征',
                        data: data.egcl || [],
                        backgroundColor: 'rgba(78, 205, 196, 0.6)',
                        borderColor: '#4ecdc4',
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: 't-SNE降维可视化'
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: 't-SNE 1'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 't-SNE 2'
                            }
                        }
                    }
                }
            });
        }

        // 创建融合特征图表
        function createFusionChart(canvas, data) {
            const ctx = canvas.getContext('2d');
            charts.fusion = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: data.labels || ['结构特征', '成分特征', '性质特征', '电子特征', '热力学特征'],
                    datasets: [{
                        label: '融合前',
                        data: data.before || [0.65, 0.70, 0.75, 0.60, 0.68],
                        borderColor: 'rgba(255, 107, 107, 0.8)',
                        backgroundColor: 'rgba(255, 107, 107, 0.2)',
                        pointBackgroundColor: '#ff6b6b',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: '#ff6b6b'
                    }, {
                        label: '融合后',
                        data: data.after || [0.85, 0.88, 0.92, 0.87, 0.90],
                        borderColor: 'rgba(102, 126, 234, 0.8)',
                        backgroundColor: 'rgba(102, 126, 234, 0.2)',
                        pointBackgroundColor: '#667eea',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: '#667eea'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: '多模态特征融合效果'
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 1
                        }
                    }
                }
            });
        }

        // 创建决策边界图表
        function createDecisionBoundaryChart(canvas, data) {
            const ctx = canvas.getContext('2d');
            charts.editing = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: '正样本',
                        data: data.positive || [],
                        backgroundColor: 'rgba(40, 167, 69, 0.6)',
                        borderColor: '#28a745',
                        pointRadius: 6
                    }, {
                        label: '负样本',
                        data: data.negative || [],
                        backgroundColor: 'rgba(220, 53, 69, 0.6)',
                        borderColor: '#dc3545',
                        pointRadius: 6
                    }, {
                        label: '支持向量',
                        data: data.supportVectors || [],
                        backgroundColor: 'rgba(255, 193, 7, 0.8)',
                        borderColor: '#ffc107',
                        pointRadius: 8,
                        pointStyle: 'star'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: 'SVM决策边界可视化'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '特征1'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '特征2'
                            }
                        }
                    }
                }
            });
        }

        // 创建优化过程图表
        function createOptimizationChart(canvas, data) {
            const ctx = canvas.getContext('2d');
            charts.design = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.iterations || Array.from({length: 200}, (_, i) => i + 1),
                    datasets: [{
                        label: '目标函数值',
                        data: data.objectiveValues || [],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.2,
                        fill: true
                    }, {
                        label: '最优值',
                        data: data.bestValues || [],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.2,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: '贝叶斯优化收敛过程'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '迭代次数'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '目标值'
                            }
                        }
                    }
                }
            });
        }

        // 更新所有结果
        async function updateAllResults() {
            if (currentResults.mining) updateMiningResults(currentResults.mining);
            if (currentResults.identification) updateIdentificationResults(currentResults.identification);
            if (currentResults.fusion) updateFusionResults(currentResults.fusion);
            if (currentResults.editing) updateEditingResults(currentResults.editing);
            if (currentResults.design) updateDesignResults(currentResults.design);
            updateStats();
        }

        // 更新统计信息
        function updateStats() {
            if (currentResults.mining) {
                document.getElementById('processedCount').textContent = currentResults.mining.processedCount || '0';
            }
            if (currentResults.fusion) {
                document.getElementById('featureCount').textContent = currentResults.fusion.fusionDim || '0';
            }
            if (currentResults.editing) {
                document.getElementById('accuracyScore').textContent = `${currentResults.editing.accuracy || 0}%`;
            }
        }

        // 下载结果
        async function downloadResults(outputId) {
            try {
                showStatus('准备下载结果...', 'info');
                
                const response = await fetch(`${API_ENDPOINTS.download}/${outputId}`);
                if (!response.ok) {
                    throw new Error('下载失败');
                }
                
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `material_design_results_${outputId}.zip`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                showStatus('结果下载成功', 'success');
            } catch (error) {
                showStatus(`下载失败: ${error.message}`, 'error');
            }
        }

        // 清除所有数据
        function clearAllData() {
            if (confirm('确定要清除所有数据吗？此操作不可恢复。')) {
                uploadedFiles = { cif: [], csv: null };
                currentResults = {};
                currentTaskId = null;
                
                document.getElementById('cifFileList').innerHTML = '';
                document.getElementById('csvFileList').innerHTML = '';
                document.querySelectorAll('[id$="Results"]').forEach(el => el.innerHTML = '');
                updateProgress(0);
                showStatus('所有数据已清除', 'info');
                updateFileCount();
                
                // 重置统计信息
                document.getElementById('processedCount').textContent = '0';
                document.getElementById('featureCount').textContent = '0';
                document.getElementById('accuracyScore').textContent = '0%';
                
                // 重置流程步骤
                document.querySelectorAll('.flow-step').forEach(el => {
                    el.classList.remove('active', 'completed');
                });
                
                // 禁用除第一个外的所有按钮
                setButtonState('btnMining', true);
                setButtonState('btnIdentification', false);
                setButtonState('btnFusion', false);
                setButtonState('btnEditing', false);
                setButtonState('btnDesign', false);
                
                // 隐藏所有图表
                Object.keys(charts).forEach(key => {
                    if (charts[key]) {
                        charts[key].destroy();
                        delete charts[key];
                    }
                });
                
                // 显示占位符
                document.querySelectorAll('.visualization-placeholder').forEach(el => {
                    el.style.display = 'flex';
                });
                document.querySelectorAll('.chart-container').forEach(el => {
                    el.style.display = 'none';
                });
            }
        }

        // 导出配置
        function exportConfig() {
            const config = {
                targetValue: document.getElementById('targetValue').value,
                labelType: document.getElementById('labelType').value,
                nCalls: document.getElementById('nCalls').value,
                trainMode: document.getElementById('trainMode').value,
                soapCutoff: document.getElementById('soapCutoff').value,
                learningRate: document.getElementById('learningRate').value,
                batchSize: document.getElementById('batchSize').value,
                randomSeed: document.getElementById('randomSeed').value,
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'material_genome_config.json';
            a.click();
            URL.revokeObjectURL(url);
            
            showStatus('配置文件已导出', 'success');
        }

        // 导入配置
        function importConfig() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const config = JSON.parse(e.target.result);
                            document.getElementById('targetValue').value = config.targetValue || '12.04';
                            document.getElementById('labelType').value = config.labelType || 'boundary_right';
                            document.getElementById('nCalls').value = config.nCalls || '200';
                            document.getElementById('trainMode').value = config.trainMode || 'true';
                            document.getElementById('soapCutoff').value = config.soapCutoff || '6.0';
                            document.getElementById('learningRate').value = config.learningRate || '0.001';
                            document.getElementById('batchSize').value = config.batchSize || '32';
                            document.getElementById('randomSeed').value = config.randomSeed || '42';
                            showStatus('配置文件导入成功', 'success');
                        } catch (error) {
                            showStatus('配置文件格式错误', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 显示帮助信息
        function showHelp() {
            const helpContent = `
                <div style="max-width: 600px; line-height: 1.6;">
                    <h3>🧬 材料基因算法系统使用指南</h3>
                    <br>
                    <h4>📤 数据上传</h4>
                    <p>• 支持拖拽上传或点击选择文件</p>
                    <p>• CIF文件：晶体结构文件，支持批量上传</p>
                    <p>• CSV文件：包含材料性质数据，单文件上传</p>
                    <br>
                    <h4>🔧 算法流程</h4>
                    <p><strong>1. 基因挖掘:</strong> 提取SOAP描述符和AFS特征</p>
                    <p><strong>2. 基因识别:</strong> 使用CGCL和EGCL深度学习模型</p>
                    <p><strong>3. 基因融合:</strong> CLIP模型融合多模态特征</p>
                    <p><strong>4. 基因编辑:</strong> SVM构建决策边界</p>
                    <p><strong>5. 基因设计:</strong> 贝叶斯优化材料设计</p>
                    <br>
                    <h4>⚙️ 参数说明</h4>
                    <p>• <strong>目标性质值:</strong> 期望达到的材料性质数值</p>
                    <p>• <strong>标签类型:</strong> 分类边界类型设置</p>
                    <p>• <strong>优化次数:</strong> 贝叶斯优化迭代次数</p>
                    <p>• <strong>训练模式:</strong> 训练新模型或使用已有模型</p>
                    <br>
                    <h4>⌨️ 快捷键</h4>
                    <p>• Ctrl+S: 导出配置</p>
                    <p>• Ctrl+O: 导入配置</p>
                    <p>• Ctrl+R: 运行完整流程</p>
                    <p>• Esc: 关闭弹窗</p>
                    <br>
                    <h4>💡 使用建议</h4>
                    <p>• 建议先运行单步骤熟悉流程</p>
                    <p>• 大批量数据建议分批处理</p>
                    <p>• 定期保存配置文件</p>
                    <p>• 查看可视化结果评估效果</p>
                </div>
            `;
            
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.innerHTML = `
                <div class="modal-content">
                    ${helpContent}
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn" onclick="this.closest('.modal').remove()">确定</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 点击外部关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }

        // 页面加载完成时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            showStatus('系统初始化完成，请上传数据文件开始分析', 'info');
            
            // 初始化拖拽功能
            initDragAndDrop();
            
            // 添加键盘快捷键支持
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey) {
                    switch(e.key) {
                        case 's':
                            e.preventDefault();
                            exportConfig();
                            break;
                        case 'o':
                            e.preventDefault();
                            importConfig();
                            break;
                        case 'r':
                            e.preventDefault();
                            runFullPipeline();
                            break;
                    }
                } else if (e.key === 'Escape') {
                    // 关闭所有模态框
                    document.querySelectorAll('.modal').forEach(modal => modal.remove());
                }
            });
            
            // 定期保存进度（防止意外关闭）
            setInterval(() => {
                if (currentTaskId && isProcessing) {
                    localStorage.setItem('lastTaskId', currentTaskId);
                    localStorage.setItem('lastConfig', JSON.stringify({
                        targetValue: document.getElementById('targetValue').value,
                        labelType: document.getElementById('labelType').value,
                        nCalls: document.getElementById('nCalls').value,
                        trainMode: document.getElementById('trainMode').value
                    }));
                }
            }, 30000); // 每30秒保存一次
            
            // 检查是否有未完成的任务
            const lastTaskId = localStorage.getItem('lastTaskId');
            if (lastTaskId) {
                if (confirm('检测到未完成的任务，是否恢复？')) {
                    currentTaskId = lastTaskId;
                    document.getElementById('currentTaskId').textContent = currentTaskId;
                    document.getElementById('taskInfo').style.display = 'block';
                    pollTaskStatus('unknown');
                }
            }
        });
    </script>

    <!-- 添加底部工具栏 -->
    <div style="position: fixed; bottom: 20px; right: 20px; display: flex; gap: 10px; z-index: 1000;">
        <button class="btn" onclick="exportConfig()" title="导出配置 (Ctrl+S)">
            💾 导出配置
        </button>
        <button class="btn btn-secondary" onclick="importConfig()" title="导入配置 (Ctrl+O)">
            📁 导入配置
        </button>
        <button class="btn btn-warning" onclick="clearAllData()" title="清除数据">
            🗑️ 清除数据
        </button>
    </div>

    <!-- 添加帮助按钮 -->
    <div style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
        <button class="btn" onclick="showHelp()" title="帮助信息">
            ❓ 帮助
        </button>
    </div>
</body>
</html>