"""
自定义异常类
定义系统中使用的各种异常类型
"""

from typing import Optional, Dict, Any


class MaterialGeneAPIException(Exception):
    """材料基因API基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class FileHandlingError(MaterialGeneAPIException):
    """文件处理相关异常"""
    
    def __init__(
        self, 
        message: str, 
        file_id: Optional[str] = None,
        file_type: Optional[str] = None
    ):
        super().__init__(
            message=message,
            error_code="FILE_HANDLING_ERROR",
            details={"file_id": file_id, "file_type": file_type}
        )


class AlgorithmError(MaterialGeneAPIException):
    """算法执行相关异常"""
    
    def __init__(
        self, 
        message: str, 
        algorithm_type: Optional[str] = None,
        stage: Optional[str] = None
    ):
        super().__init__(
            message=message,
            error_code="ALGORITHM_ERROR",
            details={"algorithm_type": algorithm_type, "stage": stage}
        )


class TaskManagerError(MaterialGeneAPIException):
    """任务管理相关异常"""
    
    def __init__(
        self, 
        message: str, 
        task_id: Optional[str] = None,
        task_type: Optional[str] = None
    ):
        super().__init__(
            message=message,
            error_code="TASK_MANAGER_ERROR",
            details={"task_id": task_id, "task_type": task_type}
        )


class ValidationError(MaterialGeneAPIException):
    """数据验证相关异常"""
    
    def __init__(
        self, 
        message: str, 
        field: Optional[str] = None,
        value: Optional[Any] = None
    ):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details={"field": field, "value": value}
        )


class ModelError(MaterialGeneAPIException):
    """模型相关异常"""
    
    def __init__(
        self, 
        message: str, 
        model_type: Optional[str] = None,
        model_path: Optional[str] = None
    ):
        super().__init__(
            message=message,
            error_code="MODEL_ERROR",
            details={"model_type": model_type, "model_path": model_path}
        )


class ConfigurationError(MaterialGeneAPIException):
    """配置相关异常"""
    
    def __init__(
        self, 
        message: str, 
        config_section: Optional[str] = None,
        config_key: Optional[str] = None
    ):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details={"config_section": config_section, "config_key": config_key}
        )


class ResourceError(MaterialGeneAPIException):
    """资源相关异常（内存、磁盘等）"""
    
    def __init__(
        self, 
        message: str, 
        resource_type: Optional[str] = None,
        usage: Optional[str] = None
    ):
        super().__init__(
            message=message,
            error_code="RESOURCE_ERROR",
            details={"resource_type": resource_type, "usage": usage}
        )


class ConcurrencyError(MaterialGeneAPIException):
    """并发相关异常"""
    
    def __init__(
        self, 
        message: str, 
        concurrent_tasks: Optional[int] = None,
        max_allowed: Optional[int] = None
    ):
        super().__init__(
            message=message,
            error_code="CONCURRENCY_ERROR",
            details={"concurrent_tasks": concurrent_tasks, "max_allowed": max_allowed}
        )


class AuthenticationError(MaterialGeneAPIException):
    """认证相关异常"""
    
    def __init__(
        self, 
        message: str = "认证失败",
        user_id: Optional[str] = None
    ):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            details={"user_id": user_id}
        )


class AuthorizationError(MaterialGeneAPIException):
    """授权相关异常"""
    
    def __init__(
        self, 
        message: str = "权限不足",
        user_id: Optional[str] = None,
        required_permission: Optional[str] = None
    ):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            details={"user_id": user_id, "required_permission": required_permission}
        )


class NetworkError(MaterialGeneAPIException):
    """网络相关异常"""
    
    def __init__(
        self, 
        message: str,
        endpoint: Optional[str] = None,
        status_code: Optional[int] = None
    ):
        super().__init__(
            message=message,
            error_code="NETWORK_ERROR",
            details={"endpoint": endpoint, "status_code": status_code}
        )


class DataIntegrityError(MaterialGeneAPIException):
    """数据完整性异常"""
    
    def __init__(
        self, 
        message: str,
        data_source: Optional[str] = None,
        integrity_check: Optional[str] = None
    ):
        super().__init__(
            message=message,
            error_code="DATA_INTEGRITY_ERROR",
            details={"data_source": data_source, "integrity_check": integrity_check}
        )


class TimeoutError(MaterialGeneAPIException):
    """超时异常"""
    
    def __init__(
        self, 
        message: str,
        operation: Optional[str] = None,
        timeout_seconds: Optional[float] = None
    ):
        super().__init__(
            message=message,
            error_code="TIMEOUT_ERROR",
            details={"operation": operation, "timeout_seconds": timeout_seconds}
        )


# 异常错误码映射
ERROR_CODE_MAPPING = {
    "FILE_HANDLING_ERROR": "文件处理错误",
    "ALGORITHM_ERROR": "算法执行错误", 
    "TASK_MANAGER_ERROR": "任务管理错误",
    "VALIDATION_ERROR": "数据验证错误",
    "MODEL_ERROR": "模型错误",
    "CONFIGURATION_ERROR": "配置错误",
    "RESOURCE_ERROR": "资源错误",
    "CONCURRENCY_ERROR": "并发错误",
    "AUTHENTICATION_ERROR": "认证错误",
    "AUTHORIZATION_ERROR": "授权错误",
    "NETWORK_ERROR": "网络错误",
    "DATA_INTEGRITY_ERROR": "数据完整性错误",
    "TIMEOUT_ERROR": "超时错误"
}


def get_error_message(error_code: str, default: str = "未知错误") -> str:
    """根据错误码获取错误消息"""
    return ERROR_CODE_MAPPING.get(error_code, default)