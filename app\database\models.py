"""
数据库模型定义
使用SQLAlchemy定义数据表结构，替代文件存储系统
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Boolean, LargeBinary, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any
import json

Base = declarative_base()

class FileRecord(Base):
    """文件记录表"""
    __tablename__ = "file_records"

    id = Column(String(36), primary_key=True)  # UUID
    original_name = Column(String(255), nullable=False)
    file_type = Column(String(10), nullable=False)  # cif, csv
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100))
    file_path = Column(String(500), nullable=False)
    upload_time = Column(DateTime, default=func.now())
    metadata = Column(JSON)

    def to_dict(self) -> Dict[str, Any]:
        return {
            'file_id': self.id,
            'original_name': self.original_name,
            'file_type': self.file_type,
            'file_size': self.file_size,
            'mime_type': self.mime_type,
            'file_path': self.file_path,
            'upload_time': self.upload_time.isoformat() if self.upload_time else None,
            'metadata': self.metadata or {}
        }

class TaskRecord(Base):
    """任务记录表"""
    __tablename__ = "task_records"

    id = Column(String(36), primary_key=True)  # UUID
    task_type = Column(String(50), nullable=False)
    status = Column(String(20), nullable=False, default='pending')
    progress = Column(Float, default=0.0)
    start_time = Column(DateTime, default=func.now())
    end_time = Column(DateTime)
    error_message = Column(Text)
    metadata = Column(JSON)

    def to_dict(self) -> Dict[str, Any]:
        return {
            'task_id': self.id,
            'task_type': self.task_type,
            'status': self.status,
            'progress': self.progress,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'error_message': self.error_message,
            'metadata': self.metadata or {}
        }

class ResultRecord(Base):
    """结果记录表"""
    __tablename__ = "result_records"

    id = Column(String(36), primary_key=True)  # UUID
    task_id = Column(String(36), nullable=False)
    result_type = Column(String(20), nullable=False)  # json, csv, pkl
    result_data = Column(LargeBinary)  # 存储序列化后的结果数据
    file_path = Column(String(500))  # 大文件存储路径
    created_time = Column(DateTime, default=func.now())
    metadata = Column(JSON)

    def to_dict(self) -> Dict[str, Any]:
        return {
            'result_id': self.id,
            'task_id': self.task_id,
            'result_type': self.result_type,
            'file_path': self.file_path,
            'created_time': self.created_time.isoformat() if self.created_time else None,
            'metadata': self.metadata or {}
        }

class CacheRecord(Base):
    """缓存记录表"""
    __tablename__ = "cache_records"

    id = Column(String(64), primary_key=True)  # MD5 hash key
    cache_key = Column(String(255), nullable=False, index=True)
    cache_data = Column(LargeBinary)
    created_time = Column(DateTime, default=func.now())
    expire_time = Column(DateTime)
    access_count = Column(Integer, default=0)
    last_access = Column(DateTime, default=func.now())

    def is_expired(self) -> bool:
        if self.expire_time is None:
            return False
        return datetime.now() > self.expire_time

    def to_dict(self) -> Dict[str, Any]:
        return {
            'cache_id': self.id,
            'cache_key': self.cache_key,
            'created_time': self.created_time.isoformat() if self.created_time else None,
            'expire_time': self.expire_time.isoformat() if self.expire_time else None,
            'access_count': self.access_count,
            'last_access': self.last_access.isoformat() if self.last_access else None
        }

class SystemMetrics(Base):
    """系统指标表"""
    __tablename__ = "system_metrics"

    id = Column(Integer, primary_key=True, autoincrement=True)
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(20))
    recorded_time = Column(DateTime, default=func.now())
    metadata = Column(JSON)

    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'metric_name': self.metric_name,
            'metric_value': self.metric_value,
            'metric_unit': self.metric_unit,
            'recorded_time': self.recorded_time.isoformat() if self.recorded_time else None,
            'metadata': self.metadata or {}
        }