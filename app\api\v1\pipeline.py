"""
完整流程API路由
提供一键式材料基因算法完整流程执行
"""

from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any, Optional
import logging
import asyncio
import time

from app.models.request_models import (
    FullPipelineRequest, AlgorithmParametersRequest
)
from app.models.response_models import (
    FullPipelineResponse, TaskResponse, ResponseStatus, TaskStatus
)
from app.services.task_manager import task_manager
from app.core.gene_algorithms import material_gene_algorithms
from app.services.file_handler import file_handler

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/pipeline", tags=["完整流程"])

@router.post("/start", response_model=TaskResponse)
async def start_full_pipeline(request: FullPipelineRequest):
    """
    启动完整的材料基因算法流程
    
    包含以下步骤：
    1. 基因挖掘 (Gene Mining)
    2. 基因识别 (Gene Identification) 
    3. 基因融合 (Gene Fusion)
    4. 基因编辑 (Gene Editing)
    5. 基因设计 (Gene Design)
    6. 可视化生成 (Visualization) - 可选
    """
    try:
        logger.info(f"启动完整流程，处理 {len(request.cif_files)} 个CIF文件")
        
        # 验证输入文件
        for file_id in request.cif_files:
            file_info = await file_handler.get_file_info(file_id)
            if not file_info or file_info.get("file_type") != "cif":
                raise HTTPException(
                    status_code=404,
                    detail=f"CIF文件不存在或类型错误: {file_id}"
                )
        
        csv_file_info = await file_handler.get_file_info(request.csv_file)
        if not csv_file_info or csv_file_info.get("file_type") != "csv":
            raise HTTPException(
                status_code=404,
                detail=f"CSV文件不存在或类型错误: {request.csv_file}"
            )
        
        # 创建完整流程任务
        task_id = await task_manager.create_task(
            task_type="full_pipeline",
            task_func=execute_full_pipeline,
            cif_file_ids=request.cif_files,
            csv_file_id=request.csv_file,
            parameters=request.parameters.dict(),
            run_visualization=request.run_visualization,
            metadata={
                "pipeline_type": "full",
                "cif_count": len(request.cif_files),
                "csv_file": request.csv_file,
                "parameters": request.parameters.dict(),
                "run_visualization": request.run_visualization
            }
        )
        
        return TaskResponse(
            status=ResponseStatus.SUCCESS,
            message="完整流程任务已启动",
            task_id=task_id,
            task_status=TaskStatus.PENDING,
            progress=0.0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动完整流程失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"启动完整流程失败: {str(e)}"
        )

async def execute_full_pipeline(
    cif_file_ids: List[str],
    csv_file_id: str,
    parameters: Dict[str, Any],
    run_visualization: bool = True,
    progress_callback=None
):
    """执行完整流程的工作函数"""
    try:
        logger.info("开始执行完整材料基因算法流程")
        
        stage_results = {}
        sub_task_ids = []
        current_stage = "初始化"
        
        # 阶段1: 基因挖掘 (0-15%)
        if progress_callback:
            progress_callback(0.0)
        
        current_stage = "基因挖掘"
        logger.info("执行阶段1: 基因挖掘")
        
        mining_result = await material_gene_algorithms.gene_mining(
            cif_file_ids,
            lambda p: progress_callback(p * 0.15) if progress_callback else None
        )
        
        stage_results["mining"] = mining_result
        
        # 保存挖掘结果
        mining_id = f"pipeline_mining_{int(time.time())}"
        await file_handler.save_result(mining_id, mining_result, "json")
        
        # 阶段2: 基因识别 (15-35%)
        current_stage = "基因识别"
        logger.info("执行阶段2: 基因识别")
        
        identification_result = await material_gene_algorithms.gene_identification(
            cif_file_ids,
            lambda p: progress_callback(15 + p * 0.2) if progress_callback else None
        )
        
        stage_results["identification"] = identification_result
        
        # 保存识别结果
        identification_id = f"pipeline_identification_{int(time.time())}"
        await file_handler.save_result(identification_id, identification_result, "json")
        
        # 阶段3: 基因融合 (35-55%)
        current_stage = "基因融合"
        logger.info("执行阶段3: 基因融合")
        
        fusion_result = await material_gene_algorithms.gene_fusion(
            cif_file_ids,
            csv_file_id,
            lambda p: progress_callback(35 + p * 0.2) if progress_callback else None
        )
        
        stage_results["fusion"] = fusion_result
        
        # 保存融合特征
        fusion_id = f"pipeline_fusion_{int(time.time())}"
        await file_handler.save_result(fusion_id, fusion_result["fused_features"], "csv")
        
        # 阶段4: 基因编辑 (55-75%)
        current_stage = "基因编辑"
        logger.info("执行阶段4: 基因编辑")
        
        editing_result = await material_gene_algorithms.gene_editing(
            fusion_id,
            parameters.get("target_value", 12.04),
            parameters.get("label_type", "boundary_right"),
            parameters.get("train_mode", "true") == "true",
            progress_callback=lambda p: progress_callback(55 + p * 0.2) if progress_callback else None
        )
        
        stage_results["editing"] = editing_result
        
        # 阶段5: 基因设计 (75-90%)
        current_stage = "基因设计"
        logger.info("执行阶段5: 基因设计")
        
        design_result = await material_gene_algorithms.gene_design(
            editing_result["model_id"],
            fusion_id,
            parameters.get("n_calls", 200),
            lambda p: progress_callback(75 + p * 0.15) if progress_callback else None
        )
        
        stage_results["design"] = design_result
        
        # 阶段6: 可视化生成 (90-100%) - 可选
        visualization_results = {}
        if run_visualization:
            current_stage = "可视化生成"
            logger.info("执行阶段6: 可视化生成")
            
            try:
                # 生成多种可视化
                viz_types = ["tsne", "optimization", "feature_importance"]
                
                for i, viz_type in enumerate(viz_types):
                    try:
                        if viz_type == "tsne":
                            data_id = fusion_id
                        elif viz_type == "optimization":
                            # 保存优化历史
                            opt_history_id = f"optimization_history_{int(time.time())}"
                            await file_handler.save_result(
                                opt_history_id, 
                                design_result.get("optimization_history", {}), 
                                "json"
                            )
                            data_id = opt_history_id
                        else:  # feature_importance
                            data_id = fusion_id
                        
                        viz_result = await material_gene_algorithms.generate_visualization(
                            data_id,
                            viz_type,
                            f"pipeline_{viz_type}_{int(time.time())}"
                        )
                        
                        visualization_results[viz_type] = viz_result
                        
                        if progress_callback:
                            viz_progress = 90 + ((i + 1) / len(viz_types)) * 10
                            progress_callback(viz_progress)
                    
                    except Exception as e:
                        logger.warning(f"可视化 {viz_type} 生成失败: {e}")
                        visualization_results[viz_type] = {"error": str(e)}
            
            except Exception as e:
                logger.warning(f"可视化阶段失败: {e}")
                visualization_results = {"error": str(e)}
        
        stage_results["visualization"] = visualization_results
        
        # 生成流程摘要
        pipeline_summary = {
            "execution_time": time.time(),
            "processed_samples": mining_result.get("processed_samples", 0),
            "final_accuracy": editing_result.get("classification_accuracy", 0.0),
            "optimal_value": design_result.get("optimal_value", 0.0),
            "improvement_percentage": design_result.get("improvement_percentage", 0.0),
            "stages_completed": len([s for s in stage_results.values() if s and not s.get("error")]),
            "total_stages": 6 if run_visualization else 5
        }
        
        stage_results["summary"] = pipeline_summary
        
        if progress_callback:
            progress_callback(100.0)
        
        current_stage = "完成"
        logger.info("完整流程执行完成")
        
        return {
            "stage_results": stage_results,
            "current_stage": current_stage,
            "overall_progress": 100.0,
            "sub_tasks": sub_task_ids,
            "pipeline_summary": pipeline_summary
        }
        
    except Exception as e:
        logger.error(f"完整流程执行失败: {e}")
        raise

@router.get("/status/{task_id}")
async def get_pipeline_status(task_id: str):
    """获取完整流程状态"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "full_pipeline":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        # 计算详细进度信息
        progress_details = {}
        if task_info.result and "stage_results" in task_info.result:
            stage_results = task_info.result["stage_results"]
            
            stages = ["mining", "identification", "fusion", "editing", "design"]
            if task_info.metadata and task_info.metadata.get("run_visualization"):
                stages.append("visualization")
            
            completed_stages = 0
            for stage in stages:
                if stage in stage_results and stage_results[stage]:
                    completed_stages += 1
            
            progress_details = {
                "completed_stages": completed_stages,
                "total_stages": len(stages),
                "stage_progress": (completed_stages / len(stages)) * 100,
                "current_stage": task_info.result.get("current_stage", "未知")
            }
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "获取流程状态成功",
            "task_id": task_id,
            "status": task_info.status.value,
            "progress": task_info.progress,
            "start_time": task_info.start_time,
            "end_time": task_info.end_time,
            "result": task_info.result if task_info.status == TaskStatus.COMPLETED else None,
            "error_message": task_info.error_message,
            "metadata": task_info.metadata,
            "progress_details": progress_details
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取流程状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/result/{task_id}", response_model=FullPipelineResponse)
async def get_pipeline_result(task_id: str):
    """获取完整流程结果"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "full_pipeline":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        if task_info.status != TaskStatus.COMPLETED:
            raise HTTPException(
                status_code=400,
                detail=f"任务未完成，当前状态: {task_info.status.value}"
            )
        
        result = task_info.result
        if not result:
            raise HTTPException(status_code=404, detail="任务结果不存在")
        
        return FullPipelineResponse(
            status=ResponseStatus.SUCCESS,
            message="完整流程执行完成",
            task_id=task_id,
            sub_tasks=result.get("sub_tasks", []),
            overall_progress=result.get("overall_progress", 100.0),
            current_stage=result.get("current_stage", "完成"),
            stage_results=result.get("stage_results", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取流程结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取结果失败: {str(e)}")

@router.post("/restart_from_stage/{task_id}")
async def restart_pipeline_from_stage(
    task_id: str,
    stage_name: str,
    new_parameters: Optional[Dict[str, Any]] = None
):
    """
    从指定阶段重新启动流程
    
    允许在流程中途失败后从特定阶段重新开始
    """
    try:
        # 获取原始任务信息
        original_task = task_manager.get_task_status(task_id)
        if not original_task or original_task.task_type != "full_pipeline":
            raise HTTPException(
                status_code=404,
                detail="原始流程任务不存在"
            )
        
        # 验证阶段名称
        valid_stages = ["mining", "identification", "fusion", "editing", "design", "visualization"]
        if stage_name not in valid_stages:
            raise HTTPException(
                status_code=400,
                detail=f"无效的阶段名称。有效阶段: {valid_stages}"
            )
        
        # 获取原始参数
        original_metadata = original_task.metadata or {}
        original_params = original_metadata.get("parameters", {})
        
        # 合并新参数
        if new_parameters:
            original_params.update(new_parameters)
        
        # 创建重启任务
        restart_task_id = await task_manager.create_task(
            task_type="pipeline_restart",
            task_func=restart_pipeline_from_stage_worker,
            original_task_id=task_id,
            restart_stage=stage_name,
            parameters=original_params,
            metadata={
                "pipeline_type": "restart",
                "original_task": task_id,
                "restart_stage": stage_name,
                "parameters": original_params
            }
        )
        
        return TaskResponse(
            status=ResponseStatus.SUCCESS,
            message=f"流程重启任务已启动，从阶段 '{stage_name}' 开始",
            task_id=restart_task_id,
            task_status=TaskStatus.PENDING,
            progress=0.0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重启流程失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"重启流程失败: {str(e)}"
        )

async def restart_pipeline_from_stage_worker(
    original_task_id: str,
    restart_stage: str,
    parameters: Dict[str, Any],
    progress_callback=None
):
    """从指定阶段重启流程的工作函数"""
    try:
        # 获取原始任务结果
        original_task = task_manager.get_task_status(original_task_id)
        original_result = original_task.result or {}
        stage_results = original_result.get("stage_results", {})
        
        # 确定需要执行的阶段
        all_stages = ["mining", "identification", "fusion", "editing", "design", "visualization"]
        restart_index = all_stages.index(restart_stage)
        stages_to_execute = all_stages[restart_index:]
        
        # 获取原始输入
        original_metadata = original_task.metadata or {}
        cif_file_ids = original_metadata.get("cif_files", [])
        csv_file_id = original_metadata.get("csv_file")
        
        # 执行剩余阶段
        current_progress = 0
        stage_weight = 100.0 / len(stages_to_execute)
        
        for i, stage in enumerate(stages_to_execute):
            if progress_callback:
                progress_callback(current_progress)
            
            stage_start_progress = current_progress
            stage_end_progress = current_progress + stage_weight
            
            def stage_progress_callback(p):
                if progress_callback:
                    total_p = stage_start_progress + (p / 100.0) * stage_weight
                    progress_callback(total_p)
            
            if stage == "mining":
                stage_result = await material_gene_algorithms.gene_mining(
                    cif_file_ids, stage_progress_callback
                )
            elif stage == "identification":
                stage_result = await material_gene_algorithms.gene_identification(
                    cif_file_ids, stage_progress_callback
                )
            elif stage == "fusion":
                stage_result = await material_gene_algorithms.gene_fusion(
                    cif_file_ids, csv_file_id, stage_progress_callback
                )
            elif stage == "editing":
                # 使用融合结果
                fusion_result = stage_results.get("fusion") or stage_result
                fusion_id = f"restart_fusion_{int(time.time())}"
                await file_handler.save_result(fusion_id, fusion_result["fused_features"], "csv")
                
                stage_result = await material_gene_algorithms.gene_editing(
                    fusion_id,
                    parameters.get("target_value", 12.04),
                    parameters.get("label_type", "boundary_right"),
                    parameters.get("train_mode", "true") == "true",
                    progress_callback=stage_progress_callback
                )
            elif stage == "design":
                # 使用编辑结果
                editing_result = stage_results.get("editing") or stage_result
                fusion_id = f"restart_fusion_{int(time.time())}"
                
                stage_result = await material_gene_algorithms.gene_design(
                    editing_result["model_id"],
                    fusion_id,
                    parameters.get("n_calls", 200),
                    stage_progress_callback
                )
            elif stage == "visualization":
                # 生成可视化
                stage_result = {}
                # 实现可视化逻辑
            
            stage_results[stage] = stage_result
            current_progress = stage_end_progress
        
        if progress_callback:
            progress_callback(100.0)
        
        return {
            "stage_results": stage_results,
            "restart_stage": restart_stage,
            "completed_stages": stages_to_execute,
            "original_task_id": original_task_id
        }
        
    except Exception as e:
        logger.error(f"重启流程执行失败: {e}")
        raise

@router.post("/batch_pipeline")
async def start_batch_pipeline(
    pipeline_configs: List[Dict[str, Any]],
    execute_parallel: bool = False
):
    """
    批量执行完整流程
    
    支持并行或顺序执行多个流程配置
    """
    try:
        logger.info(f"启动批量流程，配置数量: {len(pipeline_configs)}")
        
        if len(pipeline_configs) > 10:
            raise HTTPException(
                status_code=400,
                detail="批量流程数量不能超过10个"
            )
        
        # 验证所有配置
        for i, config in enumerate(pipeline_configs):
            if "cif_files" not in config or "csv_file" not in config:
                raise HTTPException(
                    status_code=400,
                    detail=f"配置 {i+1} 缺少必要的文件参数"
                )
        
        # 创建批量任务
        task_id = await task_manager.create_task(
            task_type="batch_pipeline",
            task_func=execute_batch_pipeline,
            pipeline_configs=pipeline_configs,
            execute_parallel=execute_parallel,
            metadata={
                "pipeline_type": "batch",
                "config_count": len(pipeline_configs),
                "execute_parallel": execute_parallel
            }
        )
        
        return TaskResponse(
            status=ResponseStatus.SUCCESS,
            message=f"批量流程任务已启动 ({'并行' if execute_parallel else '顺序'}执行)",
            task_id=task_id,
            task_status=TaskStatus.PENDING,
            progress=0.0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动批量流程失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"启动批量流程失败: {str(e)}"
        )

async def execute_batch_pipeline(
    pipeline_configs: List[Dict[str, Any]],
    execute_parallel: bool,
    progress_callback=None
):
    """执行批量流程的工作函数"""
    try:
        batch_results = []
        
        if execute_parallel:
            # 并行执行
            tasks = []
            for i, config in enumerate(pipeline_configs):
                task = execute_full_pipeline(
                    config["cif_files"],
                    config["csv_file"],
                    config.get("parameters", {}),
                    config.get("run_visualization", True),
                    lambda p, idx=i: progress_callback((idx + p/100) / len(pipeline_configs) * 100) if progress_callback else None
                )
                tasks.append(task)
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    batch_results.append({
                        "config_index": i,
                        "status": "failed",
                        "error": str(result)
                    })
                else:
                    batch_results.append({
                        "config_index": i,
                        "status": "completed",
                        "result": result
                    })
        else:
            # 顺序执行
            for i, config in enumerate(pipeline_configs):
                try:
                    def config_progress_callback(p):
                        if progress_callback:
                            total_p = (i + p/100) / len(pipeline_configs) * 100
                            progress_callback(total_p)
                    
                    result = await execute_full_pipeline(
                        config["cif_files"],
                        config["csv_file"],
                        config.get("parameters", {}),
                        config.get("run_visualization", True),
                        config_progress_callback
                    )
                    
                    batch_results.append({
                        "config_index": i,
                        "status": "completed",
                        "result": result
                    })
                    
                except Exception as e:
                    logger.error(f"批量流程配置 {i} 执行失败: {e}")
                    batch_results.append({
                        "config_index": i,
                        "status": "failed",
                        "error": str(e)
                    })
        
        if progress_callback:
            progress_callback(100.0)
        
        # 统计结果
        completed_count = len([r for r in batch_results if r["status"] == "completed"])
        failed_count = len([r for r in batch_results if r["status"] == "failed"])
        
        return {
            "batch_results": batch_results,
            "summary": {
                "total_configs": len(pipeline_configs),
                "completed": completed_count,
                "failed": failed_count,
                "success_rate": completed_count / len(pipeline_configs) * 100
            },
            "execution_mode": "parallel" if execute_parallel else "sequential"
        }
        
    except Exception as e:
        logger.error(f"批量流程执行失败: {e}")
        raise

@router.get("/templates")
async def get_pipeline_templates():
    """获取流程模板"""
    try:
        templates = {
            "semiconductor_defect_analysis": {
                "name": "半导体缺陷分析",
                "description": "针对半导体材料的缺陷形成能分析和优化",
                "parameters": {
                    "target_value": 12.04,
                    "label_type": "boundary_right",
                    "n_calls": 200,
                    "train_mode": "true"
                },
                "run_visualization": True,
                "expected_duration": "30-60分钟"
            },
            "material_property_prediction": {
                "name": "材料性质预测",
                "description": "通用材料性质预测和筛选流程",
                "parameters": {
                    "target_value": 10.0,
                    "label_type": "boundary_left",
                    "n_calls": 150,
                    "train_mode": "true"
                },
                "run_visualization": True,
                "expected_duration": "20-40分钟"
            },
            "quick_screening": {
                "name": "快速筛选",
                "description": "快速材料筛选，适用于大批量数据",
                "parameters": {
                    "target_value": 8.0,
                    "label_type": "interval",
                    "n_calls": 100,
                    "train_mode": "false"
                },
                "run_visualization": False,
                "expected_duration": "10-20分钟"
            }
        }
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "获取流程模板成功",
            "templates": templates
        }
        
    except Exception as e:
        logger.error(f"获取流程模板失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取流程模板失败: {str(e)}"
        )

@router.delete("/cancel/{task_id}")
async def cancel_pipeline_task(task_id: str):
    """取消完整流程任务"""
    try:
        success = await task_manager.cancel_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=400,
                detail="任务取消失败，可能任务不存在或已完成"
            )
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "完整流程任务已取消",
            "task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")