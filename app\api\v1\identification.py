"""
基因识别API路由
提供CGCL和EGCL深度学习模型的特征识别功能
"""

from fastapi import APIRouter, HTTPException
from typing import List, Optional
import logging
from fastapi.responses import StreamingResponse
from app.models.request_models import GeneIdentificationRequest
from app.models.response_models import (
    GeneIdentificationResponse, TaskResponse, ResponseStatus, TaskStatus
)
from app.services.task_manager import task_manager
from app.core.gene_algorithms import material_gene_algorithms
from app.services.file_handler import file_handler
from app.core.units_models import show_tsne_result
import os
logger = logging.getLogger(__name__)
router = APIRouter(prefix="/identification", tags=["基因识别"])

@router.post("/start", response_model=TaskResponse)
async def start_gene_identification(request: GeneIdentificationRequest):
    """
    启动基因识别任务
    
    使用CGCL和EGCL深度学习模型从晶体结构中识别高层次特征
    这是材料基因算法的第二步，用于提取更抽象的材料特征表示
    """
    try:
        logger.info(f"启动基因识别任务，处理 {len(request.cif_files)} 个CIF文件")
        
        # 验证文件存在性和类型
        for file_id in request.cif_files:
            file_info = await file_handler.get_file_info(file_id)
            if not file_info:
                raise HTTPException(
                    status_code=404, 
                    detail=f"CIF文件不存在: {file_id}"
                )
            if file_info.get("file_type") != "cif":
                raise HTTPException(
                    status_code=400,
                    detail=f"文件类型错误，期望CIF文件: {file_id}"
                )
        
        # 检查至少启用一个模型
        if not request.use_cgcl and not request.use_egcl:
            raise HTTPException(
                status_code=400,
                detail="至少需要启用CGCL或EGCL模型中的一个"
            )
        
        # 创建异步任务
        task_id = await task_manager.create_task(
            task_type="gene_identification",
            task_func=material_gene_algorithms.gene_identification,
            cif_file_ids=request.cif_files,
            metadata={
                "cif_count": len(request.cif_files),
                "use_cgcl": request.use_cgcl,
                "use_egcl": request.use_egcl
            }
        )
        logger.info(f"基因识别任务已在identification创建，任务ID: {task_id}")
        return TaskResponse(
            status=ResponseStatus.SUCCESS,
            message="基因识别任务已启动",
            task_id=task_id,
            task_status=TaskStatus.PENDING,
            progress=0.0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动基因识别任务失败: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"启动基因识别任务失败: {str(e)}"
        )

@router.get("/status/{task_id}")
async def get_identification_status(task_id: str):
    """获取基因识别任务状态"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "gene_identification":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "获取任务状态成功",
            "task_id": task_id,
            "status": task_info.status.value,
            "progress": task_info.progress,
            "start_time": task_info.start_time,
            "end_time": task_info.end_time,
            #"result": task_info.result if task_info.status == TaskStatus.COMPLETED else None,
            "error_message": task_info.error_message,
            "metadata": task_info.metadata
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取识别状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/result/{task_id}", response_model=GeneIdentificationResponse)
async def get_identification_result(task_id: str):
    """获取基因识别结果"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "gene_identification":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        if task_info.status != TaskStatus.COMPLETED:
            raise HTTPException(
                status_code=400, 
                detail=f"任务未完成，当前状态: {task_info.status.value}"
            )
        
        result = task_info.result
        if not result:
            raise HTTPException(status_code=404, detail="任务结果不存在")
        
        # 保存特征数据到文件系统
        # cgcl_features_id = None
        # egcl_features_id = None

        cgcl_features_id = f"cgcl_{task_id}"
        #print(f"CGCL特征ID: {cgcl_features_id}")
        cgcl_download_url =await file_handler.save_result(cgcl_features_id, result["cgcl_features"], "npy")
        save_out_path = os.path.join(await file_handler.get_save_result(), task_id)
        os.makedirs(save_out_path,exist_ok=True)
        cgcl_out_png_path= f"{save_out_path}/cgcl_tsne_result.png" 
        #print(f"CGCL特征PNG路径: {cgcl_out_png_path}")
        #print(type(result["cgcl_features"]),result["cgcl_features"])
        show_tsne_result(result["cgcl_features"], cgcl_out_png_path)
    

        egcl_features_id = f"egcl_{task_id}"
        egcl_download_url=await file_handler.save_result(egcl_features_id, result["egcl_features"], "npy")
        os.makedirs(save_out_path,exist_ok=True)
        egcl_out_png_path= f"{save_out_path}/egcl_tsne_result.png"
        #print(f"EGCL特征PNG路径: {egcl_out_png_path}")
        show_tsne_result(result["egcl_features"], egcl_out_png_path)

        return GeneIdentificationResponse(
            status=ResponseStatus.SUCCESS,
            message="基因识别完成",
            task_id=task_id,
            cgcl_features_id=cgcl_features_id,
            egcl_features_id=egcl_features_id,
            cgcl_download_url=cgcl_download_url,
            egcl_download_url=egcl_download_url,
            cgcl_png_download_url=cgcl_out_png_path,
            egcl_png_download_url=egcl_out_png_path,
            feature_dimensions=result.get("feature_dimensions", {}),
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取识别结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取结果失败: {str(e)}")
# @router.post("/continue_from_mining/{mining_task_id}", response_model=TaskResponse)
# async def continue_from_mining(
#     mining_task_id: str,
#     use_cgcl: bool = True,
#     use_egcl: bool = True
# ):
#     """
#     从基因挖掘结果继续进行基因识别
    
#     这是一个便捷方法，可以直接使用基因挖掘的结果进行识别
#     """
#     try:
#         # 获取挖掘任务结果
#         mining_task = task_manager.get_task_status(mining_task_id)
#         if not mining_task:
#             raise HTTPException(status_code=404, detail="基因挖掘任务不存在")
        
#         if mining_task.task_type != "gene_mining":
#             raise HTTPException(status_code=400, detail="任务类型不是基因挖掘")
        
#         if mining_task.status != TaskStatus.COMPLETED:
#             raise HTTPException(
#                 status_code=400, 
#                 detail="基因挖掘任务未完成"
#             )
        
#         # 获取原始CIF文件列表（从挖掘任务的元数据中）
#         # 这里假设我们在挖掘任务中保存了原始文件信息
#         metadata = mining_task.metadata or {}
        
#         # 检查至少启用一个模型
#         if not use_cgcl and not use_egcl:
#             raise HTTPException(
#                 status_code=400,
#                 detail="至少需要启用CGCL或EGCL模型中的一个"
#             )
        
#         # 创建识别任务，重用之前的文件
#         # 注意：这里需要从挖掘结果中获取CIF文件列表
#         # 在实际实现中，您可能需要在任务元数据中保存原始文件信息
        
#         # 模拟从元数据获取文件列表
#         cif_file_ids = metadata.get("original_cif_files", [])
#         if not cif_file_ids:
#             raise HTTPException(
#                 status_code=400,
#                 detail="无法从挖掘任务中获取原始CIF文件信息"
#             )
        
#         task_id = await task_manager.create_task(
#             task_type="gene_identification",
#             task_func=material_gene_algorithms.gene_identification,
#             cif_file_ids=cif_file_ids,
#             metadata={
#                 "cif_count": len(cif_file_ids),
#                 "use_cgcl": use_cgcl,
#                 "use_egcl": use_egcl,
#                 "continued_from_mining": mining_task_id
#             }
#         )
        
#         return TaskResponse(
#             status=ResponseStatus.SUCCESS,
#             message=f"基因识别任务已启动（继承自挖掘任务 {mining_task_id}）",
#             task_id=task_id,
#             task_status=TaskStatus.PENDING,
#             progress=0.0
#         )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"从挖掘结果继续识别失败: {e}")
#         raise HTTPException(
#             status_code=500, 
#             detail=f"从挖掘结果继续识别失败: {str(e)}"
#         )

# @router.post("/compare_models/{task_id}")
# async def compare_model_performance(task_id: str):
#     """
#     比较CGCL和EGCL模型性能
    
#     分析两个模型在同一数据集上的表现差异
#     """
#     try:
#         task_info = task_manager.get_task_status(task_id)
        
#         if not task_info or task_info.task_type != "gene_identification":
#             raise HTTPException(status_code=404, detail="基因识别任务不存在")
        
#         if task_info.status != TaskStatus.COMPLETED:
#             raise HTTPException(status_code=400, detail="任务未完成")
        
#         result = task_info.result
#         if not result:
#             raise HTTPException(status_code=404, detail="任务结果不存在")
        
#         # 分析模型特征
#         analysis = {
#             "cgcl_analysis": {},
#             "egcl_analysis": {},
#             "comparison": {}
#         }
        
#         # CGCL特征分析
#         cgcl_features = result.get("cgcl_features")
#         if cgcl_features:
#             import numpy as np
#             cgcl_array = np.array(cgcl_features)
#             analysis["cgcl_analysis"] = {
#                 "feature_count": cgcl_array.shape[0] if cgcl_array.size > 0 else 0,
#                 "feature_dimension": cgcl_array.shape[1] if len(cgcl_array.shape) > 1 else 0,
#                 "mean_value": float(np.mean(cgcl_array)) if cgcl_array.size > 0 else 0,
#                 "std_value": float(np.std(cgcl_array)) if cgcl_array.size > 0 else 0,
#                 "min_value": float(np.min(cgcl_array)) if cgcl_array.size > 0 else 0,
#                 "max_value": float(np.max(cgcl_array)) if cgcl_array.size > 0 else 0
#             }
        
#         # EGCL特征分析
#         egcl_features = result.get("egcl_features")
#         if egcl_features:
#             egcl_array = np.array(egcl_features)
#             analysis["egcl_analysis"] = {
#                 "feature_count": egcl_array.shape[0] if egcl_array.size > 0 else 0,
#                 "feature_dimension": egcl_array.shape[1] if len(egcl_array.shape) > 1 else 0,
#                 "mean_value": float(np.mean(egcl_array)) if egcl_array.size > 0 else 0,
#                 "std_value": float(np.std(egcl_array)) if egcl_array.size > 0 else 0,
#                 "min_value": float(np.min(egcl_array)) if egcl_array.size > 0 else 0,
#                 "max_value": float(np.max(egcl_array)) if egcl_array.size > 0 else 0
#             }
        
#         # 特征比较
#         if cgcl_features and egcl_features:
#             analysis["comparison"] = {
#                 "both_models_available": True,
#                 "dimension_difference": analysis["cgcl_analysis"]["feature_dimension"] - analysis["egcl_analysis"]["feature_dimension"],
#                 "mean_difference": analysis["cgcl_analysis"]["mean_value"] - analysis["egcl_analysis"]["mean_value"],
#                 "recommendation": "建议在后续融合步骤中使用两个模型的特征进行互补"
#             }
#         else:
#             analysis["comparison"] = {
#                 "both_models_available": False,
#                 "available_models": [
#                     "CGCL" if cgcl_features else None,
#                     "EGCL" if egcl_features else None
#                 ],
#                 "recommendation": "建议重新运行任务以获取完整的特征集"
#             }
        
#         return {
#             "status": ResponseStatus.SUCCESS,
#             "message": "模型性能比较完成",
#             "task_id": task_id,
#             "analysis": analysis
#         }
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"模型性能比较失败: {e}")
#         raise HTTPException(status_code=500, detail=f"模型性能比较失败: {str(e)}")

# @router.post("/extract_features")
# async def extract_features_standalone(
#     cif_file_ids: List[str],
#     model_type: str = "both"  # "cgcl", "egcl", or "both"
# ):
#     """
#     独立的特征提取接口
    
#     允许单独使用CGCL或EGCL模型进行特征提取
#     """
#     try:
#         if model_type not in ["cgcl", "egcl", "both"]:
#             raise HTTPException(
#                 status_code=400,
#                 detail="模型类型必须是 'cgcl', 'egcl' 或 'both'"
#             )
        
#         # 验证文件
#         for file_id in cif_file_ids:
#             file_info = await file_handler.get_file_info(file_id)
#             if not file_info or file_info.get("file_type") != "cif":
#                 raise HTTPException(
#                     status_code=400,
#                     detail=f"无效的CIF文件: {file_id}"
#                 )
        
#         # 设置模型使用标志
#         use_cgcl = model_type in ["cgcl", "both"]
#         use_egcl = model_type in ["egcl", "both"]
        
#         # 创建任务
#         task_id = await task_manager.create_task(
#             task_type="gene_identification",
#             task_func=material_gene_algorithms.gene_identification,
#             cif_file_ids=cif_file_ids,
#             metadata={
#                 "cif_count": len(cif_file_ids),
#                 "use_cgcl": use_cgcl,
#                 "use_egcl": use_egcl,
#                 "extraction_type": "standalone",
#                 "model_type": model_type
#             }
#         )
        
#         return TaskResponse(
#             status=ResponseStatus.SUCCESS,
#             message=f"独立特征提取任务已启动 (模型: {model_type})",
#             task_id=task_id,
#             task_status=TaskStatus.PENDING,
#             progress=0.0
#         )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"独立特征提取失败: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"独立特征提取失败: {str(e)}"
#         )

@router.get("/models/status")
async def get_model_status():
    """获取深度学习模型状态"""
    try:
        # 这里可以检查模型文件是否存在，模型是否已加载等
        from app.config import settings
        import os
        
        model_status = {
            "cgcl": {
                "model_path": settings.cgcl_model_path,
                "exists": os.path.exists(settings.cgcl_model_path),
                "size_mb": os.path.getsize(settings.cgcl_model_path) / (1024*1024) if os.path.exists(settings.cgcl_model_path) else 0
            },
            "egcl": {
                "model_path": settings.egcl_model_path,
                "exists": os.path.exists(settings.egcl_model_path),
                "size_mb": os.path.getsize(settings.egcl_model_path) / (1024*1024) if os.path.exists(settings.egcl_model_path) else 0
            }
        }
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "获取模型状态成功",
            "models": model_status
        }
        
    except Exception as e:
        logger.error(f"获取模型状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型状态失败: {str(e)}")

@router.delete("/cancel/{task_id}")
async def cancel_identification_task(task_id: str):
    """取消基因识别任务"""
    try:
        success = await task_manager.cancel_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=400, 
                detail="任务取消失败，可能任务不存在或已完成"
            )
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "基因识别任务已取消",
            "task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")
import json  
import numpy as np
import io
@router.get("/download/{file_type}/{task_id}", 
           summary="下载特征数据文件",
           description="下载cgcl或EGCL特征数据文件")
async def download_features(file_type: str, task_id: str):
    """
    根据任务ID和特征类型下载原始特征数据
    
    - **file_type**: 特征类型 (cgcl或egcl)
    - **task_id**: 任务ID（从基因挖掘响应中获取）
    """
    try:
        # 参数校验
        if file_type not in ["cgcl", "egcl"]:
            raise HTTPException(status_code=400, detail="文件类型必须是 'cgcl' 或 'egcl'")
        file_id = f"{file_type}_{task_id}"
        np_array = await file_handler.load_result(file_id, "npy")
        # 生成NPY文件字节流
        buffer = io.BytesIO()
        np.save(buffer, np_array)
        buffer.seek(0)
        
        return StreamingResponse(
            buffer,
            media_type="application/octet-stream",
            headers={
                "Content-Disposition": f"attachment; filename=soap_features_{task_id}.npy"
            }
        )

    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="未找到对应的特征文件")
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail="文件下载服务异常")
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse


# 🔍 可视化图像下载接口
@router.get("/tsne/{task_id}/{model_type}.png")
async def get_tsne_image(task_id: str, model_type: str):
    """
    返回指定任务的 t-SNE 图像，提供给前端展示。
    model_type 应为 'cgcl' 或 'egcl'
    """
    if model_type not in ['cgcl', 'egcl']:
        raise HTTPException(status_code=400, detail="模型类型无效，只能是 'cgcl' 或 'egcl'")

    # 构建图像路径
    save_out_path =await file_handler.get_save_result()
    save_out_path = os.path.join(save_out_path, task_id)
    image_path = os.path.join(save_out_path, f"{model_type}_tsne_result.png")

    if not os.path.exists(image_path):
        raise HTTPException(status_code=404, detail="图像未找到，请先生成可视化结果")

    return FileResponse(image_path, media_type='image/png', filename=f"{model_type}_tsne_result.png")
