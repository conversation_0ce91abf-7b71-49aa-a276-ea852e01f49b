"""
日志配置模块
提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
import sys
import os
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import json

from app.config import settings


class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',     # 青色
        'INFO': '\033[32m',      # 绿色
        'WARNING': '\033[33m',   # 黄色
        'ERROR': '\033[31m',     # 红色
        'CRITICAL': '\033[35m',  # 紫色
        'RESET': '\033[0m'       # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class JSONFormatter(logging.Formatter):
    """JSON格式日志格式化器"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'task_id'):
            log_entry['task_id'] = record.task_id
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        return json.dumps(log_entry, ensure_ascii=False)


class RequestContextFilter(logging.Filter):
    """请求上下文过滤器"""
    
    def filter(self, record):
        # 这里可以添加请求ID、用户ID等上下文信息
        # 在实际使用中可以从contextvar或线程本地存储获取
        if not hasattr(record, 'request_id'):
            record.request_id = getattr(self, '_request_id', 'N/A')
        if not hasattr(record, 'user_id'):
            record.user_id = getattr(self, '_user_id', 'anonymous')
        return True


def setup_logging(
    log_level: str = None,
    log_file: str = None,
    enable_json: bool = False,
    enable_colors: bool = True,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
):
    """
    设置日志配置
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径
        enable_json: 是否启用JSON格式
        enable_colors: 是否启用颜色（仅控制台）
        max_bytes: 单个日志文件最大大小
        backup_count: 备份文件数量
    """
    
    # 使用配置中的默认值
    log_level = log_level or settings.log_level
    log_file = log_file or settings.log_file
    
    # 创建日志目录
    if log_file:
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
    
    # 根记录器配置
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    
    if enable_json:
        console_formatter = JSONFormatter()
    elif enable_colors and sys.stdout.isatty():
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    else:
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, log_level.upper()))
        
        if enable_json:
            file_formatter = JSONFormatter()
        else:
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
        
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    # 添加请求上下文过滤器
    context_filter = RequestContextFilter()
    for handler in root_logger.handlers:
        handler.addFilter(context_filter)
    
    # 设置第三方库的日志级别
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    logging.getLogger('uvicorn.access').setLevel(logging.WARNING)
    logging.getLogger('fastapi').setLevel(logging.INFO)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    
    # 材料基因相关模块使用DEBUG级别
    for module in ['app.core', 'app.services', 'app.api']:
        logging.getLogger(module).setLevel(logging.DEBUG)


def get_logger(name: str = None) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称，默认使用调用模块的名称
        
    Returns:
        logging.Logger: 日志记录器实例
    """
    if name is None:
        # 获取调用者的模块名
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')
    
    return logging.getLogger(name)


class TaskLogger:
    """任务专用日志记录器"""
    
    def __init__(self, task_id: str, task_type: str = None):
        self.task_id = task_id
        self.task_type = task_type
        self.logger = logging.getLogger(f"task.{task_id}")
        
        # 创建任务专用的文件处理器
        if settings.log_file:
            task_log_dir = Path(settings.log_file).parent / "tasks"
            task_log_dir.mkdir(exist_ok=True)
            
            task_log_file = task_log_dir / f"task_{task_id}.log"
            
            task_handler = logging.FileHandler(task_log_file, encoding='utf-8')
            task_handler.setLevel(logging.DEBUG)
            
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            task_handler.setFormatter(formatter)
            
            self.logger.addHandler(task_handler)
            self.logger.setLevel(logging.DEBUG)
    
    def debug(self, message: str, **kwargs):
        extra = {'task_id': self.task_id, 'task_type': self.task_type, **kwargs}
        self.logger.debug(message, extra=extra)
    
    def info(self, message: str, **kwargs):
        extra = {'task_id': self.task_id, 'task_type': self.task_type, **kwargs}
        self.logger.info(message, extra=extra)
    
    def warning(self, message: str, **kwargs):
        extra = {'task_id': self.task_id, 'task_type': self.task_type, **kwargs}
        self.logger.warning(message, extra=extra)
    
    def error(self, message: str, **kwargs):
        extra = {'task_id': self.task_id, 'task_type': self.task_type, **kwargs}
        self.logger.error(message, extra=extra)
    
    def critical(self, message: str, **kwargs):
        extra = {'task_id': self.task_id, 'task_type': self.task_type, **kwargs}
        self.logger.critical(message, extra=extra)
    
    def exception(self, message: str, **kwargs):
        extra = {'task_id': self.task_id, 'task_type': self.task_type, **kwargs}
        self.logger.exception(message, extra=extra)


class PerformanceLogger:
    """性能监控日志记录器"""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.logger = logging.getLogger(f"performance.{operation_name}")
        self.start_time = None
        self.metrics = {}
    
    def start(self):
        """开始计时"""
        import time
        self.start_time = time.time()
        self.logger.info(f"开始执行操作: {self.operation_name}")
    
    def end(self, success: bool = True, **metrics):
        """结束计时并记录性能指标"""
        if self.start_time is None:
            self.logger.warning("性能监控未正确启动")
            return
        
        import time
        end_time = time.time()
        duration = end_time - self.start_time
        
        self.metrics.update({
            'operation': self.operation_name,
            'duration_seconds': duration,
            'success': success,
            'timestamp': datetime.now().isoformat(),
            **metrics
        })
        
        log_level = logging.INFO if success else logging.ERROR
        self.logger.log(
            log_level,
            f"操作完成: {self.operation_name}, 耗时: {duration:.2f}秒, 成功: {success}",
            extra=self.metrics
        )
        
        return duration
    
    def __enter__(self):
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        success = exc_type is None
        if exc_type:
            self.metrics['error'] = str(exc_val)
        self.end(success=success)


def log_api_request(func):
    """API请求日志装饰器"""
    import functools
    import time
    
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        logger = get_logger(f"api.{func.__module__}.{func.__name__}")
        
        start_time = time.time()
        
        # 记录请求开始
        logger.info(f"API请求开始: {func.__name__}")
        
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            
            # 记录请求成功
            logger.info(
                f"API请求成功: {func.__name__}, 耗时: {duration:.2f}秒",
                extra={'duration': duration, 'success': True}
            )
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            
            # 记录请求失败
            logger.error(
                f"API请求失败: {func.__name__}, 耗时: {duration:.2f}秒, 错误: {str(e)}",
                extra={'duration': duration, 'success': False, 'error': str(e)},
                exc_info=True
            )
            
            raise
    
    return wrapper


def create_audit_logger() -> logging.Logger:
    """创建审计日志记录器"""
    audit_logger = logging.getLogger('audit')
    audit_logger.setLevel(logging.INFO)
    
    # 创建审计日志文件处理器
    if settings.log_file:
        audit_log_file = Path(settings.log_file).parent / "audit.log"
        audit_handler = logging.handlers.RotatingFileHandler(
            audit_log_file,
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        
        audit_formatter = JSONFormatter()
        audit_handler.setFormatter(audit_formatter)
        audit_logger.addHandler(audit_handler)
    
    return audit_logger


# 全局审计日志记录器
audit_logger = create_audit_logger()


def audit_log(action: str, resource: str, user_id: str = None, **details):
    """记录审计日志"""
    audit_entry = {
        'action': action,
        'resource': resource,
        'user_id': user_id or 'system',
        'timestamp': datetime.now().isoformat(),
        'details': details
    }
    
    audit_logger.info(
        f"审计: {action} - {resource}",
        extra=audit_entry
    )


# 系统启动时初始化日志
setup_logging()