"""
工具模块

提供通用的工具函数、异常类、日志配置等。

包含：
- 自定义异常类
- 日志配置和管理
- 通用工具函数
- 装饰器
- 验证器
"""

# 导入异常类
from app.utils.exceptions import (
    MaterialGeneAPIException,
    FileHandlingError,
    AlgorithmError,
    TaskManagerError,
    ValidationError,
    ModelError,
    ConfigurationError,
    ResourceError,
    ConcurrencyError,
    AuthenticationError,
    AuthorizationError,
    NetworkError,
    DataIntegrityError,
    TimeoutError,
    ERROR_CODE_MAPPING,
    get_error_message
)

# 导入日志工具
from app.utils.logger import (
    setup_logging,
    get_logger,
    TaskLogger,
    PerformanceLogger,
    log_api_request,
    audit_log,
    audit_logger,
    ColoredFormatter,
    JSONFormatter,
    RequestContextFilter
)

# 通用工具函数
import hashlib
import uuid
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
import json
import os

def generate_unique_id(prefix: str = "") -> str:
    """生成唯一ID"""
    timestamp = int(time.time() * 1000)
    unique_part = uuid.uuid4().hex[:8]
    return f"{prefix}{timestamp}_{unique_part}" if prefix else f"{timestamp}_{unique_part}"

def calculate_file_hash(file_path: str, algorithm: str = "sha256") -> str:
    """计算文件哈希值"""
    hash_func = getattr(hashlib, algorithm)()
    
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_func.update(chunk)
    
    return hash_func.hexdigest()

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.2f} PB"

def parse_duration(seconds: float) -> str:
    """将秒数转换为可读的时长格式"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"

def safe_json_dumps(obj: Any, **kwargs) -> str:
    """安全的JSON序列化"""
    def default(o):
        if isinstance(o, datetime):
            return o.isoformat()
        elif hasattr(o, '__dict__'):
            return o.__dict__
        else:
            return str(o)
    
    return json.dumps(obj, default=default, ensure_ascii=False, **kwargs)

def ensure_directory(path: str) -> str:
    """确保目录存在"""
    os.makedirs(path, exist_ok=True)
    return path

def get_file_extension(filename: str) -> str:
    """获取文件扩展名（小写）"""
    return os.path.splitext(filename)[1].lower()

def is_valid_uuid(value: str) -> bool:
    """验证是否为有效的UUID"""
    try:
        uuid.UUID(value)
        return True
    except ValueError:
        return False

# 装饰器
def retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """重试装饰器"""
    def decorator(func):
        import functools
        import asyncio
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise
                    wait_time = delay * (backoff ** attempt)
                    logger = get_logger(__name__)
                    logger.warning(
                        f"函数 {func.__name__} 执行失败 (尝试 {attempt + 1}/{max_attempts}), "
                        f"{wait_time:.1f}秒后重试: {str(e)}"
                    )
                    await asyncio.sleep(wait_time)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise
                    wait_time = delay * (backoff ** attempt)
                    logger = get_logger(__name__)
                    logger.warning(
                        f"函数 {func.__name__} 执行失败 (尝试 {attempt + 1}/{max_attempts}), "
                        f"{wait_time:.1f}秒后重试: {str(e)}"
                    )
                    time.sleep(wait_time)
        
        # 判断是否为异步函数
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def timeit(func):
    """计时装饰器"""
    import functools
    import asyncio
    
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            logger = get_logger(__name__)
            logger.debug(f"{func.__name__} 执行耗时: {duration:.3f}秒")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger = get_logger(__name__)
            logger.error(f"{func.__name__} 执行失败，耗时: {duration:.3f}秒")
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger = get_logger(__name__)
            logger.debug(f"{func.__name__} 执行耗时: {duration:.3f}秒")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger = get_logger(__name__)
            logger.error(f"{func.__name__} 执行失败，耗时: {duration:.3f}秒")
            raise
    
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper

# 验证器
class Validators:
    """通用验证器集合"""
    
    @staticmethod
    def validate_positive_number(value: Union[int, float], name: str = "value") -> None:
        """验证正数"""
        if value <= 0:
            raise ValidationError(f"{name} 必须为正数", field=name, value=value)
    
    @staticmethod
    def validate_range(
        value: Union[int, float], 
        min_val: Optional[Union[int, float]] = None,
        max_val: Optional[Union[int, float]] = None,
        name: str = "value"
    ) -> None:
        """验证数值范围"""
        if min_val is not None and value < min_val:
            raise ValidationError(
                f"{name} 不能小于 {min_val}", 
                field=name, 
                value=value
            )
        if max_val is not None and value > max_val:
            raise ValidationError(
                f"{name} 不能大于 {max_val}", 
                field=name, 
                value=value
            )
    
    @staticmethod
    def validate_list_not_empty(lst: List[Any], name: str = "list") -> None:
        """验证列表非空"""
        if not lst:
            raise ValidationError(f"{name} 不能为空", field=name, value=lst)
    
    @staticmethod
    def validate_file_exists(file_path: str) -> None:
        """验证文件存在"""
        if not os.path.exists(file_path):
            raise ValidationError(
                f"文件不存在: {file_path}", 
                field="file_path", 
                value=file_path
            )

# 导出所有公共接口
__all__ = [
    # 异常类
    "MaterialGeneAPIException",
    "FileHandlingError",
    "AlgorithmError",
    "TaskManagerError",
    "ValidationError",
    "ModelError",
    "ConfigurationError",
    "ResourceError",
    "ConcurrencyError",
    "AuthenticationError",
    "AuthorizationError",
    "NetworkError",
    "DataIntegrityError",
    "TimeoutError",
    "ERROR_CODE_MAPPING",
    "get_error_message",
    
    # 日志工具
    "setup_logging",
    "get_logger",
    "TaskLogger",
    "PerformanceLogger",
    "log_api_request",
    "audit_log",
    "audit_logger",
    
    # 工具函数
    "generate_unique_id",
    "calculate_file_hash",
    "format_file_size",
    "parse_duration",
    "safe_json_dumps",
    "ensure_directory",
    "get_file_extension",
    "is_valid_uuid",
    
    # 装饰器
    "retry",
    "timeit",
    
    # 验证器
    "Validators"
]

# 模块级别的初始化
def _init_utils():
    """初始化工具模块"""
    # 确保日志目录存在
    ensure_directory("logs")
    
    # 设置默认日志
    logger = get_logger(__name__)
    logger.info("工具模块初始化完成")

# 执行初始化
_init_utils()