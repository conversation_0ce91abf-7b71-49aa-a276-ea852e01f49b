"""
可视化API路由
提供t-SNE、决策边界、优化过程等各种可视化功能
"""

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import FileResponse
from typing import List, Optional, Dict, Any
import logging
import os
import time

from app.models.request_models import VisualizationRequest
from app.models.response_models import (
    VisualizationResponse, TaskResponse, ResponseStatus, TaskStatus
)
from app.services.task_manager import task_manager
from app.core.gene_algorithms import material_gene_algorithms
from app.services.file_handler import file_handler
from app.config import settings

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/visualization", tags=["可视化"])

@router.post("/generate", response_model=TaskResponse)
async def generate_visualization(request: VisualizationRequest):
    """
    生成可视化
    
    支持多种可视化类型：
    - tsne: t-SNE降维可视化
    - edit_2d: 2D决策边界可视化
    - edit_3d: 3D决策边界可视化  
    - optimization: 优化过程可视化
    - feature_importance: 特征重要性可视化
    - correlation_matrix: 特征相关性矩阵
    """
    try:
        logger.info(f"启动可视化任务，类型: {request.visualization_type}")
        
        # 验证数据存在
        data_exists = False
        for ext in ["csv", "json", "npy", "pkl"]:
            data = await file_handler.load_result(request.data_id, ext)
            if data is not None:
                data_exists = True
                break
        
        if not data_exists:
            raise HTTPException(
                status_code=404,
                detail=f"数据不存在: {request.data_id}"
            )
        
        # 验证可视化类型
        supported_types = [
            "tsne", "edit_2d", "edit_3d", "optimization", 
            "feature_importance", "correlation_matrix", "pca",
            "cluster_analysis", "distribution_plot"
        ]
        
        if request.visualization_type not in supported_types:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的可视化类型: {request.visualization_type}. 支持的类型: {supported_types}"
            )
        
        # 创建可视化任务
        task_id = await task_manager.create_task(
            task_type="visualization",
            task_func=generate_visualization_worker,
            data_id=request.data_id,
            visualization_type=request.visualization_type,
            save_filename=request.save_filename,
            metadata={
                "visualization_type": request.visualization_type,
                "data_id": request.data_id
            }
        )
        
        return TaskResponse(
            status=ResponseStatus.SUCCESS,
            message="可视化任务已启动",
            task_id=task_id,
            task_status=TaskStatus.PENDING,
            progress=0.0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动可视化任务失败: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"启动可视化任务失败: {str(e)}"
        )

async def generate_visualization_worker(
    data_id: str,
    visualization_type: str,
    save_filename: Optional[str] = None,
    progress_callback=None
):
    """可视化生成工作函数"""
    try:
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        import matplotlib.pyplot as plt
        import seaborn as sns
        import numpy as np
        import pandas as pd
        from sklearn.manifold import TSNE
        from sklearn.decomposition import PCA
        from sklearn.cluster import KMeans
        import plotly.graph_objects as go
        import plotly.express as px
        from pathlib import Path
        
        # 创建图片保存目录
        images_dir = Path("static/images")
        images_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        if save_filename is None:
            save_filename = f"{visualization_type}_{data_id}_{int(time.time())}"
        
        # 加载数据
        data = None
        for ext in ["csv", "json", "npy", "pkl"]:
            data = await file_handler.load_result(data_id, ext)
            if data is not None:
                break
        
        if data is None:
            raise ValueError(f"无法加载数据: {data_id}")
        
        # 转换数据格式
        if isinstance(data, dict):
            if visualization_type == "optimization":
                # 优化历史数据特殊处理
                df = pd.DataFrame()
                if 'func_vals' in data:
                    df['iteration'] = range(len(data['func_vals']))
                    df['objective_value'] = data['func_vals']
            else:
                df = pd.DataFrame(data)
        elif hasattr(data, 'to_csv'):  # pandas DataFrame
            df = data
        elif isinstance(data, np.ndarray):
            df = pd.DataFrame(data)
        else:
            raise ValueError(f"不支持的数据类型: {type(data)}")
        
        if progress_callback:
            progress_callback(20.0)
        
        image_urls = []
        interactive_data = {}
        
        # 根据可视化类型生成图表
        if visualization_type == "tsne":
            # t-SNE降维可视化
            if len(df.columns) > 2:
                # 选择数值列
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) < 2:
                    raise ValueError("数据中数值列不足，无法进行t-SNE分析")
                
                X = df[numeric_cols].values
                
                # 如果样本数太少，跳过
                if len(X) < 10:
                    raise ValueError("样本数量太少，无法进行t-SNE分析")
                
                # 执行t-SNE
                perplexity = min(30, len(X) - 1)
                tsne = TSNE(n_components=2, perplexity=perplexity, random_state=42)
                X_tsne = tsne.fit_transform(X)
                
                # 创建t-SNE图
                plt.figure(figsize=(10, 8))
                scatter = plt.scatter(X_tsne[:, 0], X_tsne[:, 1], 
                                    c=range(len(X_tsne)), cmap='viridis', alpha=0.6)
                plt.colorbar(scatter)
                plt.title('t-SNE Visualization of Material Features')
                plt.xlabel('t-SNE Component 1')
                plt.ylabel('t-SNE Component 2')
                
                tsne_path = images_dir / f"{save_filename}_tsne.png"
                plt.savefig(tsne_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                image_urls.append(f"/static/images/{tsne_path.name}")
                
                # 保存交互式数据
                interactive_data['tsne_coordinates'] = X_tsne.tolist()
            
        elif visualization_type == "edit_2d":
            # 2D决策边界可视化
            # 需要同时加载SVM模型
            model_id = data_id.replace("features", "model")
            svm_model = await file_handler.load_result(model_id, "pkl")
            
            if svm_model is not None and len(df.columns) >= 2:
                # 选择前两个特征进行2D可视化
                X = df.iloc[:, :2].values
                
                # 创建网格
                h = 0.02
                x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
                y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
                xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                                   np.arange(y_min, y_max, h))
                
                # 预测网格点
                grid_points = np.c_[xx.ravel(), yy.ravel()]
                # 如果模型需要更多特征，用零填充
                if hasattr(svm_model, 'n_features_in_') and svm_model.n_features_in_ > 2:
                    padding = np.zeros((len(grid_points), svm_model.n_features_in_ - 2))
                    grid_points = np.hstack([grid_points, padding])
                
                Z = svm_model.predict(grid_points)
                Z = Z.reshape(xx.shape)
                
                # 绘制决策边界
                plt.figure(figsize=(10, 8))
                plt.contourf(xx, yy, Z, alpha=0.8, cmap=plt.cm.RdYlBu)
                
                # 绘制数据点
                y_pred = svm_model.predict(X if X.shape[1] == svm_model.n_features_in_ 
                                         else np.hstack([X, np.zeros((len(X), svm_model.n_features_in_ - 2))]))
                colors = ['red' if y == 0 else 'blue' for y in y_pred]
                plt.scatter(X[:, 0], X[:, 1], c=colors, alpha=0.6)
                
                plt.title('2D Decision Boundary Visualization')
                plt.xlabel('Feature 1')
                plt.ylabel('Feature 2')
                
                boundary_path = images_dir / f"{save_filename}_2d_boundary.png"
                plt.savefig(boundary_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                image_urls.append(f"/static/images/{boundary_path.name}")
        
        elif visualization_type == "edit_3d":
            # 3D决策边界可视化
            if len(df.columns) >= 3:
                X = df.iloc[:, :3].values
                
                # 创建3D散点图
                fig = go.Figure(data=go.Scatter3d(
                    x=X[:, 0],
                    y=X[:, 1],
                    z=X[:, 2],
                    mode='markers',
                    marker=dict(
                        size=5,
                        color=range(len(X)),
                        colorscale='Viridis',
                        showscale=True
                    )
                ))
                
                fig.update_layout(
                    title='3D Feature Visualization',
                    scene=dict(
                        xaxis_title='Feature 1',
                        yaxis_title='Feature 2',
                        zaxis_title='Feature 3'
                    )
                )
                
                html_path = images_dir / f"{save_filename}_3d.html"
                fig.write_html(html_path)
                
                image_urls.append(f"/static/images/{html_path.name}")
                interactive_data['3d_plot'] = str(html_path)
        
        elif visualization_type == "optimization":
            # 优化过程可视化
            if 'iteration' in df.columns and 'objective_value' in df.columns:
                plt.figure(figsize=(12, 6))
                
                # 主优化曲线
                plt.subplot(1, 2, 1)
                plt.plot(df['iteration'], df['objective_value'], 'b-', linewidth=2)
                plt.title('Optimization Progress')
                plt.xlabel('Iteration')
                plt.ylabel('Objective Value')
                plt.grid(True, alpha=0.3)
                
                # 累积最优值
                plt.subplot(1, 2, 2)
                cumulative_best = df['objective_value'].cummin()
                plt.plot(df['iteration'], cumulative_best, 'r-', linewidth=2)
                plt.title('Best Value So Far')
                plt.xlabel('Iteration')
                plt.ylabel('Best Objective Value')
                plt.grid(True, alpha=0.3)
                
                plt.tight_layout()
                
                opt_path = images_dir / f"{save_filename}_optimization.png"
                plt.savefig(opt_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                image_urls.append(f"/static/images/{opt_path.name}")
        
        elif visualization_type == "feature_importance":
            # 特征重要性可视化
            if len(df.columns) > 1:
                # 计算特征方差作为重要性指标
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                feature_importance = df[numeric_cols].var().sort_values(ascending=True)
                
                plt.figure(figsize=(10, max(6, len(feature_importance) * 0.3)))
                feature_importance.plot(kind='barh')
                plt.title('Feature Importance (Variance)')
                plt.xlabel('Variance')
                plt.ylabel('Features')
                plt.tight_layout()
                
                importance_path = images_dir / f"{save_filename}_importance.png"
                plt.savefig(importance_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                image_urls.append(f"/static/images/{importance_path.name}")
                interactive_data['feature_importance'] = feature_importance.to_dict()
        
        elif visualization_type == "correlation_matrix":
            # 特征相关性矩阵
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 1:
                correlation_matrix = df[numeric_cols].corr()
                
                plt.figure(figsize=(12, 10))
                sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', 
                           center=0, square=True, fmt='.2f')
                plt.title('Feature Correlation Matrix')
                plt.tight_layout()
                
                corr_path = images_dir / f"{save_filename}_correlation.png"
                plt.savefig(corr_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                image_urls.append(f"/static/images/{corr_path.name}")
                interactive_data['correlation_matrix'] = correlation_matrix.to_dict()
        
        elif visualization_type == "pca":
            # PCA主成分分析
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) >= 2:
                X = df[numeric_cols].values
                
                pca = PCA()
                X_pca = pca.fit_transform(X)
                
                plt.figure(figsize=(15, 5))
                
                # PCA散点图
                plt.subplot(1, 3, 1)
                plt.scatter(X_pca[:, 0], X_pca[:, 1], alpha=0.6)
                plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
                plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
                plt.title('PCA Visualization')
                
                # 方差解释比例
                plt.subplot(1, 3, 2)
                plt.bar(range(1, min(11, len(pca.explained_variance_ratio_) + 1)), 
                       pca.explained_variance_ratio_[:10])
                plt.xlabel('Principal Component')
                plt.ylabel('Explained Variance Ratio')
                plt.title('Explained Variance by Component')
                
                # 累积方差解释比例
                plt.subplot(1, 3, 3)
                cumulative_variance = np.cumsum(pca.explained_variance_ratio_)
                plt.plot(range(1, len(cumulative_variance) + 1), cumulative_variance, 'bo-')
                plt.xlabel('Number of Components')
                plt.ylabel('Cumulative Explained Variance')
                plt.title('Cumulative Explained Variance')
                plt.grid(True, alpha=0.3)
                
                plt.tight_layout()
                
                pca_path = images_dir / f"{save_filename}_pca.png"
                plt.savefig(pca_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                image_urls.append(f"/static/images/{pca_path.name}")
                interactive_data['pca_components'] = X_pca.tolist()
                interactive_data['explained_variance'] = pca.explained_variance_ratio_.tolist()
        
        elif visualization_type == "cluster_analysis":
            # 聚类分析可视化
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) >= 2:
                X = df[numeric_cols].values
                
                # 确定最优聚类数
                max_k = min(10, len(X) // 2)
                if max_k >= 2:
                    inertias = []
                    k_range = range(2, max_k + 1)
                    
                    for k in k_range:
                        kmeans = KMeans(n_clusters=k, random_state=42)
                        kmeans.fit(X)
                        inertias.append(kmeans.inertia_)
                    
                    # 选择膝点方法确定最优k
                    optimal_k = k_range[np.argmin(inertias)] if inertias else 3
                    
                    # 执行聚类
                    kmeans = KMeans(n_clusters=optimal_k, random_state=42)
                    cluster_labels = kmeans.fit_predict(X)
                    
                    plt.figure(figsize=(15, 5))
                    
                    # 肘部图
                    plt.subplot(1, 3, 1)
                    plt.plot(k_range, inertias, 'bo-')
                    plt.xlabel('Number of Clusters (k)')
                    plt.ylabel('Inertia')
                    plt.title('Elbow Method for Optimal k')
                    plt.grid(True, alpha=0.3)
                    
                    # 聚类结果（2D）
                    if X.shape[1] >= 2:
                        plt.subplot(1, 3, 2)
                        scatter = plt.scatter(X[:, 0], X[:, 1], c=cluster_labels, cmap='viridis', alpha=0.6)
                        plt.scatter(kmeans.cluster_centers_[:, 0], kmeans.cluster_centers_[:, 1], 
                                  c='red', marker='x', s=200, linewidths=3)
                        plt.xlabel('Feature 1')
                        plt.ylabel('Feature 2')
                        plt.title(f'Clustering Results (k={optimal_k})')
                        plt.colorbar(scatter)
                    
                    # 聚类大小分布
                    plt.subplot(1, 3, 3)
                    unique, counts = np.unique(cluster_labels, return_counts=True)
                    plt.bar(unique, counts)
                    plt.xlabel('Cluster ID')
                    plt.ylabel('Number of Samples')
                    plt.title('Cluster Size Distribution')
                    
                    plt.tight_layout()
                    
                    cluster_path = images_dir / f"{save_filename}_clusters.png"
                    plt.savefig(cluster_path, dpi=300, bbox_inches='tight')
                    plt.close()
                    
                    image_urls.append(f"/static/images/{cluster_path.name}")
                    interactive_data['cluster_labels'] = cluster_labels.tolist()
                    interactive_data['cluster_centers'] = kmeans.cluster_centers_.tolist()
        
        elif visualization_type == "distribution_plot":
            # 数据分布可视化
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                n_cols = min(4, len(numeric_cols))
                n_rows = (len(numeric_cols) + n_cols - 1) // n_cols
                
                plt.figure(figsize=(15, 4 * n_rows))
                
                for i, col in enumerate(numeric_cols[:16]):  # 最多显示16个特征
                    plt.subplot(n_rows, n_cols, i + 1)
                    
                    # 直方图和密度曲线
                    plt.hist(df[col].dropna(), bins=30, alpha=0.7, density=True)
                    
                    # 添加密度曲线
                    try:
                        from scipy import stats
                        x = np.linspace(df[col].min(), df[col].max(), 100)
                        kde = stats.gaussian_kde(df[col].dropna())
                        plt.plot(x, kde(x), 'r-', linewidth=2)
                    except:
                        pass
                    
                    plt.title(f'Distribution of {col}')
                    plt.xlabel(col)
                    plt.ylabel('Density')
                
                plt.tight_layout()
                
                dist_path = images_dir / f"{save_filename}_distributions.png"
                plt.savefig(dist_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                image_urls.append(f"/static/images/{dist_path.name}")
        
        if progress_callback:
            progress_callback(90.0)
        
        # 清理内存
        plt.close('all')
        
        if progress_callback:
            progress_callback(100.0)
        
        return {
            "visualization_type": visualization_type,
            "image_urls": image_urls,
            "interactive_data": interactive_data,
            "save_filename": save_filename
        }
        
    except Exception as e:
        logger.error(f"可视化生成失败: {e}")
        raise

@router.get("/result/{task_id}", response_model=VisualizationResponse)
async def get_visualization_result(task_id: str):
    """获取可视化结果"""
    try:
        task_info = task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_info.task_type != "visualization":
            raise HTTPException(status_code=400, detail="任务类型不匹配")
        
        if task_info.status != TaskStatus.COMPLETED:
            raise HTTPException(
                status_code=400, 
                detail=f"任务未完成，当前状态: {task_info.status.value}"
            )
        
        result = task_info.result
        if not result:
            raise HTTPException(status_code=404, detail="任务结果不存在")
        
        return VisualizationResponse(
            status=ResponseStatus.SUCCESS,
            message="可视化生成完成",
            task_id=task_id,
            visualization_type=result.get("visualization_type", ""),
            image_urls=result.get("image_urls", []),
            interactive_data=result.get("interactive_data", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取可视化结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取结果失败: {str(e)}")

@router.get("/image/{filename}")
async def get_visualization_image(filename: str):
    """获取可视化图片文件"""
    try:
        image_path = Path("static/images") / filename
        
        if not image_path.exists():
            raise HTTPException(status_code=404, detail="图片文件不存在")
        
        return FileResponse(
            path=str(image_path),
            media_type="image/png",
            filename=filename
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图片失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取图片失败: {str(e)}")

@router.post("/compare_results")
async def compare_visualization_results(
    task_ids: List[str],
    comparison_type: str = "side_by_side"
):
    """
    比较多个可视化结果
    
    Args:
        task_ids: 要比较的可视化任务ID列表
        comparison_type: 比较类型 (side_by_side, overlay, difference)
    """
    try:
        logger.info(f"开始比较 {len(task_ids)} 个可视化结果")
        
        if len(task_ids) < 2:
            raise HTTPException(
                status_code=400,
                detail="至少需要2个任务ID进行比较"
            )
        
        # 获取所有任务结果
        results = []
        for task_id in task_ids:
            task_info = task_manager.get_task_status(task_id)
            if not task_info or task_info.task_type != "visualization":
                raise HTTPException(
                    status_code=404,
                    detail=f"可视化任务不存在: {task_id}"
                )
            
            if task_info.status != TaskStatus.COMPLETED:
                raise HTTPException(
                    status_code=400,
                    detail=f"任务未完成: {task_id}"
                )
            
            results.append(task_info.result)
        
        # 创建比较任务
        task_id = await task_manager.create_task(
            task_type="visualization_comparison",
            task_func=compare_visualizations_worker,
            results=results,
            comparison_type=comparison_type,
            task_ids=task_ids,
            metadata={
                "comparison_type": comparison_type,
                "result_count": len(results)
            }
        )
        
        return TaskResponse(
            status=ResponseStatus.SUCCESS,
            message="可视化比较任务已启动",
            task_id=task_id,
            task_status=TaskStatus.PENDING,
            progress=0.0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动可视化比较失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"启动可视化比较失败: {str(e)}"
        )

async def compare_visualizations_worker(
    results: List[Dict[str, Any]],
    comparison_type: str,
    task_ids: List[str],
    progress_callback=None
):
    """可视化比较工作函数"""
    try:
        import matplotlib.pyplot as plt
        from pathlib import Path
        
        images_dir = Path("static/images")
        comparison_images = []
        
        if comparison_type == "side_by_side":
            # 并排比较
            n_results = len(results)
            fig, axes = plt.subplots(1, n_results, figsize=(5 * n_results, 5))
            
            if n_results == 1:
                axes = [axes]
            
            for i, (result, task_id) in enumerate(zip(results, task_ids)):
                # 这里需要重新加载和显示原始数据
                # 简化版本：显示基本信息
                axes[i].text(0.5, 0.5, f"Task: {task_id}\nType: {result.get('visualization_type', 'Unknown')}", 
                           ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f"Result {i+1}")
            
            plt.tight_layout()
            
            comparison_path = images_dir / f"comparison_{int(time.time())}.png"
            plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            comparison_images.append(f"/static/images/{comparison_path.name}")
        
        elif comparison_type == "overlay":
            # 叠加比较
            # 这需要根据具体的可视化类型来实现
            pass
        
        elif comparison_type == "difference":
            # 差异比较
            # 这需要计算数值差异
            pass
        
        if progress_callback:
            progress_callback(100.0)
        
        return {
            "comparison_type": comparison_type,
            "compared_tasks": task_ids,
            "comparison_images": comparison_images,
            "summary": {
                "total_compared": len(results),
                "visualization_types": [r.get("visualization_type") for r in results]
            }
        }
        
    except Exception as e:
        logger.error(f"可视化比较执行失败: {e}")
        raise

@router.get("/gallery")
async def get_visualization_gallery(
    visualization_type: Optional[str] = None,
    limit: int = Query(default=20, le=100)
):
    """获取可视化画廊"""
    try:
        # 获取所有可视化任务
        viz_tasks = task_manager.get_tasks_by_type("visualization")
        completed_tasks = [
            task for task in viz_tasks 
            if task.status == TaskStatus.COMPLETED and task.result
        ]
        
        # 过滤类型
        if visualization_type:
            completed_tasks = [
                task for task in completed_tasks
                if task.result.get("visualization_type") == visualization_type
            ]
        
        # 限制数量并按时间排序
        completed_tasks.sort(key=lambda x: x.end_time or "", reverse=True)
        completed_tasks = completed_tasks[:limit]
        
        gallery_items = []
        for task in completed_tasks:
            result = task.result
            gallery_items.append({
                "task_id": task.task_id,
                "visualization_type": result.get("visualization_type"),
                "creation_time": task.end_time,
                "image_urls": result.get("image_urls", []),
                "data_id": task.metadata.get("data_id") if task.metadata else None,
                "thumbnail": result.get("image_urls", [None])[0]  # 第一张图作为缩略图
            })
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": f"获取到 {len(gallery_items)} 个可视化结果",
            "gallery": gallery_items,
            "total_count": len(completed_tasks)
        }
        
    except Exception as e:
        logger.error(f"获取可视化画廊失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取可视化画廊失败: {str(e)}"
        )

@router.delete("/cleanup")
async def cleanup_visualization_files(days: int = 7):
    """清理旧的可视化文件"""
    try:
        from pathlib import Path
        import time
        import os
        
        images_dir = Path("static/images")
        if not images_dir.exists():
            return {
                "status": ResponseStatus.SUCCESS,
                "message": "图片目录不存在，无需清理",
                "deleted_count": 0
            }
        
        cutoff_time = time.time() - (days * 24 * 3600)
        deleted_count = 0
        
        for file_path in images_dir.iterdir():
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    deleted_count += 1
                except Exception as e:
                    logger.warning(f"删除文件失败 {file_path}: {e}")
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": f"清理完成，删除了 {deleted_count} 个文件",
            "deleted_count": deleted_count
        }
        
    except Exception as e:
        logger.error(f"清理可视化文件失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"清理文件失败: {str(e)}"
        )

@router.delete("/cancel/{task_id}")
async def cancel_visualization_task(task_id: str):
    """取消可视化任务"""
    try:
        success = await task_manager.cancel_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=400, 
                detail="任务取消失败，可能任务不存在或已完成"
            )
        
        return {
            "status": ResponseStatus.SUCCESS,
            "message": "可视化任务已取消",
            "task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")