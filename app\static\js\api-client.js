/**
 * API客户端模块
 * 统一管理所有API请求，提供错误处理和重试机制
 */

class ApiClient {
    constructor(baseUrl = 'http://localhost:8000/api/v1') {
        this.baseUrl = baseUrl;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
        this.requestQueue = new Map();
        this.retryConfig = {
            maxRetries: 3,
            retryDelay: 1000,
            backoffMultiplier: 2
        };
    }

    /**
     * 通用请求方法
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        // 请求去重
        const requestKey = `${config.method || 'GET'}_${url}_${JSON.stringify(config.body || {})}`;
        if (this.requestQueue.has(requestKey)) {
            return this.requestQueue.get(requestKey);
        }

        const requestPromise = this._executeRequest(url, config);
        this.requestQueue.set(requestKey, requestPromise);

        try {
            const result = await requestPromise;
            return result;
        } finally {
            // 清理请求队列
            setTimeout(() => this.requestQueue.delete(requestKey), 1000);
        }
    }

    /**
     * 执行请求（带重试机制）
     */
    async _executeRequest(url, config, retryCount = 0) {
        try {
            const response = await fetch(url, config);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.message || `请求失败: ${response.statusText}`,
                    response.status,
                    errorData
                );
            }

            return await response.json();
        } catch (error) {
            if (retryCount < this.retryConfig.maxRetries && this._shouldRetry(error)) {
                const delay = this.retryConfig.retryDelay * Math.pow(this.retryConfig.backoffMultiplier, retryCount);
                await this._sleep(delay);
                return this._executeRequest(url, config, retryCount + 1);
            }
            throw error;
        }
    }

    /**
     * 判断是否应该重试
     */
    _shouldRetry(error) {
        if (error instanceof ApiError) {
            return error.status >= 500 || error.status === 429;
        }
        return error.name === 'NetworkError' || error.name === 'TypeError';
    }

    /**
     * 延迟函数
     */
    _sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // API方法
    async uploadFile(file, fileType) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('file_type', fileType);

        return this.request('/upload/file', {
            method: 'POST',
            body: formData,
            headers: {} // 让浏览器自动设置Content-Type
        });
    }

    async uploadFiles(files, fileType) {
        const formData = new FormData();
        files.forEach(file => formData.append('files', file));
        formData.append('file_type', fileType);

        return this.request('/upload/files/batch', {
            method: 'POST',
            body: formData,
            headers: {}
        });
    }

    async startGeneMining(params) {
        return this.request('/mining/start', {
            method: 'POST',
            body: JSON.stringify(params)
        });
    }

    async startGeneIdentification(params) {
        return this.request('/identification/start', {
            method: 'POST',
            body: JSON.stringify(params)
        });
    }

    async startGeneFusion(params) {
        return this.request('/fusion/start', {
            method: 'POST',
            body: JSON.stringify(params)
        });
    }

    async startGeneEditing(params) {
        return this.request('/editing/start', {
            method: 'POST',
            body: JSON.stringify(params)
        });
    }

    async startGeneDesign(params) {
        return this.request('/design/start', {
            method: 'POST',
            body: JSON.stringify(params)
        });
    }

    async getTaskStatus(taskType, taskId) {
        return this.request(`/${taskType}/status/${taskId}`);
    }

    async getTaskResult(taskType, taskId) {
        return this.request(`/${taskType}/result/${taskId}`);
    }

    async startFullPipeline(params) {
        return this.request('/pipeline/start', {
            method: 'POST',
            body: JSON.stringify(params)
        });
    }
}

/**
 * API错误类
 */
class ApiError extends Error {
    constructor(message, status, data = {}) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.data = data;
    }
}

// 导出全局实例
window.apiClient = new ApiClient();
window.ApiError = ApiError;