"""
服务层模块

提供业务逻辑服务，包括文件处理、任务管理等。
服务层负责协调各个组件，实现具体的业务功能。

主要服务：
- FileHandler: 文件上传、存储和管理
- TaskManager: 异步任务创建、执行和监控
"""

from app.services.file_handler import FileHandler, file_handler
from app.services.task_manager import TaskManager, task_manager, TaskInfo

# 服务配置
SERVICE_CONFIG = {
    "file_handler": {
        "max_file_size": 100 * 1024 * 1024,  # 100MB
        "allowed_extensions": [".cif", ".csv"],
        "upload_dir": "uploads",
        "results_dir": "results",
        "cleanup_days": 7
    },
    "task_manager": {
        "max_concurrent_tasks": 5,
        "task_timeout": 3600,  # 1小时
        "cleanup_days": 7,
        "executor_workers": 4
    }
}

# 服务状态检查
def check_services_health() -> dict:
    """检查所有服务的健康状态"""
    health_status = {
        "overall": "healthy",
        "services": {}
    }
    
    # 检查文件处理服务
    try:
        file_count = len(file_handler.list_files())
        disk_usage = file_handler.get_disk_usage()
        health_status["services"]["file_handler"] = {
            "status": "healthy",
            "file_count": file_count,
            "disk_usage_mb": disk_usage.get("total_size_mb", 0)
        }
    except Exception as e:
        health_status["services"]["file_handler"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["overall"] = "degraded"
    
    # 检查任务管理服务
    try:
        task_stats = task_manager.get_task_statistics()
        health_status["services"]["task_manager"] = {
            "status": "healthy",
            "active_tasks": task_stats.get("active_tasks", 0),
            "total_tasks": task_stats.get("total_tasks", 0),
            "success_rate": task_stats.get("success_rate", 0)
        }
    except Exception as e:
        health_status["services"]["task_manager"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["overall"] = "degraded"
    
    return health_status

# 服务初始化
async def initialize_services():
    """初始化所有服务"""
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        # 清理旧文件和任务
        await file_handler.cleanup_old_files()
        await task_manager.cleanup_old_tasks()
        
        logger.info("服务初始化完成")
    except Exception as e:
        logger.error(f"服务初始化失败: {e}")
        raise

# 服务清理
async def cleanup_services():
    """清理服务资源"""
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        # 等待运行中的任务
        if task_manager.running_tasks:
            logger.info(f"等待 {len(task_manager.running_tasks)} 个任务完成...")
            # 可以添加超时机制
        
        # 关闭线程池
        task_manager.executor.shutdown(wait=True)
        
        logger.info("服务清理完成")
    except Exception as e:
        logger.error(f"服务清理失败: {e}")

# 导出的类和实例
__all__ = [
    # 类
    "FileHandler",
    "TaskManager",
    "TaskInfo",
    
    # 单例实例
    "file_handler",
    "task_manager",
    
    # 配置
    "SERVICE_CONFIG",
    
    # 函数
    "check_services_health",
    "initialize_services",
    "cleanup_services"
]

# 服务层异常类
class ServiceException(Exception):
    """服务层基础异常"""
    pass

class FileServiceException(ServiceException):
    """文件服务异常"""
    pass

class TaskServiceException(ServiceException):
    """任务服务异常"""
    pass

# 添加到导出列表
__all__.extend([
    "ServiceException",
    "FileServiceException",
    "TaskServiceException"
])