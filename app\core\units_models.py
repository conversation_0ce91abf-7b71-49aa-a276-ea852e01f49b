from __future__ import print_function, division
import argparse
import functools
import json
import logging
import math
import os
import random
from time import time
import warnings
from glob import glob
from typing import Literal, Optional
import numpy as np
import pandas as pd
import torch
import yaml
from pydantic import BaseModel
from sklearn.cluster import KMeans
from sklearn.manifold import TSNE
from sklearn.metrics import davies_bouldin_score, mean_absolute_error,mean_absolute_percentage_error, r2_score,silhouette_score
from sklearn.preprocessing import LabelBinarizer
from tqdm import tqdm
from ase.io import read
from pymatgen.core import Structure
from pymatgen.core.periodic_table import Element
from pymatgen.io.ase import AseAtomsAdaptor
import torch.nn as nn
from torch import Tensor, autograd
from torch.autograd import Variable
from torch.nn import functional as F
from torch.utils.data import DataLoader, Dataset, SequentialSampler,SubsetRandomSampler
from torch.utils.data.dataloader import default_collate
import matplotlib.pyplot as plt
from app.core.fastkan.fastkan import FastKANLayer
import warnings
import datetime
warnings.filterwarnings('ignore')
##############cgcl_model#########################
class ConvLayer(nn.Module):
    def __init__(self, atom_fea_len, nbr_fea_len):
        super(ConvLayer, self).__init__()
        self.atom_fea_len = atom_fea_len
        self.nbr_fea_len = nbr_fea_len
        self.fc_full = nn.Linear(2*self.atom_fea_len+self.nbr_fea_len,
                                 2*self.atom_fea_len)
        self.sigmoid = nn.Sigmoid()
        self.softplus1 = nn.Softplus()
        self.bn1 = nn.BatchNorm1d(2*self.atom_fea_len)
        self.bn2 = nn.BatchNorm1d(self.atom_fea_len)
        self.softplus2 = nn.Softplus()

    def forward(self, atom_in_fea, nbr_fea, nbr_fea_idx):
        N, M = nbr_fea_idx.shape
        atom_nbr_fea = atom_in_fea[nbr_fea_idx, :]
        total_nbr_fea = torch.cat(
            [atom_in_fea.unsqueeze(1).expand(N, M, self.atom_fea_len),
             atom_nbr_fea, nbr_fea], dim=2)
        total_gated_fea = self.fc_full(total_nbr_fea)
        total_gated_fea = self.bn1(total_gated_fea.view(
            -1, self.atom_fea_len*2)).view(N, M, self.atom_fea_len*2)
        nbr_filter, nbr_core = total_gated_fea.chunk(2, dim=2)
        nbr_filter = self.sigmoid(nbr_filter)
        nbr_core = self.softplus1(nbr_core)
        nbr_sumed = torch.sum(nbr_filter * nbr_core, dim=1)
        nbr_sumed = self.bn2(nbr_sumed)
        out = self.softplus2(atom_in_fea + nbr_sumed)
        return out
class CrystalGraphConvNet(nn.Module):
    def __init__(self, orig_atom_fea_len, nbr_fea_len,
                 atom_fea_len=64, n_conv=3, h_fea_len=128, n_h=1,
                 classification=False):
        super(CrystalGraphConvNet, self).__init__()
        self.classification = classification
        self.embedding = nn.Linear(orig_atom_fea_len, atom_fea_len)
        self.convs = nn.ModuleList([ConvLayer(atom_fea_len=atom_fea_len,
                                    nbr_fea_len=nbr_fea_len)
                                    for _ in range(n_conv)])
        self.conv_to_fc = nn.Linear(atom_fea_len, h_fea_len)
        self.conv_to_fc_softplus = nn.Softplus()
        if n_h > 1:
            self.fcs = nn.ModuleList([nn.Linear(h_fea_len, h_fea_len)
                                      for _ in range(n_h-1)])
            self.softpluses = nn.ModuleList([nn.Softplus()
                                             for _ in range(n_h-1)])
        if self.classification:
            self.fc_out = nn.Linear(h_fea_len, 2)
        else:
            self.fc_out = nn.Linear(h_fea_len, 1)
        if self.classification:
            self.logsoftmax = nn.LogSoftmax(dim=1)
            self.dropout = nn.Dropout()
        ######### 对比学习参数  ############
        self.logit_scale = nn.Parameter(torch.ones([]) * np.log(1 / 0.07))
        self.register_buffer('total_labels', torch.arange(30000))

    def forward(self, atom_fea, nbr_fea, nbr_fea_idx, crystal_atom_idx):
        
        atom_fea = self.embedding(atom_fea)

        for conv_func in self.convs:
            atom_fea = conv_func(atom_fea, nbr_fea, nbr_fea_idx)
        crys_fea = self.pooling(atom_fea, crystal_atom_idx)
        crys_fea = self.conv_to_fc(self.conv_to_fc_softplus(crys_fea))
        crys_fea = self.conv_to_fc_softplus(crys_fea)
        if self.classification:
            crys_fea = self.dropout(crys_fea)
        if hasattr(self, 'fcs') and hasattr(self, 'softpluses'):
            for fc, softplus in zip(self.fcs, self.softpluses):
                crys_fea = softplus(fc(crys_fea))

        ############ 对比学习部分 ##############
        contr_fea = crys_fea / crys_fea.norm(dim=-1, keepdim=True)
        logit_scale = self.logit_scale.exp()
        logits = contr_fea @ contr_fea.t()

        leng, _ = logits.size()
        labels = self.total_labels[:leng]
        contr_loss = torch.nn.functional.cross_entropy(logits, labels)
        out = self.fc_out(crys_fea)
        if self.classification:
            out = self.logsoftmax(out)
        return out, contr_loss, crys_fea

    def pooling(self, atom_fea, crystal_atom_idx):
        assert sum([len(idx_map) for idx_map in crystal_atom_idx]) ==\
            atom_fea.data.shape[0]
        summed_fea = [torch.mean(atom_fea[idx_map], dim=0, keepdim=True)
                      for idx_map in crystal_atom_idx]
        return torch.cat(summed_fea, dim=0)
######################dataload#############################
def get_train_val_test_loader(dataset, collate_fn=default_collate,
                              batch_size=64, train_ratio=None,
                              val_ratio=0.1, test_ratio=0.1, return_test=False,
                              num_workers=1, pin_memory=False, **kwargs):
    prefetch_factor = 8
    total_size = len(dataset)
    if kwargs['train_size'] is None:
        if train_ratio is None:
            assert val_ratio + test_ratio < 1
            train_ratio = 1 - val_ratio - test_ratio
            print(f'[Warning] train_ratio is None, using 1 - val_ratio - '
                  f'test_ratio = {train_ratio} as training data.')
        else:
            assert train_ratio + val_ratio + test_ratio <= 1
    indices = list(range(total_size))
    if kwargs['train_size']:
        train_size = kwargs['train_size']
    else:
        train_size = int(train_ratio * total_size)
    if kwargs['test_size']:
        test_size = kwargs['test_size']
    else:
        test_size = int(test_ratio * total_size)
    if kwargs['val_size']:
        valid_size = kwargs['val_size']
    else:
        valid_size = int(val_ratio * total_size)
    train_sampler = SubsetRandomSampler(indices[:train_size])
    val_sampler = SubsetRandomSampler(
        indices[-(valid_size + test_size):-test_size])
    if return_test:
        test_sampler = SubsetRandomSampler(indices[-test_size:])
    train_loader = DataLoader(dataset, batch_size=batch_size,
                              sampler=train_sampler,
                              num_workers=num_workers,
                              prefetch_factor=prefetch_factor,
                              collate_fn=collate_fn, pin_memory=pin_memory)
    val_loader = DataLoader(dataset, batch_size=batch_size,
                            sampler=val_sampler,
                            num_workers=num_workers,
                            prefetch_factor=prefetch_factor,
                            collate_fn=collate_fn, pin_memory=pin_memory)
    if return_test:
        test_loader = DataLoader(dataset, batch_size=batch_size,
                                 sampler=test_sampler,
                                 num_workers=0,
                                 collate_fn=collate_fn, pin_memory=pin_memory)
    if return_test:
        return train_loader, val_loader, test_loader
    else:
        return train_loader, val_loader
def collate_pool(dataset_list):
    batch_atom_fea, batch_nbr_fea, batch_nbr_fea_idx = [], [], []
    crystal_atom_idx, batch_target = [], []
    batch_cif_ids = []
    base_idx = 0
    for i, ((atom_fea, nbr_fea, nbr_fea_idx), target, cif_id)\
            in enumerate(dataset_list):
        n_i = atom_fea.shape[0]  # number of atoms for this crystal
        batch_atom_fea.append(atom_fea)
        batch_nbr_fea.append(nbr_fea)
        batch_nbr_fea_idx.append(nbr_fea_idx+base_idx)
        new_idx = torch.LongTensor(np.arange(n_i)+base_idx)
        crystal_atom_idx.append(new_idx)
        batch_target.append(target)
        batch_cif_ids.append(cif_id)
        base_idx += n_i
    return (torch.cat(batch_atom_fea, dim=0),
            torch.cat(batch_nbr_fea, dim=0),
            torch.cat(batch_nbr_fea_idx, dim=0),
            crystal_atom_idx),\
        torch.stack(batch_target, dim=0),\
        batch_cif_ids
class GaussianDistance(object):

    def __init__(self, dmin, dmax, step, var=None):

        assert dmin < dmax
        assert dmax - dmin > step
        self.filter = np.arange(dmin, dmax+step, step)
        if var is None:
            var = step
        self.var = var

    def expand(self, distances):

        return np.exp(-(distances[..., np.newaxis] - self.filter)**2 /
                      self.var**2)
class AtomInitializer(object):
    def __init__(self, atom_types):
        self.atom_types = set(atom_types)
        self._embedding = {}

    def get_atom_fea(self, atom_type):
        assert atom_type in self.atom_types
        return self._embedding[atom_type]

    def load_state_dict(self, state_dict):
        self._embedding = state_dict
        self.atom_types = set(self._embedding.keys())
        self._decodedict = {idx: atom_type for atom_type, idx in
                            self._embedding.items()}

    def state_dict(self):
        return self._embedding

    def decode(self, idx):
        if not hasattr(self, '_decodedict'):
            self._decodedict = {idx: atom_type for atom_type, idx in
                                self._embedding.items()}
        return self._decodedict[idx]
class AtomCustomJSONInitializer(AtomInitializer):
    def __init__(self, elem_embedding_file):
        with open(elem_embedding_file) as f:
            elem_embedding = json.load(f)
        elem_embedding = {int(key): value for key, value
                          in elem_embedding.items()}
        atom_types = set(elem_embedding.keys())
        super(AtomCustomJSONInitializer, self).__init__(atom_types)
        for key, value in elem_embedding.items():
            self._embedding[key] = np.array(value, dtype=float)
class CIFData(Dataset):
    def __init__(self, cif_datas,atom_json, max_num_nbr=12, radius=12, dmin=0, step=0.2,
                 random_seed=123):
        self.max_num_nbr, self.radius = max_num_nbr, radius
        self.id_prop_data = [file for file in cif_datas]
        random.seed(random_seed)
        random.shuffle(self.id_prop_data)
        atom_init_file = atom_json
        assert atom_init_file, 'atom_init.json does not exist!'
        self.ari = AtomCustomJSONInitializer(atom_init_file)
        self.gdf = GaussianDistance(dmin=dmin, dmax=self.radius, step=step)

    def __len__(self):
        return len(self.id_prop_data)

    @functools.lru_cache(maxsize=None)  # Cache loaded structures
    def __getitem__(self, idx):
        cif_file = self.id_prop_data[idx]
        target = 0
        crystal = Structure.from_file(cif_file)
        atom_fea = np.vstack([self.ari.get_atom_fea(crystal[i].specie.number)
                              for i in range(len(crystal))])
        atom_fea = torch.Tensor(atom_fea)
        all_nbrs = crystal.get_all_neighbors(self.radius, include_index=True)
        all_nbrs = [sorted(nbrs, key=lambda x: x[1]) for nbrs in all_nbrs]
        nbr_fea_idx, nbr_fea = [], []
        for nbr in all_nbrs:
            if len(nbr) < self.max_num_nbr:

                nbr_fea_idx.append(list(map(lambda x: x[2], nbr)) +
                                   [0] * (self.max_num_nbr - len(nbr)))
                nbr_fea.append(list(map(lambda x: x[1], nbr)) +
                               [self.radius + 1.] * (self.max_num_nbr -
                                                     len(nbr)))
            else:
                nbr_fea_idx.append(list(map(lambda x: x[2],
                                            nbr[:self.max_num_nbr])))
                nbr_fea.append(list(map(lambda x: x[1],
                                        nbr[:self.max_num_nbr])))
        nbr_fea_idx, nbr_fea = np.array(nbr_fea_idx), np.array(nbr_fea)
        nbr_fea = self.gdf.expand(nbr_fea)
        atom_fea = torch.Tensor(atom_fea)
        nbr_fea = torch.Tensor(nbr_fea)
        nbr_fea_idx = torch.LongTensor(nbr_fea_idx)
        target = torch.Tensor([float(target)])
        return (atom_fea, nbr_fea, nbr_fea_idx), target, idx
class Normalizer(object):
    """Normalize a Tensor and restore it later. """

    def __init__(self, tensor):
        """tensor is taken as a sample to calculate the mean and std"""
        self.mean = torch.mean(tensor)
        self.std = torch.std(tensor)

    def norm(self, tensor):
        return (tensor - self.mean) / self.std

    def denorm(self, normed_tensor):
        return normed_tensor * self.std + self.mean

    def state_dict(self):
        return {'mean': self.mean,
                'std': self.std}

    def load_state_dict(self, state_dict):
        self.mean = state_dict['mean']
        self.std = state_dict['std']

################cgcl###################
def main_cgcl(mydata_cifs, mydata_atom_json,model_path="./pretrain_model_dict/model_best.pth.tar",afs_features=None):
    #print(mydata_atom_json,model_path,afs_features)
    # 检查模型文件是否存在
    if os.path.isfile(model_path):
        try:
            model_checkpoint = torch.load(model_path, map_location=lambda storage, loc: storage,weights_only=False)
            model_args = argparse.Namespace(**model_checkpoint['args'])
        except Exception as e:
            print(str(e))
            return None
    else:
        print("error")
        return None
    # 加载数据
    print(len(mydata_cifs),mydata_atom_json)
    dataset = CIFData(mydata_cifs, mydata_atom_json)
    collate_fn = collate_pool
    test_loader = DataLoader(dataset, batch_size=1, shuffle=False,
                            num_workers=4, collate_fn=collate_fn, pin_memory=True)
    print(len(test_loader))
    # 构建模型
    structures, _, _ = dataset[0]
    orig_atom_fea_len = structures[0].shape[-1]
    nbr_fea_len = structures[1].shape[-1]
    model = CrystalGraphConvNet(orig_atom_fea_len, nbr_fea_len,
                                atom_fea_len=model_args.atom_fea_len,
                                n_conv=model_args.n_conv,
                                h_fea_len=model_args.h_fea_len,
                                n_h=model_args.n_h,
                                classification=True if model_args.task == 'classification' else False)

    if torch.cuda.is_available():
        model.cuda()
    normalizer = Normalizer(torch.zeros(3))
    # 加载权重
    if os.path.isfile(model_path):
        try:
            checkpoint = torch.load(model_path, map_location=lambda storage, loc: storage,weights_only=False)
            model.load_state_dict(checkpoint['state_dict'])
            normalizer.load_state_dict(checkpoint['normalizer'])
        except Exception as e:
            print(str(e))
            return None
    else:
        return None
    embeddings = []
    try:
        for i, (input, target, batch_cif_ids) in enumerate(test_loader):
                #print(i)
                if torch.cuda.is_available():
                    input_var = (Variable(input[0].cuda(non_blocking=True)),
                                Variable(input[1].cuda(non_blocking=True)),
                                input[2].cuda(non_blocking=True),
                                [crys_idx.cuda(non_blocking=True) for crys_idx in input[3]])
                else:
                    input_var = (Variable(input[0]),
                                Variable(input[1]),
                                input[2],
                                input[3])

                # 获取模型的输出
                try:
                    output = model(*input_var)
                    embeddings.append(output[2].detach().cpu())
                except Exception as e:
                    
                    return None
    except Exception as e:
        return None
    # 合并所有批次的嵌入
    data = np.vstack(embeddings)
    print("cgcl",data.shape)
    return data
##############################egcl_begin##############################################
#################data_load###########################
def convert(seconds):
    seconds = seconds % (24 * 3600)
    hour = seconds // 3600
    seconds %= 3600
    minutes = seconds // 60
    seconds %= 60

    return "%d:%02d:%02d" % (hour, minutes, seconds)
torch.set_default_dtype(torch.float32)
class Graph(object):
    '''
    Graph object fro creation of atomic graphs with bond and node attributes from pymatgen structure
    '''

    def __init__(
        self,
        neighbors=12,
        rcut=0,
        delta=1,
    ):

        self.neighbors = neighbors
        self.rcut = rcut
        self.delta = delta
        # self.atom = []
        self.bond = []
        self.nbr = []
        self.angle_cosines = []

    def setGraphFea(self, structure):

        if self.rcut > 0:
            pass
        else:
            species = [site.specie.symbol for site in structure.sites]
            self.rcut = max(
                [Element(elm).atomic_radius * 3 for elm in species]
            )

        all_nbrs = structure.get_all_neighbors(self.rcut, include_index=True)

        len_nbrs = np.array([len(nbr) for nbr in all_nbrs])

        indexes = np.where((len_nbrs < self.neighbors))[0]

        # print(self.rcut,len(indexes))

        for i in indexes:
            cut = self.rcut
            curr_N = len(all_nbrs[i])
            while curr_N < self.neighbors:
                cut += self.delta
                #  print("I am here")
                nbr = structure.get_neighbors(structure[i], cut)
                # print("I am here1")
                curr_N = len(nbr)
            # print("got it",count)
            # count +=1
            all_nbrs[i] = nbr

        all_nbrs = [sorted(nbrs, key=lambda x: x[1]) for nbrs in all_nbrs]

        self.nbr = torch.LongTensor(
            [
                list(map(lambda x: x[2], nbrs[: self.neighbors]))
                for nbrs in all_nbrs
            ]
        )
        self.bond = torch.Tensor(
            [
                list(map(lambda x: x[1], nbrs[: self.neighbors]))
                for nbrs in all_nbrs
            ]
        )

        cart_coords = torch.Tensor(np.array(
            [structure[i].coords for i in range(len(structure))]
        ))
        atom_nbr_fea = torch.Tensor(np.array(
            [
                list(map(lambda x: x[0].coords, nbrs[: self.neighbors]))
                for nbrs in all_nbrs
            ]
        ))
        centre_coords = cart_coords.unsqueeze(1).expand(
            len(structure), self.neighbors, 3
        )
        dxyz = atom_nbr_fea - centre_coords
        r = self.bond.unsqueeze(2)
        self.angle_cosines = torch.matmul(
            dxyz, torch.swapaxes(dxyz, 1, 2)
        ) / torch.matmul(r, torch.swapaxes(r, 1, 2))
class load_graphs_targets(object):

    '''
    structureData should
    be in dict format
                      structure:{pymatgen structure},
                      property:{}
                      formula: None or formula
    if not from database
    '''

    def __init__(self, neighbors=12, rcut=0, delta=1):

        self.neighbors = neighbors
        self.rcut = rcut
        self.delta = delta

    def load(self, data):
        structure = data["structure"]
        target = data["target"]
        # print(target)
        graph = Graph(
            neighbors=self.neighbors, rcut=self.rcut, delta=self.delta
        )
        # try:
        # print("graphs")
        graph.setGraphFea(structure)
        # print("graphs done")
        return (graph, target)
        # except:
        #    return None
def process(func, tasks, n_proc, mp_load=False, mp_pool=None):
    if mp_load:
        results = []
        chunks = [tasks[i : i + n_proc] for i in range(0, len(tasks), n_proc)]
        for chunk in chunks:
            # print("chunks")
            r = mp_pool.map_async(func, chunk, callback=results.append)
            r.wait()
        mp_pool.close()
        mp_pool.join()
        return results[0]
    else:
        # print("chunks")
        return [func(task) for task in tasks]
class CrystalGraphDataset(Dataset):
    '''
    A Crystal graph dataset container for genrating and loading pytorch dataset to be passed to train test and validation loader
    '''

    def __init__(
        self,
        dataset,
        neighbors=12,
        rcut=0,
        delta=1,
        mp_load=False,
        mp_pool=None,
        mp_cpu_count=None,
        **kwargs
    ):
        # ================================
        print("Loading {} graphs .......".format(len(dataset)))
        # =================================
        t1 = time()
        load_graphs = load_graphs_targets(
            neighbors=neighbors,
            rcut=rcut,
            delta=delta,
        )
        results = process(
            load_graphs.load,
            dataset,
            mp_cpu_count,
            mp_load=mp_load,
            mp_pool=mp_pool,
        )
        self.graphs = [res[0] for res in results if res is not None]

        self.targets = [
            torch.LongTensor(res[1]) for res in results if res is not None
        ]
        # print(self.targets)
        self.binarizer = LabelBinarizer()
        self.binarizer.fit(torch.cat(self.targets))

        t2 = time()
        print("Total time taken {}".format(convert(t2 - t1)))

        self.size = len(self.targets)

    def collate(self, datalist):

        bond_feature, nbr_idx, angular_feature, crys_idx, targets = (
            [],
            [],
            [],
            [],
            [],
        )

        index = 0

        for (bond_fea, idx, angular_fea), targ in datalist:
            Natoms = bond_fea.shape[0]

            bond_feature.append(bond_fea)
            angular_feature.append(angular_fea)

            nbr_idx.append(idx + index)
            crys_idx.append([index, index + Natoms])
            targets.append(targ)
            index += Natoms

        return (
            torch.cat(bond_feature, dim=0),
            torch.cat(angular_feature, dim=0),
            torch.cat(nbr_idx, dim=0),
            torch.LongTensor(crys_idx),
            torch.cat(targets, dim=0),
        )

    def __getitem__(self, idx):

        graph = self.graphs[idx]
        bond_feature = graph.bond
        nbr_idx = graph.nbr
        angular_feature = graph.angle_cosines
        target = self.targets[idx]

        return (bond_feature, nbr_idx, angular_feature), target
def prepare_batch_fn(batch, device, non_blocking):
    (bond_feature, angular_feature, nbr_idx, crys_idx, target) = batch
    return (
        bond_feature.to(device, non_blocking=non_blocking),
        angular_feature.to(device, non_blocking=non_blocking),
        nbr_idx.to(device, non_blocking=non_blocking),
        crys_idx.to(device, non_blocking=non_blocking),
    ), target.to(device, non_blocking=non_blocking)

##########setting#####################
class Settings(BaseModel):

    # ----------------------------------------------------
    search_type: Literal["local", "global"] = "local"

    POOL : dict={"local": False, "global": True}

    # ---------------------Graph creation--------------------

    neighbors: int = 12
    rcut: float = 3.0
    search_delta: float = 1.0
    n_classification: int = 7

    # --------------dataloader parameter--------------------

    train_size: Optional[int] = None
    test_size: Optional[int] = None
    val_size: Optional[int] = None
    train_ratio: float = 0.8
    val_ratio: float = 0.1
    test_ratio: float = 0.1
    return_test: bool = True
    num_workers: int = 1
    pin_memory: bool = False
    batch_size: int = 24

    # ----------------- model training parameters-----------------

    bond_fea_len: int = 80
    angle_fea_len: int = 80
    n_conv_edge: int = 3

    gbf_bond: dict = {'dmin': 0, 'dmax': 8, 'steps': bond_fea_len}
    gbf_angle: dict = {'dmin': -1, 'dmax': 1, 'steps': angle_fea_len}

    h_fea_edge: int = 128  # hidden feature len
    h_fea_angle: int = 128
    # Number of hidden layer

    @property
    def pooling(self):
        return self.POOL[self.search_type]

    embedding: bool = False
    checkpoint_every: int = 1

    # ----------------- model run parameters-----------------

    resume: bool = False
    epochs: int = 100
    optimizer: Literal["adam", "sgd"] = "adam"
    weight_decay: float = 0
    momentum: float = 0.9
    learning_rate: float = 1e-2

    scheduler: bool = True
    gamma: float = 0.1
    step_size: int = 30

    write_checkpoint: bool = True
    progress: bool = True
    auto_weight: float=0.000001
######################################
def get_factor(l):
    return math.sqrt((2 * l + 1) / 4 * math.pi)
# -------------------------------------------------------------------
class gbf_expansion(nn.Module):
    def __init__(self, gbf):
        super().__init__()
        self.min = gbf["dmin"]
        self.max = gbf["dmax"]
        self.steps = gbf["steps"]
        self.gamma = (self.max - self.min) / self.steps
        self.register_buffer(
            "filters", torch.linspace(self.min, self.max, self.steps)
        )

    def forward(self, data: torch.Tensor, bond=True) -> torch.Tensor:
        if bond:
            return torch.exp(
                -((data.unsqueeze(2) - self.filters) ** 2) / self.gamma**2
            )
        else:
            return torch.exp(
                -((data.unsqueeze(3) - self.filters) ** 2) / self.gamma**2
            )
# -------------------------------------------------------------------
class legendre_expansion(nn.Module):
    def __init__(self, l):
        super().__init__()
        self.l = l

    def forward(self, data: torch.Tensor) -> torch.Tensor:
        P0 = torch.clone(data)
        P0[:, :, :] = 1
        P1 = data
        if self.l == 0:
            return P0.unsqueeze(3) * get_factor(0)
        if self.l == 1:
            return torch.stack([P0, P1 * get_factor(1)], dim=3)
        else:
            factors = [get_factor(0), get_factor(1)]
            retvars = [P0, P1]
            for i in range(2, self.l):
                P = (1 / (i + 1)) * (
                    (2 * i + 1) * data * retvars[i - 1] - i * retvars[i - 2]
                )
                retvars.append(P)
                factors.append(get_factor(i))

        retvars = [var * factors[i] for i, var in enumerate(retvars)]
        return torch.stack(retvars, dim=3)
# -------------------------------------------------------------------
def Concat(atom_feature, bond_feature, nbr_idx):

    N, M = nbr_idx.shape
    _, O = atom_feature.shape
    _, _, P = bond_feature.shape

    index = nbr_idx.unsqueeze(1).expand(N, M, M)
    xk = atom_feature[index, :]
    xj = atom_feature[nbr_idx, :]
    xi = atom_feature.unsqueeze(1).expand(N, M, O)
    xij = torch.cat([xi, xj], dim=2)
    xij = xij.unsqueeze(2).expand(N, M, M, 2 * O)
    xijk = torch.cat([xij, xk], dim=3)

    eij = bond_feature.unsqueeze(2).expand(N, M, M, P)
    eik = bond_feature[nbr_idx, :]
    eijk = torch.cat([eij, eik], dim=3)

    return torch.cat([xijk, eijk], dim=3)
# -------------------------------------------------------------------
class ConvAngle(nn.Module):
    """
    Convolutional operation on graphs
    """

    def __init__(
        self,
        edge_fea_len,
        angle_fea_len,
    ):

        super(ConvAngle, self).__init__()

        self.angle_fea_len = angle_fea_len
        self.edge_fea_len = edge_fea_len

        # -------------------Angle----------------------

        self.lin_angle = nn.Linear(
            self.angle_fea_len + 2 * self.edge_fea_len, self.angle_fea_len
        )

        # ---------------Angle 3body attention------------------------

        self.attention_1 = nn.Linear(
            self.angle_fea_len + 2 * self.edge_fea_len, 1
        )
        self.leakyrelu_1 = nn.LeakyReLU(negative_slope=0.01)
        self.bn_ijkl = nn.LayerNorm(self.angle_fea_len + 2 * self.edge_fea_len)
        self.bn_ijkl = nn.LayerNorm(self.angle_fea_len)
        self.softplus_1 = nn.Softplus()

        self.bn_2 = nn.LayerNorm(self.angle_fea_len)
        self.softplus_2 = nn.Softplus()

    def forward(self, angle_fea, edge_fea, nbr_idx):

        N, M, O, P = angle_fea.shape

        # ---------------Edge update--------------------------

        eij = edge_fea.unsqueeze(2).expand(N, M, M, P)
        eik = edge_fea[nbr_idx, :]
        eijk = torch.cat([eij, eik], dim=3)

        angle_fea_cat = torch.cat([eijk, angle_fea], dim=3)

        attention_1 = self.attention_1(angle_fea_cat)
        alpha_1 = self.leakyrelu_1(attention_1)
        angle_fea_cat = alpha_1 * self.lin_angle(angle_fea_cat)

        angle_fea_summed = angle_fea_cat
        angle_fea_summed = angle_fea + angle_fea_summed
        angle_fea_summed = self.bn_2(angle_fea_summed)
        angle_fea_summed = self.softplus_2(angle_fea_summed)

        return angle_fea_summed
# -------------------------------------------------------------------
class ConvEdge(nn.Module):
    """
    Convolutional operation on graphs
    """

    def __init__(self, edge_fea_len, angle_fea_len):

        super(ConvEdge, self).__init__()

        self.edge_fea_len = edge_fea_len
        self.angle_fea_len = angle_fea_len

        # -------------------Angle----------------------
        self.lin_edge = nn.Linear(
            2 * self.edge_fea_len + self.angle_fea_len, self.edge_fea_len
        )

        # ---------------edege attention------------------------

        self.attention_1 = nn.Linear(
            2 * self.edge_fea_len + self.angle_fea_len, 1
        )
        self.leakyrelu_1 = nn.LeakyReLU(negative_slope=0.01)
        self.softmax_1 = nn.Softmax(dim=2)
        self.bn_1 = nn.LayerNorm(self.edge_fea_len)
        self.softplus_1 = nn.Softplus()

        self.bn_2 = nn.LayerNorm(self.edge_fea_len)
        self.softplus_2 = nn.Softplus()

    def forward(self, edge_fea, angle_fea, nbr_idx):

        N, M = nbr_idx.shape

        eij = edge_fea.unsqueeze(2).expand(N, M, M, self.edge_fea_len)

        eik = edge_fea[nbr_idx, :]

        # print(edge_fea.shape)
        # print(nbr_idx.shape)

        edge_fea_cat = torch.cat([eij, eik, angle_fea], dim=3)

        attention_1 = self.attention_1(edge_fea_cat)
        alpha_1 = self.softmax_1(self.leakyrelu_1(attention_1))

        
        edge_fea_cat = alpha_1 * self.lin_edge(edge_fea_cat)


        edge_fea_cat = self.bn_1(edge_fea_cat)
        edge_fea_cat = self.softplus_1(edge_fea_cat)

        edge_fea_summed = edge_fea + torch.sum(edge_fea_cat, dim=2)

        edge_fea_summed = self.bn_2(edge_fea_summed)
        edge_fea_summed = self.softplus_2(edge_fea_summed)

        return edge_fea_summed
# -------------------------------------------------------------------
class CEGAN(nn.Module):
    """
    Create a crystal graph convolutional neural network for predicting total material properties.
    """

    def __init__(
        self,
        gbf_bond,
        gbf_angle,
        n_conv_edge=3,
        h_fea_edge=128,
        h_fea_angle=128,
        n_classification=14,
        pooling=True,
        embedding=False,
    ):

        super(CEGAN, self).__init__()

        self.bond_fea_len = gbf_bond["steps"]
        self.angle_fea_len = gbf_angle["steps"]
        self.gbf_bond = gbf_expansion(gbf_bond)
        self.gbf_angle = gbf_expansion(gbf_angle)
        self.pooling = pooling
        self.embedding = embedding

        self.EdgeConv = nn.ModuleList(
            [
                ConvEdge(self.bond_fea_len, self.angle_fea_len)
                for _ in range(n_conv_edge)
            ]
        )
        self.AngConv = nn.ModuleList(
            [
                ConvAngle(self.bond_fea_len, self.angle_fea_len)
                for _ in range(n_conv_edge - 1)
            ]
        )

        self.expandEdge = nn.Linear(self.bond_fea_len, h_fea_edge)
        self.expandAngle = nn.Linear(self.angle_fea_len, h_fea_angle)

        self.bn = nn.LayerNorm(h_fea_edge + h_fea_angle)
        self.conv_to_fc_softplus = nn.Softplus()

        self.out = nn.Linear(h_fea_edge + h_fea_angle, n_classification)

        self.dropout = nn.Dropout()
        
        
        ######### 对比学习参数  ############
        self.logit_scale = nn.Parameter(torch.ones([]) * np.log(1 / 0.07))
        self.register_buffer('total_labels', torch.arange(30000))

    def forward(self, data):

        bond_fea, angle_fea, nbr_idx, crys_idx = data

        edge_fea = self.gbf_bond(bond_fea)
        angle_fea = self.gbf_angle(angle_fea, bond=False)

        # print(angle_fea.shape)
        # print(edge_fea.shape)

        edge_fea = self.EdgeConv[0](edge_fea, angle_fea, nbr_idx)

        for conv_edge, conv_angle in zip(self.EdgeConv[1:], self.AngConv):
            angle_fea = conv_angle(angle_fea, edge_fea, nbr_idx)
            edge_fea = conv_edge(edge_fea, angle_fea, nbr_idx)

        edge_fea = self.expandEdge(self.dropout(edge_fea))
        angle_fea = self.expandAngle(self.dropout(angle_fea))

        edge_fea = torch.sum(self.conv_to_fc_softplus(edge_fea), dim=1)

        # print(edge_fea.shape)

        angle_fea = torch.sum(
            self.conv_to_fc_softplus(
                torch.sum(self.conv_to_fc_softplus(angle_fea), dim=2)
            ),
            dim=1,
        )

        # print(angle_fea.shape)

        crys_fea = torch.cat([edge_fea, angle_fea], dim=1)
        # print(crys_fea.shape)

        if self.pooling:
            crys_fea = self.pool(crys_fea, crys_idx)
            # print("pooled",crys_fea.shape)

        crys_fea = self.conv_to_fc_softplus(self.bn(crys_fea))
        if self.embedding:
            embed = crys_fea

        # crys_fea = self.dropout(crys_fea)
        # out = self.out(crys_fea)
        logit_scale = self.logit_scale.exp()
        logits = crys_fea @ crys_fea.t()

        leng, _ = logits.size()
        labels = self.total_labels[:leng]
        out = torch.nn.functional.cross_entropy(logits, labels)
        
        
        # print(out.shape)

        if self.embedding:
            return out, embed

        else:
            return out

    def pool(self, atom_fea, crys_idx):

        # print(crystal_atom_idx)

        summed_fea = [
            torch.mean(
                atom_fea[idx_map[0] : idx_map[1], :], dim=0, keepdim=True
            )
            for idx_map in crys_idx
        ]
        return torch.cat(summed_fea, dim=0)
############CEGAN#######################
def main_egcl(cifs,custom_yaml="./custom_config.yaml", model_path="./pretrain_model_dict/best_contrast.pt",soap_features=None):
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    random.seed(42)
    # 加载自定义配置
    if custom_yaml:
        with open(custom_yaml, 'r') as file:
            custom_dict = yaml.full_load(file)
            settings = Settings(**custom_dict)
    else:
        settings = Settings()
    classification_dataset = []
    struct_label = []
    for file_path in cifs:
        try:
            label = file_path.split("/")[-1].replace(".cif", "")
            structure = Structure.from_file(file_path)
            struct_label.append(label)
            dictionary = {
                'structure': structure,
                'target': np.array([0]),  # 虚拟目标
            }
            classification_dataset.append(dictionary)
        except Exception as e:
            continue
    graphs = CrystalGraphDataset(
        classification_dataset,
        neighbors=settings.neighbors,
        rcut=settings.rcut,
        delta=settings.search_delta,
    )
    net = CEGAN(
        settings.gbf_bond,
        settings.gbf_angle,
        n_conv_edge=settings.n_conv_edge,
        h_fea_edge=settings.h_fea_edge,
        h_fea_angle=settings.h_fea_angle,
        n_classification=settings.n_classification,
        pooling=settings.pooling,
        embedding=True,
    )
    net.to(device)
    try:
        best_checkpoint = torch.load(model_path, map_location=torch.device(device),weights_only=False)
        net.load_state_dict(best_checkpoint)
    except Exception as e:
        print("egcl",str(e))
        return None
    predictions = []
    
    for i in range(graphs.size):
        outdata = graphs.collate([graphs[i]])
        label = struct_label[i]
        x, y = prepare_batch_fn(outdata, device, non_blocking=False)
        predict, embedding = net(x)
        predictions.append(embedding.cpu().detach().numpy())
    # 将预测结果转换为 NumPy 数组
    data = np.vstack(predictions)
    print("egcl",data.shape)
    return data
######################feature_embedding_done##############################################
###########gru_material mix##########################
class Graph_clip(object):
    '''
    Graph object fro creation of atomic graphs with bond and node attributes from pymatgen structure
    '''

    def __init__(
        self,
        neighbors=12,
        rcut=0,
        delta=1,
        atom_init_file="./atom_init.json"
    ):
        atom_init_file = atom_init_file
        self.radius = 12
        self.max_num_nbr=12
        self.ari = AtomCustomJSONInitializer(atom_init_file)
        self.gdf = GaussianDistance(dmin=0, dmax=self.radius, step=0.2)
        self.neighbors = neighbors
        self.rcut = rcut
        self.delta = delta
        # self.atom = []
        self.bond = []
        self.nbr = []
        self.angle_cosines = []

    def setGraphFea(self, structure):

        if self.rcut > 0:
            pass
        else:
            species = [site.specie.symbol for site in structure.sites]
            self.rcut = max(
                [Element(elm).atomic_radius * 3 for elm in species]
            )

        all_nbrs = structure.get_all_neighbors(self.rcut, include_index=True)

        len_nbrs = np.array([len(nbr) for nbr in all_nbrs])

        indexes = np.where((len_nbrs < self.neighbors))[0]

        atom_fea = np.vstack([self.ari.get_atom_fea(structure[i].specie.number)
                              for i in range(len(structure))])
        
        atom_fea = torch.Tensor(atom_fea)
        all_nbrs_cgcnn = [sorted(nbrs, key=lambda x: x[1]) for nbrs in all_nbrs]
        nbr_fea_idx, nbr_fea = [], []
        for nbr in all_nbrs_cgcnn:
            if len(nbr) < self.max_num_nbr:
                # warnings.warn('{} not find enough neighbors to build graph. '
                #               'If it happens frequently, consider increase '
                #               'radius.'.format(cif_id))
                nbr_fea_idx.append(list(map(lambda x: x[2], nbr)) +
                                   [0] * (self.max_num_nbr - len(nbr)))
                nbr_fea.append(list(map(lambda x: x[1], nbr)) +
                               [self.radius + 1.] * (self.max_num_nbr -
                                                     len(nbr)))
            else:
                nbr_fea_idx.append(list(map(lambda x: x[2],
                                            nbr[:self.max_num_nbr])))
                nbr_fea.append(list(map(lambda x: x[1],
                                        nbr[:self.max_num_nbr])))
        nbr_fea_idx, nbr_fea = np.array(nbr_fea_idx), np.array(nbr_fea)
        nbr_fea = self.gdf.expand(nbr_fea)
        self.atom_fea = torch.Tensor(atom_fea)
        self.nbr_fea = torch.Tensor(nbr_fea)
        self.nbr_fea_idx = torch.LongTensor(nbr_fea_idx)

        # print(self.rcut,len(indexes))

        for i in indexes:
            cut = self.rcut
            curr_N = len(all_nbrs[i])
            while curr_N < self.neighbors:
                cut += self.delta
                #  print("I am here")
                nbr = structure.get_neighbors(structure[i], cut)
                # print("I am here1")
                curr_N = len(nbr)
            # print("got it",count)
            # count +=1
            all_nbrs[i] = nbr

        all_nbrs = [sorted(nbrs, key=lambda x: x[1]) for nbrs in all_nbrs]

        self.nbr = torch.LongTensor(
            [
                list(map(lambda x: x[2], nbrs[: self.neighbors]))
                for nbrs in all_nbrs
            ]
        )
        self.bond = torch.Tensor(
            [
                list(map(lambda x: x[1], nbrs[: self.neighbors]))
                for nbrs in all_nbrs
            ]
        )

        cart_coords = torch.Tensor(np.array(
            [structure[i].coords for i in range(len(structure))]
        ))
        atom_nbr_fea = torch.Tensor(np.array(
            [
                list(map(lambda x: x[0].coords, nbrs[: self.neighbors]))
                for nbrs in all_nbrs
            ]
        ))
        centre_coords = cart_coords.unsqueeze(1).expand(
            len(structure), self.neighbors, 3
        )
        dxyz = atom_nbr_fea - centre_coords
        r = self.bond.unsqueeze(2)
        self.angle_cosines = torch.matmul(
            dxyz, torch.swapaxes(dxyz, 1, 2)
        ) / torch.matmul(r, torch.swapaxes(r, 1, 2))
# -------------------------------------------------
class load_graphs_targets_clip(object):

    '''
    structureData should
    be in dict format
                      structure:{pymatgen structure},
                      property:{}
                      formula: None or formula
    if not from database
    '''

    def __init__(self, neighbors=12, rcut=0, delta=1):

        self.neighbors = neighbors
        self.rcut = rcut
        self.delta = delta

    def load(self, data):
        structure = data["structure"]
        target = data["elas"]
        # print(target)
        graph = Graph_clip(
            neighbors=self.neighbors, rcut=self.rcut, delta=self.delta
        )
        # try:
        # print("graphs")
        graph.setGraphFea(structure)
        # print("graphs done")
        return (graph, target)
        # except:
        #    return None
class CrystalGraphDataset_clip(Dataset):
    '''
    A Crystal graph dataset container for genrating and loading pytorch dataset to be passed to train test and validation loader
    '''

    def __init__(
        self,
        dataset,
        neighbors=12,
        rcut=0,
        delta=1,
        mp_load=False,
        mp_pool=None,
        mp_cpu_count=None,
        **kwargs
    ):

        # ================================
        print("Loading {} graphs .......".format(len(dataset)))
        # =================================

        t1 = time()

        load_graphs = load_graphs_targets_clip(
            neighbors=neighbors,
            rcut=rcut,
            delta=delta,
        )

        results = process(
            load_graphs.load,
            dataset,
            mp_cpu_count,
            mp_load=mp_load,
            mp_pool=mp_pool,
        )

        # print(results)

        self.graphs = [res[0] for res in results if res is not None]

        self.targets = [
            torch.tensor(res[1]).float() for res in results if res is not None
        ]
        # print(self.targets)
        # self.binarizer = LabelBinarizer()
        # self.binarizer.fit(torch.cat(self.targets))

        t2 = time()
        print("Total time taken {}".format(convert(t2 - t1)))

        self.size = len(self.targets)

    def collate(self, datalist):

        bond_feature, nbr_idx, angular_feature, crys_idx, targets = (
            [],
            [],
            [],
            [],
            [],
        )
        batch_atom_fea, batch_nbr_fea, batch_nbr_fea_idx = [], [], []
        crystal_atom_idx, batch_target = [], []
        batch_cif_ids = []

        index = 0

        for (bond_fea, idx, angular_fea, atom_fea, nbr_fea, nbr_fea_idx), targ in datalist:
            Natoms = bond_fea.shape[0]

            bond_feature.append(bond_fea)
            angular_feature.append(angular_fea)

            batch_atom_fea.append(atom_fea)
            batch_nbr_fea.append(nbr_fea)
            batch_nbr_fea_idx.append(nbr_fea_idx+index)
            new_idx = torch.LongTensor(np.arange(Natoms)+index)
            crystal_atom_idx.append(new_idx)
            
            nbr_idx.append(idx + index)
            crys_idx.append([index, index + Natoms])
            targets.append(targ.unsqueeze(0))
            index += Natoms


        return (
            torch.cat(bond_feature, dim=0),
            torch.cat(angular_feature, dim=0),
            torch.cat(nbr_idx, dim=0),
            torch.LongTensor(crys_idx),
            torch.cat(targets, dim=0)),\
            (torch.cat(batch_atom_fea, dim=0),
            torch.cat(batch_nbr_fea, dim=0),
            torch.cat(batch_nbr_fea_idx, dim=0),
            crystal_atom_idx)

    def __getitem__(self, idx):

        graph = self.graphs[idx]
        bond_feature = graph.bond
        nbr_idx = graph.nbr
        angular_feature = graph.angle_cosines

        atom_fea = graph.atom_fea
        nbr_fea = graph.nbr_fea
        nbr_fea_idx = graph.nbr_fea_idx

        target = self.targets[idx]

        return (bond_feature, nbr_idx, angular_feature, atom_fea, nbr_fea, nbr_fea_idx), target
def get_train_val_test_loader_clip(
    dataset,
    collate_fn=default_collate,
    batch_size=64,
    train_ratio=None,
    val_ratio=0.1,
    test_ratio=0.1,
    num_workers=1,
    pin_memory=False,
    **kwargs,
):
    total_size = dataset.size
    indices = list(range(total_size))
    data = DataLoader(
        dataset,
        batch_size=batch_size,
        sampler = SequentialSampler(indices),
        num_workers=num_workers,
        collate_fn=collate_fn,
        pin_memory=pin_memory,
    )
    return data
def get_clip_data(cifs, data):
    array_dtype = np.float64
    cifs = [i for i in cifs]
    converted_value = np.array([[-1000]], dtype=array_dtype)
    # print(cifs)
    # print(data)
    if data is not None:
       
        
        # 创建文件名集合（兼容带.cif和不带.cif的情况）
        data_cif_names = set(
            [name if '.cif' in name else name + '.cif' 
             for name in data.iloc[:, 0].tolist()]
        )
        filtered_fe, filtered_atoms, filtered_files = [], [], []
        pbar = tqdm(cifs, total=len(cifs), desc='Processing CIF files')
        for cif in pbar:
            cif_name = cif
            #print("cif_name",cif_name)
            # 统一比较标准：都包含.cif后缀
            compare_name = cif_name if cif_name in data_cif_names else None
            #print("compare_name",compare_name)
            if compare_name is not None:
                matched_rows = data[data.iloc[:, 0] == compare_name]
                if len(matched_rows) == 0:
                    compare_name = compare_name.replace('.cif', '')
                    matched_rows = data[data.iloc[:, 0] == compare_name]
                target_values = matched_rows.iloc[:, 1].values
                # 形状保护逻辑
                if len(target_values.shape) == 0:  # 标量
                    target_array = np.array([[target_values]], dtype=array_dtype)
                elif target_values.ndim == 1:
                    if len(target_values) > 1:
                        print(f"\nWarning: Multiple targets for {cif_name}, using first")
                    target_array = np.array([[target_values[0]]], dtype=array_dtype)
                else:
                    print(f"\nError: Invalid target shape for {cif_name}, skipping")
                    continue
                    
                filtered_fe.append(target_array)
                try:
                    atoms = read(cif, format='cif')
                    filtered_atoms.append(atoms)
                    filtered_files.append(cif_name)
                except Exception as e:
                    print(f"\nError reading {cif_name}: {str(e)}")
                    continue
                    
                    
                    
        pbar.close()
        return filtered_fe, filtered_atoms, filtered_files
        
    else:  # 当没有提供目标数据时
        filtered_fe, filtered_atoms, filtered_files = [], [], []
        pbar = tqdm(cifs, total=len(cifs), desc='Processing CIF files')
        
        for cif in pbar:
            try:
                filtered_fe.append(converted_value)
                atoms = read(cif, format='cif')
                filtered_atoms.append(atoms)
                filtered_files.append(cif.split("/")[-1])
            except Exception as e:
                print(f"\nError processing {cif}: {str(e)}")
                continue
                
        pbar.close()
        return filtered_fe, filtered_atoms, filtered_files
torch.set_default_dtype(torch.float32)
def convert(seconds):
    seconds = seconds % (24 * 3600)
    hour = seconds // 3600
    seconds %= 3600
    minutes = seconds // 60
    seconds %= 60

    return "%d:%02d:%02d" % (hour, minutes, seconds)

###########model_clip##################
logger = logging.getLogger(__name__)
class CLIPConfig:
    embd_pdrop = 0.1
    resid_pdrop = 0.1
    attn_pdrop = 0.1

    def __init__(self, block_size, **kwargs):
        self.block_size = block_size
        for k, v in kwargs.items():
            setattr(self, k, v)
class PointNetConfig:
    """ base PointNet config """

    def __init__(self, embeddingSize, numberofPoints,
                 **kwargs):
        self.embeddingSize = embeddingSize
        self.numberofPoints = numberofPoints  # number of points


        for k, v in kwargs.items():
            setattr(self, k, v)
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout, max_len):
        """
        :param d_model: pe编码维度，一般与word embedding相同，方便相加
        :param dropout: dorp out
        :param max_len: 语料库中最长句子的长度，即word embedding中的L
        """
        super(PositionalEncoding, self).__init__()
        # 定义drop out
        self.dropout = nn.Dropout(p=dropout)
        # 计算pe编码
        pe = torch.zeros(max_len, d_model)  # 建立空表，每行代表一个词的位置，每列代表一个编码位
        position = torch.arange(0, max_len).unsqueeze(1)  # 建个arrange表示词的位置以便公式计算，size=(max_len,1)
        div_term = torch.exp(torch.arange(0, d_model, 2) *  # 计算公式中10000**（2i/d_model)
                             -(math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)  # 计算偶数维度的pe值
        pe[:, 1::2] = torch.cos(position * div_term)  # 计算奇数维度的pe值
        pe = pe.unsqueeze(0)  # size=(1, L, d_model)，为了后续与word_embedding相加,意为batch维度下的操作相同
        self.register_buffer('pe', pe)  # pe值是不参加训练的

    def forward(self, x):
        # 输入的最终编码 = word_embedding + positional_embedding
        x = x + torch.autograd.Variable(self.pe[:, :x.size(1)], requires_grad=False)  # size = [batch, L, d_model]
        return x  # size = [batch, L, d_model]
class seq_emb(nn.Module):
    def __init__(self, config):
        super(seq_emb, self).__init__()
        self.fc = nn.Linear(64, 128)
        self.min = -1.0
        self.max = 15.0
        self.steps = 64
        self.gamma = (self.max - self.min) / (self.steps - 1)
        self.register_buffer(
            "filters", torch.linspace(self.min, self.max, self.steps)
        )        
        
        self.fc3 = nn.Linear(128, config.n_embd)
        self.act_fun = nn.ReLU()
        self.act_tanh = nn.GELU()
        self.drop1 = nn.Dropout(0.1)




    def forward(self, seq):
        seq = seq.unsqueeze(-1)
        seq = seq.expand(-1, 64)
        
        seq = torch.exp(-((seq - self.filters) ** 2) / self.gamma**2)
        
        seq = self.fc(seq)
        seq = self.act_fun(seq)

        seq = self.drop1(seq)

        seq = self.act_tanh(self.fc3(seq))

        return seq
class CLIP(nn.Module):


    def __init__(self, config, pointNetConfig=None):
        super().__init__()

        self.config = config
        self.pointNetConfig = pointNetConfig
        self.pointNet = None

        settings = config.settings
        embeddingSize = config.n_embd
        self.block_size = config.block_size

        self.cegann = CEGAN(
            settings.gbf_bond,
            settings.gbf_angle,
            n_conv_edge=settings.n_conv_edge,
            h_fea_edge=settings.h_fea_edge,
            h_fea_angle=settings.h_fea_angle,
            n_classification=settings.n_classification,
            pooling=settings.pooling,
            embedding=True,
        )

        self.cgcnn = CrystalGraphConvNet(92, 61,
                                         atom_fea_len=64,
                                         n_conv=3,
                                         h_fea_len=128,
                                         n_h=1,
                                         classification=True)

        self.pos_emb_points = PositionalEncoding(embeddingSize, dropout=0.1, max_len=self.pointNetConfig.numberofPoints)

        self.penalty_labels = torch.eye(config.block_size)

        self.points_emb = seq_emb(config)

        # self.drop = nn.Dropout(config.embd_pdrop)

        self.mlp_predict = nn.Sequential(
            nn.Linear(2 * config.n_embd, config.n_embd),
            nn.GELU(),
            nn.Dropout(config.resid_pdrop),
            nn.Linear(config.n_embd, 64),
            nn.GELU(),
            nn.Dropout(config.resid_pdrop),
            nn.Linear(64, pointNetConfig.numberofPoints),
        )

        self.fc_project_formula = FastKANLayer(512, config.n_embd)
        self.fc_project_formula_cgcnn = FastKANLayer(128, config.n_embd)
        self.fc_project_points = FastKANLayer(config.n_embd, config.n_embd)

        self.logit_scale = nn.Parameter(torch.ones([]) * np.log(1 / 0.07))# 0.07
        self.register_buffer('total_labels', torch.arange(30000))
        
        self.rng = torch.Generator()
        self.rng.manual_seed(42)  # 使用常见的种子数字42


        self.ln_f = nn.LayerNorm(512)
        self.ln_f_cgcnn = nn.LayerNorm(128)
        # self.ln_f = nn.LayerNorm(config.n_embd)
        self.ln_p = nn.LayerNorm(config.n_embd)

        self.apply(self._init_weights)

        logger.info("number of parameters: %e", sum(p.numel() for p in self.parameters()))

    def get_block_size(self):
        return self.block_size

    def _init_weights(self, module):
        if isinstance(module, (nn.Linear, nn.Embedding)):
            module.weight.data.normal_(mean=0.0, std=0.02)
            if isinstance(module, nn.Linear) and module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def configure_optimizers(self, train_config):
        """
        This long function is unfortunately doing something very simple and is being very defensive:
        We are separating out all parameters of the model into two buckets: those that will experience
        weight decay for regularization and those that won't (biases, and layernorm/embedding weights).
        We are then returning the PyTorch optimizer object.
        """

        # separate out all parameters to those that will and won't experience regularizing weight decay
        decay = set()
        no_decay = set()
        whitelist_weight_modules = (torch.nn.Linear, torch.nn.Conv1d, torch.nn.Conv2d, torch.nn.GRU)
        blacklist_weight_modules = (torch.nn.LayerNorm, torch.nn.Embedding, torch.nn.BatchNorm1d, torch.nn.BatchNorm2d)
        for mn, m in self.named_modules():
            for pn, p in m.named_parameters():
                fpn = '%s.%s' % (mn, pn) if mn else pn  # full param name

                if pn.endswith('bias'):
                    # all biases will not be decayed
                    no_decay.add(fpn)
                elif pn.endswith('weight') and isinstance(m, whitelist_weight_modules):
                    # weights of whitelist modules will be weight decayed
                    decay.add(fpn)
                elif pn.endswith('weight') and isinstance(m, blacklist_weight_modules):
                    # weights of blacklist modules will NOT be weight decayed
                    no_decay.add(fpn)
                elif pn.startswith('gru1.'):
                    decay.add(fpn)
                elif pn.startswith('gru2.'):
                    decay.add(fpn)
                elif pn.endswith('grid'):
                    no_decay.add(fpn)
                elif mn.endswith('cope'):
                    no_decay.add(fpn)

        # special case the position embedding parameter in the root GPT module as not decayed
        no_decay.add('logit_scale')

        # validate that we considered every parameter
        param_dict = {pn: p for pn, p in self.named_parameters()}
        inter_params = decay & no_decay
        union_params = decay | no_decay
        assert len(inter_params) == 0, "parameters %s made it into both decay/no_decay sets!" % (str(inter_params),)
        assert len(
            param_dict.keys() - union_params) == 0, "parameters %s were not separated into either decay/no_decay set!" \
                                                    % (str(param_dict.keys() - union_params),)

        # create the pytorch optimizer object
        optim_groups = [
            {"params": [param_dict[pn] for pn in sorted(list(decay))], "weight_decay": train_config.weight_decay},
            {"params": [param_dict[pn] for pn in sorted(list(no_decay))], "weight_decay": 0.0},
        ]
        optimizer = torch.optim.AdamW(optim_groups, lr=train_config.learning_rate, betas=train_config.betas)
        return optimizer

    def forward(self, idx, points=None, input_cgcnn=None):
        _, x = self.cegann(idx)
        _, _, x_cgcnn = self.cgcnn(*input_cgcnn)

        b, _ = x.size()

        x = self.ln_f(x)
        formula_embedding_final = self.fc_project_formula(x)

        x_cgcnn = self.ln_f_cgcnn(x_cgcnn)
        formula_embedding_final_cgcnn = self.fc_project_formula_cgcnn(x_cgcnn)

        formula_embedding_final = formula_embedding_final * formula_embedding_final_cgcnn
        # formula_embedding_final = self.ln_f(formula_embedding_final)
        formula_embedding_final = formula_embedding_final / formula_embedding_final.norm(dim=-1, keepdim=True)

        # return formula_embedding_final

        points_embeddings_final = self.points_emb(points)
        points_embeddings_final = self.ln_p(points_embeddings_final)

        points_embeddings_final = self.fc_project_points(points_embeddings_final)
        points_embeddings_final = points_embeddings_final / points_embeddings_final.norm(dim=-1, keepdim=True)

        logit_scale = self.logit_scale.exp()
        logits_per_points = logit_scale * points_embeddings_final @ formula_embedding_final.t()
        logits_per_formula = logits_per_points.t()

        labels = self.total_labels[:b]


        if points.shape[0] == b:
            loss = (F.cross_entropy(logits_per_points, labels) +
                    F.cross_entropy(logits_per_formula, labels)) / 2
        else:
            loss = 0.0

        return loss, logits_per_points, logits_per_formula, formula_embedding_final

###########MAIN#############
def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


@torch.no_grad()
def sample_from_model(model, x, points=None, test=False, input_var=None, iter=None,filtered_files=None):
    device = 'cuda'
    model = model.to(device)
    N = len(x)
    x_new = torch.linspace(-5, 15, 100).to(device)
    loss, predicts, _ ,_= model(points, points=x_new, input_cgcnn=input_var)
    #x = x.unsqueeze(1)
    x_new = x_new.unsqueeze(1)
    predicts = predicts.cpu()
    topN = 5
    targets = torch.tensor(np.arange(N))
    _, indx = predicts.topk(topN, 0)
    indx = indx.t()
    indx = indx.cpu().numpy().tolist()
    if test and iter!=-1:
        targets = targets.cpu().numpy()
        judge = torch.nn.L1Loss(reduce=True, size_average=True)
        ground_truth = []
        best_pred = []
        for i in range(N):
            top3_captions = [indx[i][j] for j in range(len(indx[i]))]
            err = [judge(x[i, :], x_new[j, :]).cpu().numpy() for j in top3_captions]
            ground_truth.append(x[i, :].cpu().numpy())
            best_pred.append(x_new[top3_captions[np.argmin(err)], :].cpu().numpy())
        best_pred = np.array(best_pred)
        ground_truth = np.array(ground_truth)
        rss = np.sum((ground_truth - best_pred) ** 2)
        tss = np.sum((ground_truth - np.mean(ground_truth)) ** 2)
        r2 = 1 - (rss / tss)
        mae = np.mean(np.abs(best_pred - ground_truth))
        mape = mean_absolute_percentage_error(best_pred, ground_truth)
        print(r2,mae,mape)
        # 筛选误差在1以内的文件
        absolute_error = np.abs(best_pred - ground_truth)
        relative_error = absolute_error / np.abs(ground_truth)

        # 筛选相对误差在0.3以内的文件
        within_relative_error = relative_error < 0.3  # 修改为 0.3

        # 确保 filtered_files 是一个 NumPy 数组
        filtered_files_array = np.array(filtered_files)
        filtered_results = filtered_files_array[np.where(within_relative_error)[0]]
        filtered_ground_truth = ground_truth[within_relative_error].flatten()
        filtered_best_pred = best_pred[within_relative_error].flatten()

        # 保存筛选结果到 CSV
        results_df_filtered = pd.DataFrame({
            'FILES': filtered_results,
            'Ground Truth': filtered_ground_truth,
            'Best Prediction': filtered_best_pred,
            'Relative Error': relative_error[within_relative_error].flatten()  # 添加相对误差列
        })
        #results_df_filtered.to_csv(f'filtered_results_iter_{iter}.csv', index=False)

        results_df = pd.DataFrame({
            'FILES':filtered_files,
            'Ground Truth': ground_truth.flatten(),
            'Best Prediction': best_pred.flatten()
        })
        plt.figure(figsize=(8, 6))
        plt.scatter(ground_truth, best_pred, s=30, alpha=0.8)
        plt.plot([min(ground_truth), max(ground_truth)],
                 [min(ground_truth), max(ground_truth)], 'r--', label='y=x', linewidth=3)
        plt.xlabel('Calculated value', fontsize=18)
        plt.ylabel('Predicted value', fontsize=18)
        plt.title('Predicted Value vs Calculated Value', fontsize=20)
        ax = plt.gca()
        for spine in ax.spines.values():
            spine.set_linewidth(2)
        ax.tick_params(width=2)
        plt.text(0.035, 0.85, f'MAPE:{mape:.2f} ', transform=ax.transAxes,
                 fontsize=14, verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.5))
        plt.legend(fontsize=16)
        plt.tick_params(axis='both', which='major', labelsize=14)
        png_file_path = f'./test_set_{iter}.png'
        plt.savefig(png_file_path, format='png')
        plt.close()
        return mape, results_df, png_file_path
    else:
        judge = torch.nn.L1Loss(reduce=True, size_average=True)
        best_pred = []
        for i in range(N):
            topN_captions = [indx[i][j] for j in range(len(indx[i]))]
            # 计算前N个预测值的平均值
            avg_pred = torch.stack([x_new[j, :] for j in topN_captions]).mean(dim=0)
            best_pred.append(avg_pred.cpu().numpy())
        best_pred = np.array(best_pred)
        # print(len(best_pred))
        # print(len(filtered_files))
        results_df = pd.DataFrame({
            'FILES':filtered_files,
            'Best Prediction': best_pred.flatten()
        })
        return results_df
def main_clip(cifs,data_frame=None,custom_config_file=None,model_path='./pretrain_model_dict/best_contrast_clip.pt'):
    try:
        with open(custom_config_file, 'r') as file:
            custom_dict = yaml.full_load(file)
            settings = Settings(**custom_dict)
    except:
        settings=Settings()
    set_seed(42)
    device='cuda'
    embeddingSize = 384 
    n_layers = 2
    n_heads = 4
    numPoints = 9
    blockSize = 31
    filtered_elec, filtered_atoms,filtered_files=get_clip_data(cifs,data_frame)
    print(len(filtered_elec),len(filtered_atoms))
    classification_dataset = []
    data = list(zip(filtered_elec, filtered_atoms))
    for (elas, atoms) in data:
        structure = AseAtomsAdaptor.get_structure(atoms)
        dictionary = {
            'structure': structure,
            'elas': elas
        }
        classification_dataset.append(dictionary)
    graphs = CrystalGraphDataset_clip(
        classification_dataset,
        neighbors=settings.neighbors,
        rcut=settings.rcut,
        delta=settings.search_delta,
    )
    test_loader = get_train_val_test_loader_clip(
        graphs,
        collate_fn=graphs.collate,
        shuffle=False,
        batch_size=len(classification_dataset),
        train_ratio=settings.train_ratio,
        val_ratio=settings.val_ratio,
        test_ratio=settings.test_ratio,
        num_workers=0,
        pin_memory=settings.pin_memory,
        train_size=settings.train_size,
        test_size=settings.test_size,
        val_size=settings.val_size,
    )
    pconf = PointNetConfig(embeddingSize=embeddingSize,
                        numberofPoints=numPoints)
    mconf = CLIPConfig(blockSize,
                    n_layer=n_layers, n_head=n_heads, n_embd=embeddingSize, settings=settings)
    model = CLIP(mconf, pconf)
    ckptPath =model_path
    model.load_state_dict(torch.load(ckptPath, map_location='cpu'))
    model.train()
    str_length=[f"x{i}" for i in range(384)]
    str_length=str_length+["y"]
    data_list=[]
    device = 'cpu'
    model = model.to(device)

    for step, batch in enumerate(test_loader):
        x, y = prepare_batch_fn(batch[0], device, non_blocking=False)
        input = batch[1]
        input_var = (Variable(input[0].to(device)),
                    Variable(input[1].to(device)),
                    input[2].to(device),
                    [crys_idx.to(device) for crys_idx in input[3]])
        x_new = torch.linspace(-5, 15, 200).to(device)
        _,predicts,_,x_embedding = model(x, points=x_new, input_cgcnn=input_var)
        x_new = x_new.unsqueeze(1)
        N=len(y)
        predicts = predicts.cpu()
        topN=5
        targets = torch.tensor(np.arange(N))
        _, indx = predicts.topk(topN, 0)
        indx = indx.t()
        indx = indx.cpu().numpy().tolist()
        targets = targets.cpu().numpy()
        judge = torch.nn.L1Loss(reduce=True, size_average=True)
        ground_truth = []
        best_pred = []
        for i in range(N):
            top3_captions = [indx[i][j] for j in range(len(indx[i]))]
            err = [judge(y[i, :], x_new[j, :]).cpu().numpy() for j in top3_captions]
            ground_truth.append(y[i, :].cpu().numpy())
            best_pred.append(x_new[top3_captions[np.argmin(err)], :].cpu().numpy())
        best_pred = np.array(best_pred)
        ground_truth = np.array(ground_truth)
        batch_size = x_embedding.size()[0]
        print(batch_size,x_embedding.size())
        x_embedding = x_embedding.detach().cpu().numpy()  # 将张量转换为 NumPy 数组
        y = y.detach().cpu().numpy()
        for i in range(batch_size):
            d={f"x{j}":x_embedding[i][j] for j in range(384)}
            d["y"]=y[i][0]
            data_list.append(d)
    data_pair = pd.DataFrame(data_list)
    return data_pair,best_pred,ground_truth

################材料基因编辑算法##############################
#####材料基因编辑算法

from sklearn.datasets import make_classification
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, confusion_matrix, ConfusionMatrixDisplay
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
import pickle
def semi_supervised_learning(X_labeled, y_labeled, X_unlabeled, svc, confidence_threshold=0.8, max_iter=5):
    X_combined = np.copy(X_labeled)
    y_combined = np.copy(y_labeled)
    X_unlabeled_remaining = np.copy(X_unlabeled)
    
    for iteration in range(max_iter):
        # 检查无标签数据是否为空
        if X_unlabeled_remaining.shape[0] == 0:
            print(f"Iteration {iteration + 1}: No remaining unlabeled data. Stopping early.")
            break
        
        # 训练模型
        svc.fit(X_combined, y_combined)
        # 对剩余的无标签数据进行预测
        proba = svc.predict_proba(X_unlabeled_remaining)
        y_pseudo = svc.predict(X_unlabeled_remaining)
        
        # 过滤高置信度的伪标签
        high_confidence_idx = np.max(proba, axis=1) > confidence_threshold
        X_high_confidence = X_unlabeled_remaining[high_confidence_idx]
        y_pseudo_high_confidence = y_pseudo[high_confidence_idx]
        
        # 如果没有高置信度的样本，提前终止
        if len(X_high_confidence) == 0:
            print(f"Iteration {iteration + 1}: No high-confidence pseudo-labels found. Stopping early.")
            break
        
        # 合并高置信度的伪标签数据
        X_combined = np.vstack((X_combined, X_high_confidence))
        y_combined = np.hstack((y_combined, y_pseudo_high_confidence))
        
        # 更新剩余的无标签数据
        X_unlabeled_remaining = X_unlabeled_remaining[~high_confidence_idx]
        
        print(f"Iteration {iteration + 1}: Added {len(X_high_confidence)} high-confidence pseudo-labels.")
    
    return svc, X_combined, y_combined

import re
def main_gene_edit(final_features, flag, label_type, is_train=False, model_path=None, auto_select_ratio=0.3):
    """
    材料基因编辑算法（改进版：处理y与flag无交集情况）
    
    Args:
        final_features: DataFrame, 包含384维特征和目标值y
        flag: float或tuple, 目标性质值或范围
        label_type: str, 标签划分方式:
            - 'interval': 区间内为正样本
            - 'boundary_left': 左侧为正样本
            - 'boundary_right': 右侧为正样本
        is_train: bool, 是否是训练模式
        model_path: str, 模型文件路径
        auto_select_ratio: float, 当y与flag无交集时选择的比例
        
    Returns:
        训练好的模型或加载的模型
    """
    # 参数校验
    
    if not is_train and model_path:
        print("model_path",model_path)
        return _load_model(model_path)
    # ===== 训练模式 =====
    X = final_features.iloc[:, :384].values
    y = final_features['y'].values
    def clean_y_values(y):
        # 如果 y 是字符串加括号的格式（例如 '[6.54]'），去掉括号，提取数字
        return np.array([float(re.sub(r'[^\d.-]', '', str(val))) for val in y])
    def _calculate_labels(y, flag, label_type, ratio):
        """智能标签划分核心逻辑"""
        n_samples = len(y)
        n_select = int(n_samples * ratio)
        y=clean_y_values(y)
        if isinstance(flag, tuple):
            # 区间模式
            left, right = flag
            in_interval = (y >= left) & (y <= right)
            if not np.any(in_interval):  # 如果完全没有交集
                # 计算所有点到区间中点的距离
                mid = (left + right) / 2
                distances = np.abs(y - mid)
                closest_indices = np.argsort(distances)[:n_select]
                labels = np.zeros_like(y, dtype=int)
                labels[closest_indices] = 1
                print(f"警告: y值不在flag区间内，已自动选择最近{ratio*100}%的样本作为正样本")
            else:
                labels = in_interval.astype(int)
        else:
            # 单值模式
            if label_type == 'interval':
                # 区间模式（flag±tol）
                tol = abs(flag) * 0.05
                in_interval = (y >= flag - tol) & (y <= flag + tol)
                
                if not np.any(in_interval):  # 无交集
                    distances = np.abs(y - flag)
                    closest_indices = np.argsort(distances)[:n_select]
                    labels = np.zeros_like(y, dtype=int)
                    labels[closest_indices] = 1
                    print(f"警告: y值不在flag附近，已自动选择最近{ratio*100}%的样本作为正样本")
                else:
                    labels = in_interval.astype(int)
                    
            elif label_type == 'boundary_left':
                # 左侧分界
                if np.all(y > flag):  # 全部在右侧
                    # 选择最靠近flag的30%
                    distances = y - flag
                    closest_indices = np.argsort(distances)[:n_select]
                    labels = np.zeros_like(y, dtype=int)
                    labels[closest_indices] = 1
                    print(f"警告: 所有y值都在flag右侧，已自动选择最接近的{ratio*100}%作为正样本")
                else:
                    labels = (y <= flag).astype(int)
                    
            elif label_type == 'boundary_right':
                # 右侧分界
                if np.all(y < flag):  # 全部在左侧
                    # 选择最靠近flag的30%
                    distances = flag - y
                    closest_indices = np.argsort(distances)[:n_select]
                    labels = np.zeros_like(y, dtype=int)
                    labels[closest_indices] = 1
                    print(f"警告: 所有y值都在flag左侧，已自动选择最接近的{ratio*100}%作为正样本")
                else:
                    labels = (y >= flag).astype(int)
         # ✅ 校验标签分布（防止训练出错）
        unique_counts = np.bincount(labels)
        if min(unique_counts) < 2:
            return None
        return labels
    
    # 执行标签划分
    labels = _calculate_labels(y, flag, label_type, auto_select_ratio)
    if labels is None:
        return _load_model(model_path)
    #print(labels)
    print("正样本数量:", np.sum(labels == 1))
    print("负样本数量:", np.sum(labels == 0))
    # 数据标准化和半监督学习...
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    X_labeled, X_unlabeled, y_labeled, _ = train_test_split(
        X_scaled, labels, test_size=0.2, stratify=labels, random_state=42
    )
    
    svc_model = SVC(
        kernel='rbf', C=1, gamma=0.1, 
        probability=True, random_state=42,
        class_weight="balanced"
    )
    
    
    svc_model, _, _ = semi_supervised_learning(
        X_labeled, y_labeled, X_unlabeled, svc_model,
        confidence_threshold=0.8, max_iter=5
    )
    svc_model.fit(X,labels)

    return svc_model
def save_model_svc(svc,model_path):
    # 保存训练好的 svc 模型到本地文件
    with open(model_path, 'wb') as f:
        pickle.dump(svc, f)
def _load_model(model_path):
    """封装模型加载逻辑"""
    try:
        with open(model_path, 'rb') as f:
            return pickle.load(f)
    except FileNotFoundError:
        raise FileNotFoundError(f"模型文件 {model_path} 不存在")
    except Exception as e:
        raise ValueError(f"模型加载失败: {str(e)}")

##############材料基因设计算法############################
import torch
import numpy as np
class CLIP_pre(nn.Module):
    def __init__(self, config, pointNetConfig=None):
        super().__init__()

        self.config = config
        self.pointNetConfig = pointNetConfig
        self.pointNet = None

        settings = config.settings
        embeddingSize = config.n_embd
        self.block_size = config.block_size

        self.cegann = CEGAN(
            settings.gbf_bond,
            settings.gbf_angle,
            n_conv_edge=settings.n_conv_edge,
            h_fea_edge=settings.h_fea_edge,
            h_fea_angle=settings.h_fea_angle,
            n_classification=settings.n_classification,
            pooling=settings.pooling,
            embedding=True,
        )

        self.cgcnn = CrystalGraphConvNet(92, 61,
                                         atom_fea_len=64,
                                         n_conv=3,
                                         h_fea_len=128,
                                         n_h=1,
                                         classification=True)

        self.pos_emb_points = PositionalEncoding(embeddingSize, dropout=0.1, max_len=self.pointNetConfig.numberofPoints)

        self.penalty_labels = torch.eye(config.block_size)

        self.points_emb = seq_emb(config)

        # self.drop = nn.Dropout(config.embd_pdrop)

        self.mlp_predict = nn.Sequential(
            nn.Linear(2 * config.n_embd, config.n_embd),
            nn.GELU(),
            nn.Dropout(config.resid_pdrop),
            nn.Linear(config.n_embd, 64),
            nn.GELU(),
            nn.Dropout(config.resid_pdrop),
            nn.Linear(64, pointNetConfig.numberofPoints),
        )


        self.fc_project_formula = FastKANLayer(512, config.n_embd)
        self.fc_project_formula_cgcnn = FastKANLayer(128, config.n_embd)
        self.fc_project_points = FastKANLayer(config.n_embd, config.n_embd)

        self.logit_scale = nn.Parameter(torch.ones([]) * np.log(1 / 0.07))# 0.07
        self.register_buffer('total_labels', torch.arange(30000))
        
        self.rng = torch.Generator()
        self.rng.manual_seed(42)  # 使用常见的种子数字42


        self.ln_f = nn.LayerNorm(512)
        self.ln_f_cgcnn = nn.LayerNorm(128)
        # self.ln_f = nn.LayerNorm(config.n_embd)
        self.ln_p = nn.LayerNorm(config.n_embd)

        self.apply(self._init_weights)

        logger.info("number of parameters: %e", sum(p.numel() for p in self.parameters()))

    def get_block_size(self):
        return self.block_size

    def _init_weights(self, module):
        if isinstance(module, (nn.Linear, nn.Embedding)):
            module.weight.data.normal_(mean=0.0, std=0.02)
            if isinstance(module, nn.Linear) and module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def configure_optimizers(self, train_config):
        """
        This long function is unfortunately doing something very simple and is being very defensive:
        We are separating out all parameters of the model into two buckets: those that will experience
        weight decay for regularization and those that won't (biases, and layernorm/embedding weights).
        We are then returning the PyTorch optimizer object.
        """

        # separate out all parameters to those that will and won't experience regularizing weight decay
        decay = set()
        no_decay = set()
        whitelist_weight_modules = (torch.nn.Linear, torch.nn.Conv1d, torch.nn.Conv2d, torch.nn.GRU)
        blacklist_weight_modules = (torch.nn.LayerNorm, torch.nn.Embedding, torch.nn.BatchNorm1d, torch.nn.BatchNorm2d)
        for mn, m in self.named_modules():
            for pn, p in m.named_parameters():
                fpn = '%s.%s' % (mn, pn) if mn else pn  # full param name

                if pn.endswith('bias'):
                    # all biases will not be decayed
                    no_decay.add(fpn)
                elif pn.endswith('weight') and isinstance(m, whitelist_weight_modules):
                    # weights of whitelist modules will be weight decayed
                    decay.add(fpn)
                elif pn.endswith('weight') and isinstance(m, blacklist_weight_modules):
                    # weights of blacklist modules will NOT be weight decayed
                    no_decay.add(fpn)
                elif pn.startswith('gru1.'):
                    decay.add(fpn)
                elif pn.startswith('gru2.'):
                    decay.add(fpn)
                elif pn.endswith('grid'):
                    no_decay.add(fpn)
                elif mn.endswith('cope'):
                    no_decay.add(fpn)

        # special case the position embedding parameter in the root GPT module as not decayed
        no_decay.add('logit_scale')

        # validate that we considered every parameter
        param_dict = {pn: p for pn, p in self.named_parameters()}
        inter_params = decay & no_decay
        union_params = decay | no_decay
        assert len(inter_params) == 0, "parameters %s made it into both decay/no_decay sets!" % (str(inter_params),)
        assert len(
            param_dict.keys() - union_params) == 0, "parameters %s were not separated into either decay/no_decay set!" \
                                                    % (str(param_dict.keys() - union_params),)

        # create the pytorch optimizer object
        optim_groups = [
            {"params": [param_dict[pn] for pn in sorted(list(decay))], "weight_decay": train_config.weight_decay},
            {"params": [param_dict[pn] for pn in sorted(list(no_decay))], "weight_decay": 0.0},
        ]
        optimizer = torch.optim.AdamW(optim_groups, lr=train_config.learning_rate, betas=train_config.betas)
        return optimizer

    def forward(self, formula_embeddings_final, points=None, input_cgcnn=None):
        formula_embedding_final = formula_embeddings_final
        points_embeddings_final = self.points_emb(points)
        points_embeddings_final = self.ln_p(points_embeddings_final)
        points_embeddings_final = self.fc_project_points(points_embeddings_final)
        points_embeddings_final = points_embeddings_final / points_embeddings_final.norm(dim=-1, keepdim=True)
        logit_scale = self.logit_scale.exp()
        logits_per_points = logit_scale * points_embeddings_final @ formula_embedding_final.t()
        return logits_per_points
def load_pre_model(model_path,custom_yaml):
    if os.path.exists(custom_yaml):
        with open(custom_yaml, 'r') as file:
            custom_dict = yaml.full_load(file)
            settings = Settings(**custom_dict)
    else:
        settings = Settings()
    set_seed(42)
    # config
    device='cuda'
    embeddingSize = 384 # the hidden dimension of the representation of both GPT and PT
    n_layers = 2
    n_heads = 4
    numPoints = 9
    blockSize = 31
    pconf = PointNetConfig(embeddingSize=embeddingSize,
                       numberofPoints=numPoints)
    mconf = CLIPConfig(blockSize,
                    n_layer=n_layers, n_head=n_heads, n_embd=embeddingSize, settings=settings)
    model = CLIP_pre(mconf, pconf)
    print('The following model {} has been loaded!'.format(model_path))
    model.load_state_dict(torch.load(model_path, map_location='cpu'))
    model = model.to(device)
    return model
####代理模型给出目标函数
def get_predvalue(embedding, model, return_type="mean"):
    """
    获取模型预测值，并返回指定类型的标量值。
    
    参数:
        embedding: 输入嵌入向量。
        model: 模型实例()。
        return_type: 返回值类型，可选 "mean", "max", "min"。
    """
    model.train()  # 确保模型处于训练模式
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    with torch.no_grad():
        x_new = torch.linspace(-5, 15, 100).to(device)
        embedding = torch.tensor(embedding).float().to(device)
        predicts = model(embedding, points=x_new)
        x_new_cpu = x_new.cpu().unsqueeze(1)
        predicts = predicts.cpu()
        
        topN = 5
        _, indx = predicts.topk(topN, 0)
        indx = indx.t()
        indx = indx.numpy().tolist()  # 已经在CPU，无需.cpu()
        
        top_predictions = [x_new_cpu[j, 0].item() for j in indx]
        
        if return_type == "mean":
            return np.clip(np.mean(top_predictions), -5, 15.0) 
        elif return_type == "max":
            return np.clip(np.max(top_predictions), -5, 15.0) 
        elif return_type == "min":
            return np.clip(np.min(top_predictions), -5, 15.0)
import pickle
# 加载保存的 svc 模型
def load_svc_model(model_path):
    with open(model_path, 'rb') as f:  # 以二进制读取模式打开文件
        model = pickle.load(f)  # 反序列化模型
    return model
        
def objective_function(x, model, svc_model, search_bounds, history,target_value=6.0):
    """
    目标函数，结合代理模型和决策边界模型。
    """
    # 确保输入在搜索范围内
    x = np.clip(x, [b[0] for b in search_bounds], [b[1] for b in search_bounds])
    
    # 使用 SVC 模型判断类别
    predicted_class = svc_model.predict([x])[0]  # 修改索引为 [0]
    if predicted_class != 1:  # 只在类别为 1 的区域探索
        return 1e10  # 用大数值替代无穷大，避免优化器崩溃
    
    # 使用代理模型计算目标值
    value = get_predvalue(x, model, return_type="mean")
    
    # 检查代理模型输出是否异常
    if np.isnan(value) or np.isinf(value):
        value = 1e10  # 处理异常输出
    
    # 返回负值以寻找最小值
    #negative_value = -value
    negative_value =value  # 确保负值在合理范围内
    # 修改6: 根据不同场景调整优化方向
    # 场景1: 寻找低形成能（最小化）
    #objective_value = value  # 直接使用预测值
    
    #场景2: 寻找特定范围（如4-8 eV）
    target =target_value
    negative_value= abs(value - target)  # 最小化与目标的距离
    #记录历史数据
    history["points"].append(x)
    history["values"].append(negative_value)
    # print("history",history["points"])
    return negative_value
import numpy as np
import pandas as pd
import warnings
from skopt import gp_minimize
from skopt import forest_minimize
def optimize_design_space(
    svc_model_path: str,
    proxy_model_path: str,
    config_yaml: str,
    n_calls: int = 2000,
    n_random_starts: int = 20,
    random_state: int = 42,
    search_features=None,
    svc_model=None,
    target_value=6.0,  # 目标值
):
    """
    Optimize design space with proper NaN handling and input validation
    
    Args:
        svc_model_path: SVC model path
        proxy_model_path: Proxy model path
        config_yaml: Model config file path
        n_calls: Total evaluation count
        n_random_starts: Initial random points count
        random_state: Random seed
        search_features: Initial search features (DataFrame or numpy array)
        svc_model: Pre-loaded SVC model (optional)
    
    Returns:
        Dictionary containing optimization results and history
    """
    # Load models
    if not svc_model:
        svc_model = load_svc_model(svc_model_path)
    proxy_model = load_pre_model(proxy_model_path, config_yaml)
    
    # Initialize search space with NaN handling
    if search_features is None:
        n_samples = 5000
        search_space_dim = 384
        random_points = np.random.uniform(-20,20, (n_samples, search_space_dim))
    else:
        # Handle input types and check dimensions
        if isinstance(search_features, pd.DataFrame):
            if search_features.shape[1] < 384:
                raise ValueError(f"DataFrame needs ≥384 columns, got {search_features.shape[1]}")
            random_points = search_features.iloc[:, :384].values
        elif isinstance(search_features, np.ndarray):
            if search_features.ndim != 2 or search_features.shape[1] < 384:
                raise ValueError("numpy array must be 2D with ≥384 columns")
            random_points = search_features[:, :384]
        else:
            raise TypeError("search_features must be DataFrame or numpy array")
        # Clean NaN values
        if np.isnan(random_points).any():
            warnings.warn("Input contains NaN values - replacing with zeros")
            random_points = np.nan_to_num(random_points, nan=0.0)
    # Pre-filter class 1 regions (changed from class_1_mask to be more clear)
    is_class_1 = svc_model.predict(random_points) == 1
    if not np.any(is_class_1):
        raise ValueError("No class 1 points found in initial search space!")
    
    class_1_points = random_points[is_class_1]
    # Calculate search bounds with 3-sigma rule
    means = np.mean(class_1_points, axis=0)
    stds = np.std(class_1_points, axis=0)
    # search_bounds = [(max(m-2*s, -5), min(m+2*s, 5)) for m, s in zip(means, stds)] 
    search_bounds = [(m - 2*s, m + 2*s) for m, s in zip(means, stds)]

    # Initialize history
    history = {"points": [], "values": []}
    result=forest_minimize(
        func=lambda x: objective_function(x, proxy_model, svc_model, search_bounds, history, target_value),
        dimensions=search_bounds,  # 动态定义的搜索范围
        n_calls=n_calls,  # 迭代次数
        n_random_starts=n_random_starts,  # 初始随机采样点数
        random_state=random_state,
        # verbose=True,
        n_jobs=-1
    )
#     result = gp_minimize(
#     func=lambda x: objective_function(x, proxy_model, svc_model,search_bounds, history,target_value),
#     dimensions=search_bounds,  # 动态定义的搜索范围
#     n_calls=n_calls,  # 迭代次数
#     n_random_starts=4,  # 初始随机采样点数
#     random_state=0
# )
    print("here")
    
    # Process results
    best_params = result.x
    best_value = -result.fun  # Convert back to original objective
    
    return {
        "optim_params": best_params,
        "optim_value": best_value,
        "history": history,
        "search_space": search_bounds,
        "success": not np.isinf(best_value),
        "class_1_points":class_1_points,
        "result":best_params
    }

############材料基因表示算法###################

import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
import imageio
import os

def find_best_solution(values, target=6.0, opt_type="min"):
    """
    Find the index of the best solution based on the optimization type
    
    Parameters:
    - values: Array of objective function values
    - target: Target value (only used in 'range' mode)
    - opt_type: Optimization type ("min", "max", "range")
    
    Returns:
    - best_indices: List of best solution indices for each iteration
    """
    best_indices = []
    
    for i in range(len(values)):
        current_values = values[:i+1]
        
        if opt_type == "min":
            best_idx = np.argmin(current_values)
        elif opt_type == "max":
            best_idx = np.argmax(current_values)
        elif opt_type == "range":
            distances = np.abs(current_values - target)
            best_idx = np.argmin(distances)
        else:
            raise ValueError("opt_type must be 'min', 'max', or 'range'")
        
        best_indices.append(best_idx)
    
    return best_indices

def generate_optimization_gif(history, class_1_points, output_dir, target=6.0, opt_type="range", gif_name=None):
    """
    Generate a GIF animation of the optimization process and save to specified output directory.
    
    Parameters:
    - history: Optimization history (points & values)
    - class_1_points: Class 1 data for visualization
    - output_dir: Directory to save frames and final GIF
    - target: Target value for 'range' mode
    - opt_type: Optimization type ("min", "max", "range")
    - gif_name: Optional custom GIF filename
    """

    points = np.array(history["points"])
    values = np.array(history["values"])
    
    pca = PCA(n_components=2)
    class_1_points_2d = pca.fit_transform(class_1_points)
    points_2d = pca.transform(points)
    
    os.makedirs(output_dir, exist_ok=True)  # ✅ 使用外部传入的目录
    
    # 清空旧帧
    current_frames = [f for f in os.listdir(output_dir) if f.startswith("frame_")]
    for f in current_frames:
        os.remove(os.path.join(output_dir, f))

    # coloring logic
    if opt_type == "min":
        cmap = plt.cm.viridis_r
        norm = plt.Normalize(vmin=min(values), vmax=max(values))
        title_suffix = "Minimization"
    elif opt_type == "max":
        cmap = plt.cm.viridis  # 别使用 reverse 来优化颜色
        norm = plt.Normalize(vmin=min(values), vmax=max(values))
        title_suffix = "Maximization"
    else:
        cmap = plt.cm.RdYlBu_r
        distances = np.abs(values - target)
        norm = plt.Normalize(vmin=0, vmax=np.max(distances))
        title_suffix = f"Target Value {target}"
    
    best_indices = find_best_solution(values, target, opt_type)

    frames = []
    for i in range(len(points)):
        plt.figure(figsize=(12, 8))
        plt.scatter(class_1_points_2d[:, 0], class_1_points_2d[:, 1],
                    alpha=0.2, label="Boundary Points", color="lightgray", s=20)

        if opt_type == "range":
            color_values = np.abs(values[:i+1])
        else:
            color_values = values[:i+1]

        scatter = plt.scatter(
            points_2d[:i + 1, 0],
            points_2d[:i + 1, 1],
            c=color_values,
            cmap=cmap,
            norm=norm,
            label="Candidate Points",
            s=60,
            alpha=0.7,
            edgecolors='white',
            linewidth=0.5
        )

        plt.colorbar(scatter)

        # 记录最佳解
        current_best_index = best_indices[i]
        current_best_point = points_2d[current_best_index]
        current_best_value = values[current_best_index]

        plt.scatter(current_best_point[0], current_best_point[1], color='red', s=300,
                    marker='*', edgecolors="black", linewidth=2,
                    label=f"Current Best Value: {current_best_value:.3f}", zorder=10)

        if i > 0:
            best_traj_x = [points_2d[idx, 0] for idx in best_indices[:i+1]]
            best_traj_y = [points_2d[idx, 1] for idx in best_indices[:i+1]]
            plt.plot(best_traj_x, best_traj_y, 'r--', linewidth=2,
                     label="Best Solution Trajectory")
        
        plt.title(f"Optimization Process - {title_suffix}, Iteration: {i + 1}/{len(points)}", fontsize=14, pad=20)
        plt.xlabel("PCA Component 1")
        plt.ylabel("PCA Component 2")
        plt.legend(loc="upper right", fontsize=12, framealpha=0.8)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        frame_path = os.path.join(output_dir, f"frame_{i:03d}.png")
        plt.savefig(frame_path, dpi=150, bbox_inches='tight')
        plt.close()
        frames.append(frame_path)

    # 自动命名功能
    if not gif_name:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        gif_name = f"opt_process_{opt_type}_{timestamp}.gif"

    gif_path = os.path.join(output_dir, gif_name)

    with imageio.get_writer(gif_path, mode='I', duration=0.8) as writer:
        for frame in frames:
            writer.append_data(imageio.imread(frame))

    # 清理帧图片
    for frame in frames:
        os.remove(frame)
    
    return gif_path  # 返回生成的 gif 的完整路径
def visualize_optimization(history, result_opt, class_1_points, output_dir, target=6.0, opt_type="range", img_name=None):
    """
    生成优化过程的静态图像
  
    history: 优化历史（点与目标）
    result_opt: 优化 lbfgs 等推导出的最优值
    class_1_points: 用来绘制背景边界的 class 1 点
    output_dir: 自定义图片保存目录
    target: 目标值（range 模式用）
    opt_type: min / max / range
    """
    points = np.array(history["points"])
    values = np.array(history["values"])
    
    pca = PCA(n_components=2)
    pca.fit(class_1_points) 
    points_2d = pca.transform(points)
    class_2d = pca.transform(class_1_points)
    opt_2d = pca.transform([result_opt])

    # 根据 opt_type 计算 best_values
    best_indices = find_best_solution(values, target, opt_type)
    final_best_value = values[best_indices[-1]]

    # 创建子图
    fig = plt.figure(figsize=(15, 6))
    
    plt.subplot(1, 3, 1)
    plt.plot(values, c='b', alpha=0.7, lw=1.5, label='总目标值')
    best_history = values[np.array(best_indices)]
    plt.scatter(np.array(best_indices), best_history, c='red', label='最优值轨迹', s=30)
    plt.plot(best_history, 'r--', lw=2)
    if opt_type == "range":
        plt.axhline(y=target, color='g', linestyle='--', lw=2, label=f'目标值 {target:.2f}')

    plt.xlabel("迭代")
    plt.ylabel("适应值")
    plt.grid(True, alpha=0.3)
    plt.legend()

    plt.subplot(1, 3, 2)
    plt.scatter(class_2d[:, 0], class_2d[:, 1], alpha=0.1, c='lightgrey', s=20, label='边界原始数据')
    if opt_type == "range":
        scatter = plt.scatter(points_2d[:, 0], points_2d[:, 1],
                              c=np.abs(values - target), cmap=plt.cm.RdYlBu_r, label="适应值到目标", s=50, alpha=0.7)
    else:
        scatter = plt.scatter(points_2d[:, 0], points_2d[:, 1],
                              c=values, cmap=plt.cm.viridis, label="适应值", s=50, alpha=0.7)

    plt.colorbar(scatter)
    plt.scatter(opt_2d[0, 0], opt_2d[0, 1], marker='*', c='red', s=200, label=f"最优解 (值: {final_best_value:.3f})", edgecolor='black', lw=2, zorder=10)
    plt.title("投影向量图 / 完整搜索点")
    plt.grid(True, alpha=0.3)
    plt.legend()

    plt.subplot(1, 3, 3)

    if opt_type == "min":
        improvement = values[0] - final_best_value
        improvement_pct = (improvement / abs(values[0])) * 100 if values[0] != 0 else 0
    elif opt_type == "max":
        improvement = final_best_value - values[0]
        improvement_pct = (improvement / abs(values[0])) * 100 if values[0] != 0 else 0
    else:
        improvement = abs(values[0] - target) - abs(final_best_value - target)
        improvement_pct = (improvement / abs(values[0] - target)) * 100 if abs(values[0] - target) != 0 else 0

    stats = {
        '起始值': f"{values[0]:.4f}",
        '最终值': f"{final_best_value:.4f}",
        '优化次数': f"{len(values)}",
        '优化改善值': f"{improvement:.4f}",
        '改善百分比': f"{improvement_pct:.2f}%",
    }
    if opt_type == "range":
        stats["目标值"] = f"{target:.2f}"

    y_labels = stats.keys()
    y_vals = np.arange(len(y_labels))
    plt.barh(y_vals, [1]*len(y_labels), color="#fee6c1", alpha=0.4)
    plt.yticks(y_vals, y_labels)

    for idx, (key, value) in enumerate(stats.items()):
        plt.text(0.5, idx, value, ha="center", va='center', fontsize=12, fontweight='bold')

    plt.title("优化统计面板")
    plt.xlim(0, 1)
    plt.xticks([])

    plt.tight_layout()
    
    if not img_name:
        import time
        timestamp = time.strftime("%Y%m%d%H%M%S")
        img_name = f"static_optimization_{opt_type}_{timestamp}.png"
        
    img_path = os.path.join(output_dir, img_name)

    plt.savefig(img_path, dpi=120, bbox_inches='tight')
    plt.close()

    return final_best_value, improvement


# def visualize_optimization(history, result, class_1_points, save_filename, target=6.0, opt_type="range"):
#     """
#     Visualize the optimization result in a static plot
    
#     Parameters:
#     - history: Optimization history
#     - result: Optimization result
#     - class_1_points: Data points of class 1
#     - save_filename: Filename to save the plot
#     - target: Target value (only used in 'range' mode)
#     - opt_type: Optimization type ("min", "max", "range")
#     """
#     points = np.array(history["points"])
#     values = np.array(history["values"])
    
#     pca = PCA(n_components=2)
#     class_1_points_2d = pca.fit_transform(class_1_points)
#     points_2d = pca.transform(points)
#     optimal_point_2d = pca.transform([result.x])
    
#     best_indices = find_best_solution(values, target, opt_type)
#     final_best_index = best_indices[-1]
#     final_best_value = values[final_best_index]
    
#     plt.figure(figsize=(15, 6))
    
#     plt.subplot(1, 3, 1)
#     plt.plot(values, 'b-', alpha=0.7, linewidth=1, label="All Candidate Points")
    
#     best_values = [values[idx] for idx in best_indices]
#     plt.plot(best_values, 'r-', linewidth=2, label="Best Value Trajectory")
#     plt.scatter(range(len(best_values)), best_values, c='red', s=30, zorder=5)
    
#     if opt_type == "range":
#         plt.axhline(y=target, color='green', linestyle='--', 
#                     linewidth=2, label=f'Target Value {target}')
    
#     plt.xlabel("Iteration")
#     plt.ylabel("Objective Function Value")
#     plt.title(f"Convergence Curve ({opt_type.upper()})")
#     plt.legend()
#     plt.grid(True, alpha=0.3)
    
#     plt.subplot(1, 3, 2)
#     plt.scatter(class_1_points_2d[:, 0], class_1_points_2d[:, 1], 
#                alpha=0.2, label="Boundary Points", color="lightgray", s=20)
    
#     if opt_type == "range":
#         color_values = np.abs(values - target)
#         cmap = plt.cm.RdYlBu_r
#         cbar_label = f"Distance to Target {target}"
#     else:
#         color_values = values
#         cmap = plt.cm.viridis_r if opt_type == "min" else plt.cm.viridis
#         cbar_label = "Objective Function Value"
    
#     scatter = plt.scatter(points_2d[:, 0], points_2d[:, 1], 
#                          c=color_values, cmap=cmap, 
#                          label="Candidate Points", s=50, alpha=0.7)
#     plt.colorbar(scatter, label=cbar_label)
    
#     plt.scatter(optimal_point_2d[:, 0], optimal_point_2d[:, 1], 
#                s=200, color='red', marker='*', 
#                edgecolors="black", linewidth=2, 
#                label=f"Optimal Solution (Value: {final_best_value:.3f})", zorder=10)
    
#     plt.xlabel("PCA Component 1")
#     plt.ylabel("PCA Component 2")
#     plt.title("Candidate Points and Optimal Solution (PCA Projection)")
#     plt.legend()
#     plt.grid(True, alpha=0.3)
    
#     plt.subplot(1, 3, 3)
    
#     if opt_type == "min":
#         improvement = values[0] - final_best_value
#         improvement_pct = (improvement / abs(values[0])) * 100 if values[0] != 0 else 0
#     elif opt_type == "max":
#         improvement = final_best_value - values[0]
#         improvement_pct = (improvement / abs(values[0])) * 100 if values[0] != 0 else 0
#     else:  # range
#         initial_distance = abs(values[0] - target)
#         final_distance = abs(final_best_value - target)
#         improvement = initial_distance - final_distance
#         improvement_pct = (improvement / initial_distance) * 100 if initial_distance != 0 else 0
    
#     stats = {
#         'Initial Value': f'{values[0]:.4f}',
#         'Final Value': f'{final_best_value:.4f}',
#         'Total Iterations': f'{len(values)}',
#         'Improvement': f'{improvement:.4f}',
#         'Improvement Percentage': f'{improvement_pct:.2f}%'
#     }
    
#     if opt_type == "range":
#         stats['Target Value'] = f'{target:.4f}'
#         stats['Final Distance'] = f'{abs(final_best_value - target):.4f}'
    
#     y_pos = np.arange(len(stats))
#     plt.barh(y_pos, [1] * len(stats), alpha=0.3)
    
#     for i, (key, value) in enumerate(stats.items()):
#         plt.text(0.5, i, f'{key}: {value}', 
#                  ha='center', va='center', fontsize=10, fontweight='bold')
    
#     plt.yticks([])
#     plt.xticks([])
#     plt.title("Optimization Statistics")
#     plt.xlim(0, 1)
    
#     plt.tight_layout()
#     plt.savefig(f'./output/{save_filename}_{opt_type}.png', dpi=120, bbox_inches='tight')
#     plt.show()
    
#     return final_best_value, improvement

# #################材料基因表示算法#############################
# #cgcl+egcl
def show_tsne_result(data,save_filename):
    import numpy as np
    from sklearn.manifold import TSNE
    from sklearn.preprocessing import StandardScaler
    import matplotlib.pyplot as plt
    data = np.array(data)
    # 数据标准化（可选但推荐）
    scaler = StandardScaler()
    data_scaled = scaler.fit_transform(data)

    # 使用t-SNE降维到2维
    tsne = TSNE(
        n_components=2,    # 降维后的维度
        perplexity=min(len(data),5),     # 控制局部与全局结构的平衡，通常取值5-50
        n_iter=1000,       # 迭代次数
        random_state=42    # 随机种子保证可重复性
    )
    data_embedded = tsne.fit_transform(data_scaled)

    # 可视化结果
    plt.figure(figsize=(10, 8))
    plt.scatter(
        data_embedded[:, 0], 
        data_embedded[:, 1],
        s=15,              # 点的大小
        alpha=0.6,         # 透明度
        cmap='viridis'     # 颜色映射（如果数据有标签建议添加c参数）
    )
    
    # 添加图表信息
    plt.title('t-SNE Visualization', fontsize=14)
    plt.xlabel('t-SNE Dimension 1', fontsize=12)
    plt.ylabel('t-SNE Dimension 2', fontsize=12)
    plt.grid(alpha=0.3)   # 添加半透明网格
    
    # 保存并显示图片
    plt.savefig(save_filename, dpi=120, bbox_inches='tight')
    plt.show()

#edit
def show_edit_2d(svc,data_pair,save_filename):
    X = data_pair.drop(columns=["y"]).values
    y = data_pair["y"].values
    def clean_string_array(arr):
        return np.array([
            float(re.sub(r'[^\d.-]', '', str(val))) if isinstance(val, (str, np.str_)) else float(val)
            for val in arr
        ])
    y_clean = clean_string_array(y)
    def plot_decision_boundary_with_params(clf, X, y, C, gamma):
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X)
        # 生成网格数据
        x_min, x_max = X_pca[:, 0].min() - 1, X_pca[:, 0].max() + 1
        y_min, y_max = X_pca[:, 1].min() - 1, X_pca[:, 1].max() + 1
        xx, yy = np.meshgrid(np.arange(x_min, x_max, 0.01), np.arange(y_min, y_max, 0.005))
        grid_points = np.c_[xx.ravel(), yy.ravel()]
        grid_points_high_dim = pca.inverse_transform(grid_points)
        
        Z = clf.predict(grid_points_high_dim)
        Z = Z.reshape(xx.shape)

        # 绘制决策边界
        plt.figure(figsize=(8, 6))
        plt.contourf(xx, yy, Z, alpha=0.8, cmap='viridis')
        
        # 绘制原始数据点的投影
        scatter = plt.scatter(X_pca[:, 0], X_pca[:, 1], c=y, edgecolors='k', cmap='viridis', s=40, alpha=0.9)
        plt.title(f"Semi-Supervised SVM Decision Boundary\nC={C}, Gamma={gamma}", fontsize=18, weight='bold')
        plt.xlabel("Principal Component 1", fontsize=16, labelpad=10, weight='bold')
        plt.ylabel("Principal Component 2", fontsize=16, labelpad=10, weight='bold')
        cbar = plt.colorbar(scatter, ax=plt.gca(), pad=0.01)
        cbar.set_label('Class Label', fontsize=14)
        plt.xticks(fontsize=12)
        plt.yticks(fontsize=12)
        plt.grid(alpha=0.3, linestyle='--')
        plt.tight_layout()
        plt.savefig(save_filename, dpi=120, bbox_inches='tight')
        plt.show()

    # 调用绘图函数
    plot_decision_boundary_with_params(svc, X, y_clean, C=1, gamma=0.5)
def show_edit_3d(svc,data_pair,save_filename):
    X = data_pair.drop(columns=["y"]).values
    y = data_pair["y"].values
    def clean_string_array(arr):
        return np.array([
            float(re.sub(r'[^\d.-]', '', str(val))) if isinstance(val, (str, np.str_)) else float(val)
            for val in arr
        ])
    y_clean = clean_string_array(y)
    def plot_3d_decision_boundary(clf, X, y, title="3D Decision Boundary", cmap='viridis', grid_resolution=0.1):
        """
        绘制3D决策边界。

        参数:
        clf: 训练好的分类器模型。
        X: 特征数据（三维）。
        y: 标签数据。
        title: 图的标题。
        cmap: 颜色映射。
        grid_resolution: 网格的分辨率。
        """
        pca = PCA(n_components=3)
        X_pca = pca.fit_transform(X)
        # 动态调整网格范围
        x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
        y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
        z_min, z_max = X[:, 2].min() - 1, X[:, 2].max() + 1
        xx, yy = np.meshgrid(np.arange(x_min, x_max, grid_resolution),
                            np.arange(y_min, y_max, grid_resolution))
        
        grid_points = np.c_[xx.ravel(), yy.ravel(),np.zeros_like(xx.ravel())]
        grid_points_high_dim = pca.inverse_transform(grid_points)
        
        # 预测网格点的类别
        # Z = clf.predict(np.c_[xx.ravel(), yy.ravel()])
        # Z = Z.reshape(xx.shape)
        Z = clf.predict(grid_points_high_dim)
        Z = Z.reshape(xx.shape)

        # 创建3D图形
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        # 绘制决策边界
        ax.plot_surface(xx, yy, Z, alpha=0.6, cmap=cmap, edgecolor='none')
        
        # 绘制数据点
        scatter = ax.scatter(X[:, 0], X[:, 1], X[:, 2], c=y, cmap=cmap, s=50, alpha=0.9, edgecolors='k', label="Data Points")
        
        # 添加图例
        legend = ax.legend(*scatter.legend_elements(), title="Classes", loc="upper right")
        ax.add_artist(legend)
        
        # 添加标题和标签
        ax.set_title(title, fontsize=18, weight='bold', pad=20)
        ax.set_xlabel("Feature 1", fontsize=14, labelpad=10, weight='bold')
        ax.set_ylabel("Feature 2", fontsize=14, labelpad=10, weight='bold')
        ax.set_zlabel("Feature 3", fontsize=14, labelpad=10, weight='bold')
        
        # 美化图形
        ax.grid(alpha=0.2, linestyle='--')
        plt.tight_layout()
        plt.savefig(save_filename, dpi=120, bbox_inches='tight')
        plt.show()
    plot_3d_decision_boundary(svc, X, y_clean, title="3D SVM Decision Boundary", cmap='plasma', grid_resolution=0.5)
