"""
核心算法模块

包含材料基因算法的核心实现和算法包装器。
将原始算法代码封装为适合API调用的形式。

算法组件：
- 基因挖掘：SOAP描述符、AFS特征
- 基因识别：CGCL、EGCL深度学习模型
- 基因融合：CLIP多模态融合
- 基因编辑：SVM分类器
- 基因设计：贝叶斯优化
"""

from app.core.gene_algorithms import (
    MaterialGeneAlgorithms,
    material_gene_algorithms
)

# 算法配置常量
ALGORITHM_CONSTANTS = {
    # 特征维度
    "SOAP_DIM": 1024,
    "AFS_DIM": 290,
    "CGCL_DIM": 128,
    "EGCL_DIM": 256,
    "CLIP_DIM": 384,
    
    # 默认参数
    "DEFAULT_TARGET_VALUE": 12.04,
    "DEFAULT_N_CALLS": 200,
    "DEFAULT_LABEL_TYPE": "boundary_right",
    
    # 模型配置
    "CGCL_CONFIG": {
        "atom_fea_len": 64,
        "n_conv": 3,
        "h_fea_len": 128,
        "n_h": 1
    },
    
    "EGCL_CONFIG": {
        "neighbors": 12,
        "rcut": 3.0,
        "search_delta": 1.0,
        "n_classification": 7,
        "bond_fea_len": 80,
        "angle_fea_len": 80,
        "n_conv_edge": 3,
        "h_fea_edge": 128,
        "h_fea_angle": 128
    },
    
    "CLIP_CONFIG": {
        "embedding_size": 384,
        "n_layers": 2,
        "n_heads": 4,
        "num_points": 9,
        "block_size": 31
    },
    
    "SVM_CONFIG": {
        "kernel": "rbf",
        "C": 1.0,
        "gamma": 0.1,
        "probability": True,
        "random_state": 42,
        "class_weight": "balanced"
    },
    
    "OPTIMIZATION_CONFIG": {
        "n_random_starts": 20,
        "random_state": 42,
        "confidence_threshold": 0.8,
        "max_iter": 5
    }
}

# 算法阶段定义
ALGORITHM_STAGES = {
    "MINING": {
        "name": "基因挖掘",
        "description": "从晶体结构中提取基础特征",
        "required_inputs": ["cif_files"],
        "outputs": ["soap_features", "afs_features"]
    },
    "IDENTIFICATION": {
        "name": "基因识别",
        "description": "使用深度学习识别高层特征",
        "required_inputs": ["cif_files"],
        "outputs": ["cgcl_features", "egcl_features"]
    },
    "FUSION": {
        "name": "基因融合",
        "description": "多模态特征融合",
        "required_inputs": ["cif_files", "csv_file"],
        "outputs": ["fused_features"]
    },
    "EDITING": {
        "name": "基因编辑",
        "description": "材料性能筛选",
        "required_inputs": ["features_data", "target_value"],
        "outputs": ["svm_model", "predictions"]
    },
    "DESIGN": {
        "name": "基因设计",
        "description": "智能材料优化",
        "required_inputs": ["svm_model", "search_space"],
        "outputs": ["optimal_parameters", "optimization_history"]
    }
}

# 导出算法实例和配置
__all__ = [
    "MaterialGeneAlgorithms",
    "material_gene_algorithms",
    "ALGORITHM_CONSTANTS",
    "ALGORITHM_STAGES"
]

# 模块初始化
def _init_algorithms():
    """初始化算法模块"""
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        # 检查必要的依赖
        import torch
        import sklearn
        import pymatgen
        logger.info("算法依赖检查通过")
    except ImportError as e:
        logger.warning(f"算法依赖缺失: {e}")
    
    # 设置随机种子
    import random
    import numpy as np
    
    seed = 42
    random.seed(seed)
    np.random.seed(seed)
    
    try:
        import torch
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)
    except:
        pass
    
    logger.info("核心算法模块初始化完成")

# 执行初始化
_init_algorithms()