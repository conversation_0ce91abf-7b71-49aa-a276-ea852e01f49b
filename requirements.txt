# ===== 核心框架 =====
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
aiofiles==23.2.1
pydantic==2.5.0
python-dotenv==1.0.0

# ===== 科学计算核心 =====
numpy==1.24.3
pandas==2.0.3
scipy==1.11.4
scikit-learn==1.3.2

# ===== 深度学习框架 =====
torch==2.1.1
torchvision==0.16.1
dgl==1.1.3

# ===== 材料科学专用 =====
pymatgen==2023.10.11
ase==3.22.1

# ===== 数据库和缓存 =====
sqlalchemy==2.0.23
aiosqlite==0.19.0
alembic==1.12.1
redis==5.0.1
psutil==5.9.6

# ===== 配置管理 =====
pyyaml==6.0.1
python-magic==0.4.27

# ===== 可视化和图表 =====
matplotlib==3.8.2
plotly==5.17.0
seaborn==0.13.0

# ===== 网络和HTTP =====
requests==2.31.0
httpx==0.25.2
websockets==12.0

# ===== 图像处理 =====
Pillow==10.1.0
opencv-python==********

# ===== 优化和性能 =====
numba==0.58.1
joblib==1.3.2
tqdm==4.66.1

# ===== 测试和开发工具 =====
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# ===== 日志和监控 =====
loguru==0.7.2
prometheus-client==0.19.0

# ===== 安全和认证 =====
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# ===== 文件格式支持 =====
openpyxl==3.1.2
xlsxwriter==3.1.9