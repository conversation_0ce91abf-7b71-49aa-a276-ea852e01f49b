"""
API v1版本模块

当前版本的API接口实现，提供完整的材料基因算法功能。

更新日志：
- v1.0.0 (2025-6-20): 初始版本发布
  * 完整的五步算法流程
  * 文件管理功能
  * 任务异步处理
  * 可视化生成
  * 批处理支持
"""

# 导入所有API路由模块
from . import (
    upload,
    mining,
    identification,
    fusion,
    editing,
    design,
    visualization,
    pipeline
)

# 路由配置
ROUTE_CONFIG = {
    "upload": {
        "prefix": "/upload",
        "tags": ["文件管理"],
        "dependencies": []
    },
    "mining": {
        "prefix": "/mining",
        "tags": ["基因挖掘"],
        "dependencies": []
    },
    "identification": {
        "prefix": "/identification",
        "tags": ["基因识别"],
        "dependencies": []
    },
    "fusion": {
        "prefix": "/fusion",
        "tags": ["基因融合"],
        "dependencies": []
    },
    "editing": {
        "prefix": "/editing",
        "tags": ["基因编辑"],
        "dependencies": []
    },
    "design": {
        "prefix": "/design",
        "tags": ["基因设计"],
        "dependencies": []
    },
    "visualization": {
        "prefix": "/visualization",
        "tags": ["可视化"],
        "dependencies": []
    },
    "pipeline": {
        "prefix": "/pipeline",
        "tags": ["完整流程"],
        "dependencies": []
    }
}

# API响应示例（用于文档）
RESPONSE_EXAMPLES = {
    "success": {
        "description": "成功响应示例",
        "content": {
            "application/json": {
                "example": {
                    "status": "success",
                    "message": "操作成功",
                    "data": {}
                }
            }
        }
    },
    "error": {
        "description": "错误响应示例",
        "content": {
            "application/json": {
                "example": {
                    "status": "error",
                    "message": "操作失败",
                    "error_code": "ERROR_CODE",
                    "error_details": {}
                }
            }
        }
    }
}

__all__ = [
    "upload",
    "mining",
    "identification",
    "fusion",
    "editing",
    "design",
    "visualization",
    "pipeline",
    "ROUTE_CONFIG",
    "RESPONSE_EXAMPLES"
]