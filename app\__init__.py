"""
材料基因算法在线系统 - 主应用包

这是一个基于人工智能的材料发现与设计系统，采用"材料基因"概念，
从材料的晶体结构中提取决定其性能的关键特征信息。

主要模块：
- api: RESTful API接口
- core: 核心算法实现
- models: 数据模型定义
- services: 业务服务层
- utils: 工具函数库
"""

__version__ = "1.0.0"
__author__ = "HIT Material Gene Team"



# 版本信息
VERSION_INFO = {
    "major": 1,
    "minor": 0,
    "patch": 0,
    "release": "stable",
    "build": "20241220"
}

# 系统元数据
METADATA = {
    "name": "Material Gene Algorithm API",
    "description": "AI-powered material discovery and design system",
    "url": "https://github.com/material-gene/api",
    "keywords": ["material science", "AI", "gene algorithm", "crystal structure"],
    "classifiers": [
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Science/Research",
        "Topic :: Scientific/Engineering :: Chemistry",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ]
}

# 导出版本信息
__all__ = [
    "__version__",
    "__author__",
    "VERSION_INFO",
    "METADATA"
]