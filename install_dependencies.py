#!/usr/bin/env python3
"""
材料基因算法系统依赖安装脚本
自动检测环境并安装所需依赖包
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def run_command(command, description=""):
    """执行命令并处理错误"""
    print(f"🔄 {description}")
    print(f"执行命令: {command}")

    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        print(f"✅ {description} - 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - 失败")
        print(f"错误信息: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")

    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False

    print("✅ Python版本符合要求")
    return True

def install_basic_packages():
    """安装基础包"""
    basic_packages = [
        "pip>=23.0",
        "setuptools>=65.0",
        "wheel>=0.38.0"
    ]

    for package in basic_packages:
        if not run_command(f"pip install --upgrade {package}", f"安装 {package}"):
            return False

    return True

def install_pytorch():
    """安装PyTorch（根据系统自动选择版本）"""
    system = platform.system().lower()

    if system == "windows":
        # Windows版本
        command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    elif system == "darwin":  # macOS
        command = "pip install torch torchvision torchaudio"
    else:  # Linux
        command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"

    return run_command(command, "安装PyTorch")

def install_requirements():
    """安装requirements.txt中的依赖"""
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt文件不存在")
        return False

    return run_command("pip install -r requirements.txt", "安装项目依赖")

def install_optional_packages():
    """安装可选包（如果失败不影响主要功能）"""
    optional_packages = [
        ("dgl", "图神经网络库"),
        ("pymatgen", "材料科学计算库"),
        ("redis", "Redis客户端"),
        ("python-magic", "文件类型检测")
    ]

    for package, description in optional_packages:
        run_command(f"pip install {package}", f"安装可选包: {description}")

def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "uploads",
        "results",
        "cache",
        "config"
    ]

    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 创建目录: {directory}")

def create_env_file():
    """创建环境配置文件"""
    env_content = """# 材料基因算法系统环境配置

# 应用配置
DEBUG=false
HOST=0.0.0.0
PORT=8000
WORKERS=1

# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///./app.db

# Redis配置（可选）
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 文件上传配置
MAX_FILE_SIZE=104857600  # 100MB
UPLOAD_DIR=uploads
RESULTS_DIR=results

# 缓存配置
CACHE_SIZE=1000
CACHE_TTL=3600

# 安全配置
SECRET_KEY=your-secret-key-here
"""

    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)

    print("📝 创建环境配置文件: .env")

def main():
    """主安装流程"""
    print("🚀 开始安装材料基因算法系统依赖")
    print("=" * 50)

    # 检查Python版本
    if not check_python_version():
        sys.exit(1)

    # 安装基础包
    print("\n📦 安装基础包...")
    if not install_basic_packages():
        print("❌ 基础包安装失败")
        sys.exit(1)

    # 安装PyTorch
    print("\n🔥 安装PyTorch...")
    if not install_pytorch():
        print("⚠️ PyTorch安装失败，请手动安装")

    # 安装项目依赖
    print("\n📋 安装项目依赖...")
    if not install_requirements():
        print("❌ 项目依赖安装失败")
        sys.exit(1)

    # 安装可选包
    print("\n🎁 安装可选包...")
    install_optional_packages()

    # 创建目录
    print("\n📁 创建项目目录...")
    create_directories()

    # 创建环境文件
    print("\n⚙️ 创建配置文件...")
    create_env_file()

    print("\n" + "=" * 50)
    print("🎉 安装完成！")
    print("\n下一步操作:")
    print("1. 编辑 .env 文件配置参数")
    print("2. 运行: python -m uvicorn app.main:app --reload")
    print("3. 访问: http://localhost:8000")

if __name__ == "__main__":
    main()