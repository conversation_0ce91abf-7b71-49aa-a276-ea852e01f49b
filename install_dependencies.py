#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Material Genome Algorithm System - Dependency Installation Script
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

# Windows编码处理
def safe_print(text):
    """安全的打印函数，避免编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 移除特殊字符，只保留ASCII和中文
        safe_text = ''.join(c for c in text if ord(c) < 128 or '\u4e00' <= c <= '\u9fff')
        print(safe_text)
    except Exception:
        # 最后的备选方案
        print(text.encode('ascii', 'ignore').decode('ascii'))

def run_command(command, description=""):
    """执行命令并处理错误"""
    safe_print(f"[INFO] {description}")
    safe_print(f"执行命令: {command}")

    try:
        # Windows系统编码处理
        if platform.system().lower() == "windows":
            result = subprocess.run(
                command,
                shell=True,
                check=True,
                capture_output=True,
                text=True,
                encoding='gbk',
                errors='ignore'
            )
        else:
            result = subprocess.run(
                command,
                shell=True,
                check=True,
                capture_output=True,
                text=True
            )
        safe_print(f"[OK] {description} - 成功")
        return True
    except subprocess.CalledProcessError as e:
        safe_print(f"[ERROR] {description} - 失败")
        error_msg = e.stderr if e.stderr else str(e)
        safe_print(f"错误信息: {error_msg}")
        return False
    except Exception as e:
        safe_print(f"[ERROR] {description} - 失败")
        safe_print(f"错误信息: {str(e)}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")

    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False

    print("✅ Python版本符合要求")
    return True

def install_basic_packages():
    """安装基础包"""
    basic_packages = [
        ("pip", "23.0"),
        ("setuptools", "65.0"),
        ("wheel", "0.38.0")
    ]

    for package_name, min_version in basic_packages:
        command = f"pip install --upgrade \"{package_name}>={min_version}\""
        if not run_command(command, f"安装 {package_name}>={min_version}"):
            # 如果指定版本失败，尝试安装最新版本
            command_fallback = f"pip install --upgrade {package_name}"
            if not run_command(command_fallback, f"安装 {package_name} (最新版本)"):
                print(f"⚠️ {package_name} 安装失败，但可能不影响主要功能")

    return True

def install_pytorch():
    """安装PyTorch（根据系统自动选择版本）"""
    system = platform.system().lower()

    if system == "windows":
        # Windows版本
        command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    elif system == "darwin":  # macOS
        command = "pip install torch torchvision torchaudio"
    else:  # Linux
        command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"

    return run_command(command, "安装PyTorch")

def install_requirements():
    """安装requirements.txt中的依赖"""
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt文件不存在")
        return False

    return run_command("pip install -r requirements.txt", "安装项目依赖")

def install_optional_packages():
    """安装可选包（如果失败不影响主要功能）"""
    optional_packages = [
        ("dgl", "图神经网络库"),
        ("pymatgen", "材料科学计算库"),
        ("redis", "Redis客户端"),
        ("python-magic", "文件类型检测")
    ]

    for package, description in optional_packages:
        run_command(f"pip install {package}", f"安装可选包: {description}")

def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "uploads",
        "results",
        "cache",
        "config"
    ]

    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 创建目录: {directory}")

def create_env_file():
    """创建环境配置文件"""
    env_content = """# 材料基因算法系统环境配置

# 应用配置
DEBUG=false
HOST=0.0.0.0
PORT=8000
WORKERS=1

# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///./app.db

# Redis配置（可选）
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 文件上传配置
MAX_FILE_SIZE=104857600  # 100MB
UPLOAD_DIR=uploads
RESULTS_DIR=results

# 缓存配置
CACHE_SIZE=1000
CACHE_TTL=3600

# 安全配置
SECRET_KEY=your-secret-key-here
"""

    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)

    print("📝 创建环境配置文件: .env")

def main():
    """主安装流程"""
    try:
        print("开始安装材料基因算法系统依赖")
        print("=" * 50)

        # 检查Python版本
        if not check_python_version():
            input("按回车键退出...")
            sys.exit(1)

        # 安装基础包
        print("\n[STEP 1] 安装基础包...")
        install_basic_packages()  # 不强制要求成功

        # 安装项目依赖
        print("\n[STEP 2] 安装项目依赖...")
        if not install_requirements():
            print("[ERROR] 项目依赖安装失败")
            input("按回车键退出...")
            sys.exit(1)

        # 安装可选包
        print("\n[STEP 3] 安装可选包...")
        install_optional_packages()

        # 创建目录
        print("\n[STEP 4] 创建项目目录...")
        create_directories()

        # 创建环境文件
        print("\n[STEP 5] 创建配置文件...")
        create_env_file()

        print("\n" + "=" * 50)
        print("[SUCCESS] 安装完成！")
        print("\n下一步操作:")
        print("1. 编辑 .env 文件配置参数")
        print("2. 运行: python -m uvicorn app.main:app --reload")
        print("3. 访问: http://localhost:8000")

    except KeyboardInterrupt:
        print("\n[INFO] 用户取消安装")
    except Exception as e:
        print(f"\n[ERROR] 安装过程中出现错误: {e}")
        input("按回车键退出...")
        sys.exit(1)

    input("\n按回车键退出...")

if __name__ == "__main__":
    main()